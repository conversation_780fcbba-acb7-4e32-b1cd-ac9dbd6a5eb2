"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(main)/jobs/[id]/page",{

/***/ "(app-pages-browser)/./lib/integration-client.js":
/*!***********************************!*\
  !*** ./lib/integration-client.js ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   IntegrationClient: () => (/* binding */ IntegrationClient),\n/* harmony export */   integrationClient: () => (/* binding */ integrationClient)\n/* harmony export */ });\n/* harmony import */ var _shared_integration_config_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../shared/integration-config.js */ \"(app-pages-browser)/../shared/integration-config.js\");\n/* harmony import */ var _services_supabaseClient_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../services/supabaseClient.js */ \"(app-pages-browser)/./services/supabaseClient.js\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n// Integration Client for Voice Agent Communication\n// This utility handles communication between the main platform and voice agent\n\n\nclass IntegrationClient {\n    // Create headers for API requests\n    createHeaders() {\n        return {\n            'Content-Type': 'application/json',\n            [_shared_integration_config_js__WEBPACK_IMPORTED_MODULE_0__.INTEGRATION_CONFIG.API_KEY_HEADER]: this.apiKey\n        };\n    }\n    // Make API request with retry logic\n    async makeRequest(url) {\n        let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n        const requestOptions = {\n            ...options,\n            headers: {\n                ...this.createHeaders(),\n                ...options.headers\n            },\n            signal: AbortSignal.timeout(_shared_integration_config_js__WEBPACK_IMPORTED_MODULE_0__.INTEGRATION_CONFIG.REQUEST_TIMEOUT)\n        };\n        for(let attempt = 0; attempt <= this.maxRetries; attempt++){\n            try {\n                const response = await fetch(url, requestOptions);\n                if (response.ok) {\n                    return await response.json();\n                }\n                // If it's a client error (4xx), don't retry\n                if (response.status >= 400 && response.status < 500) {\n                    var _errorData_error;\n                    const errorData = await response.json().catch(()=>({}));\n                    throw new Error(\"Client error \".concat(response.status, \": \").concat(((_errorData_error = errorData.error) === null || _errorData_error === void 0 ? void 0 : _errorData_error.message) || 'Unknown error'));\n                }\n                // For server errors (5xx), retry\n                if (attempt < this.maxRetries) {\n                    await this.delay(_shared_integration_config_js__WEBPACK_IMPORTED_MODULE_0__.INTEGRATION_CONFIG.RETRY_DELAY * (attempt + 1));\n                    continue;\n                }\n                throw new Error(\"Server error \".concat(response.status, \" after \").concat(this.maxRetries, \" retries\"));\n            } catch (error) {\n                var _error_name;\n                if (attempt < this.maxRetries && !((_error_name = error.name) === null || _error_name === void 0 ? void 0 : _error_name.includes('AbortError'))) {\n                    await this.delay(_shared_integration_config_js__WEBPACK_IMPORTED_MODULE_0__.INTEGRATION_CONFIG.RETRY_DELAY * (attempt + 1));\n                    continue;\n                }\n                throw error;\n            }\n        }\n    }\n    // Delay utility for retries\n    delay(ms) {\n        return new Promise((resolve)=>setTimeout(resolve, ms));\n    }\n    // Create interview session in voice agent\n    async createInterviewSession(sessionData) {\n        try {\n            (0,_shared_integration_config_js__WEBPACK_IMPORTED_MODULE_0__.logIntegrationEvent)('info', 'main_platform', 'creating_voice_agent_session', 'Creating interview session in voice agent', sessionData.sessionId);\n            const url = \"\".concat(_shared_integration_config_js__WEBPACK_IMPORTED_MODULE_0__.INTEGRATION_CONFIG.VOICE_AGENT_URL, \"/api/integration/interview/start\");\n            const response = await this.makeRequest(url, {\n                method: 'POST',\n                body: JSON.stringify(sessionData)\n            });\n            if (!response.success) {\n                var _response_error;\n                throw new Error(((_response_error = response.error) === null || _response_error === void 0 ? void 0 : _response_error.message) || 'Failed to create session in voice agent');\n            }\n            (0,_shared_integration_config_js__WEBPACK_IMPORTED_MODULE_0__.logIntegrationEvent)('info', 'main_platform', 'voice_agent_session_created', 'Interview session created in voice agent', sessionData.sessionId, {\n                voiceAgentInterviewId: response.data.voiceAgentInterviewId\n            });\n            return response.data;\n        } catch (error) {\n            (0,_shared_integration_config_js__WEBPACK_IMPORTED_MODULE_0__.logIntegrationEvent)('error', 'main_platform', 'voice_agent_session_failed', 'Failed to create session in voice agent', sessionData.sessionId, {\n                error: error.message\n            });\n            throw error;\n        }\n    }\n    // Get interview status from voice agent\n    async getInterviewStatus(sessionId) {\n        let voiceAgentInterviewId = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : null;\n        try {\n            const params = new URLSearchParams();\n            if (sessionId) params.append('sessionId', sessionId);\n            if (voiceAgentInterviewId) params.append('voiceAgentInterviewId', voiceAgentInterviewId);\n            const url = \"\".concat(_shared_integration_config_js__WEBPACK_IMPORTED_MODULE_0__.INTEGRATION_CONFIG.VOICE_AGENT_URL, \"/api/integration/interview/start?\").concat(params);\n            const response = await this.makeRequest(url, {\n                method: 'GET'\n            });\n            if (!response.success) {\n                var _response_error;\n                throw new Error(((_response_error = response.error) === null || _response_error === void 0 ? void 0 : _response_error.message) || 'Failed to get interview status');\n            }\n            return response.data;\n        } catch (error) {\n            (0,_shared_integration_config_js__WEBPACK_IMPORTED_MODULE_0__.logIntegrationEvent)('error', 'main_platform', 'get_status_failed', 'Failed to get interview status from voice agent', sessionId, {\n                error: error.message\n            });\n            throw error;\n        }\n    }\n    // Check voice agent health\n    async checkVoiceAgentHealth() {\n        try {\n            const url = \"\".concat(_shared_integration_config_js__WEBPACK_IMPORTED_MODULE_0__.INTEGRATION_CONFIG.VOICE_AGENT_URL, \"/api/integration/health\");\n            const response = await this.makeRequest(url, {\n                method: 'GET'\n            });\n            return response.data;\n        } catch (error) {\n            (0,_shared_integration_config_js__WEBPACK_IMPORTED_MODULE_0__.logIntegrationEvent)('warning', 'main_platform', 'voice_agent_health_check_failed', 'Voice agent health check failed', null, {\n                error: error.message\n            });\n            return {\n                status: 'unhealthy',\n                error: error.message,\n                timestamp: new Date().toISOString()\n            };\n        }\n    }\n    // Generate interview questions\n    async generateQuestions(jobData) {\n        try {\n            (0,_shared_integration_config_js__WEBPACK_IMPORTED_MODULE_0__.logIntegrationEvent)('info', 'main_platform', 'generating_questions', 'Generating interview questions', null, {\n                jobTitle: jobData.jobTitle\n            });\n            const response = await fetch('/api/generate-questions', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    jobTitle: jobData.jobTitle,\n                    jobDescription: jobData.jobDescription,\n                    requiredSkills: jobData.requiredSkills,\n                    experienceLevel: jobData.experienceLevel,\n                    questionCount: jobData.questionCount || _shared_integration_config_js__WEBPACK_IMPORTED_MODULE_0__.INTEGRATION_CONFIG.DEFAULT_QUESTION_COUNT\n                })\n            });\n            const questionsData = await response.json();\n            if (!response.ok) {\n                throw new Error(questionsData.error || 'Failed to generate questions');\n            }\n            // Format questions for voice agent\n            const formattedQuestions = (questionsData.questions || []).map((q, index)=>({\n                    id: \"q_\".concat(index + 1),\n                    question: q,\n                    type: 'technical',\n                    difficulty: 'medium',\n                    expectedDuration: 120,\n                    followUpAllowed: true,\n                    metadata: {\n                        generated: true,\n                        jobTitle: jobData.jobTitle\n                    }\n                }));\n            (0,_shared_integration_config_js__WEBPACK_IMPORTED_MODULE_0__.logIntegrationEvent)('info', 'main_platform', 'questions_generated', 'Interview questions generated successfully', null, {\n                questionCount: formattedQuestions.length\n            });\n            return formattedQuestions;\n        } catch (error) {\n            (0,_shared_integration_config_js__WEBPACK_IMPORTED_MODULE_0__.logIntegrationEvent)('error', 'main_platform', 'question_generation_failed', 'Failed to generate interview questions', null, {\n                error: error.message\n            });\n            // Return default questions as fallback\n            return [\n                {\n                    id: 'q_default_1',\n                    question: 'Tell me about yourself and your experience.',\n                    type: 'behavioral',\n                    difficulty: 'easy',\n                    expectedDuration: 120,\n                    followUpAllowed: true,\n                    metadata: {\n                        fallback: true\n                    }\n                },\n                {\n                    id: 'q_default_2',\n                    question: \"What interests you about the \".concat(jobData.jobTitle, \" position?\"),\n                    type: 'behavioral',\n                    difficulty: 'easy',\n                    expectedDuration: 120,\n                    followUpAllowed: true,\n                    metadata: {\n                        fallback: true\n                    }\n                }\n            ];\n        }\n    }\n    // Start interview process (creates session and redirects to voice agent)\n    async startInterview(interviewData) {\n        try {\n            const sessionId = \"session_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substr(2, 9));\n            (0,_shared_integration_config_js__WEBPACK_IMPORTED_MODULE_0__.logIntegrationEvent)('info', 'main_platform', 'starting_interview_process', 'Starting integrated interview process', sessionId, {\n                userEmail: interviewData.userEmail,\n                jobTitle: interviewData.jobTitle\n            });\n            // Generate questions if not provided\n            let questions = interviewData.questions;\n            if (!questions || questions.length === 0) {\n                questions = await this.generateQuestions({\n                    jobTitle: interviewData.jobTitle,\n                    jobDescription: interviewData.jobDescription,\n                    requiredSkills: interviewData.requiredSkills,\n                    experienceLevel: interviewData.experienceLevel,\n                    questionCount: interviewData.questionCount\n                });\n            }\n            // Prepare session data for voice agent\n            const sessionData = {\n                sessionId,\n                userEmail: interviewData.userEmail,\n                userName: interviewData.userName,\n                jobTitle: interviewData.jobTitle,\n                jobDescription: interviewData.jobDescription,\n                companyName: interviewData.companyName,\n                interviewType: interviewData.interviewType || 'job_application',\n                duration: interviewData.duration || _shared_integration_config_js__WEBPACK_IMPORTED_MODULE_0__.INTEGRATION_CONFIG.DEFAULT_INTERVIEW_DURATION,\n                experienceLevel: interviewData.experienceLevel,\n                requiredSkills: interviewData.requiredSkills,\n                companyCriteria: interviewData.companyCriteria,\n                questions,\n                questionCount: questions.length,\n                callbackUrl: \"\".concat(_shared_integration_config_js__WEBPACK_IMPORTED_MODULE_0__.INTEGRATION_CONFIG.MAIN_PLATFORM_URL, \"/api/integration/interview/feedback\"),\n                webhookSecret: process.env.INTEGRATION_SHARED_SECRET || 'webhook-secret'\n            };\n            // Create session in main platform database\n            const { createClient } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @supabase/supabase-js */ \"(app-pages-browser)/../node_modules/@supabase/supabase-js/dist/module/index.js\"));\n            const supabase = createClient(\"https://aimmcbxnlckivomgsdnt.supabase.co\", process.env.SUPABASE_SERVICE_ROLE_KEY);\n            const { data: session, error: sessionError } = await supabase.from('Interview_Sessions').insert([\n                {\n                    main_interview_id: sessionId,\n                    job_id: interviewData.jobId || null,\n                    company_id: interviewData.companyId || null,\n                    user_email: interviewData.userEmail,\n                    user_name: interviewData.userName,\n                    job_title: interviewData.jobTitle,\n                    job_description: interviewData.jobDescription,\n                    required_skills: interviewData.requiredSkills,\n                    experience_level: interviewData.experienceLevel,\n                    company_criteria: interviewData.companyCriteria,\n                    interview_type: sessionData.interviewType,\n                    duration: sessionData.duration,\n                    question_count: questions.length,\n                    questions: questions,\n                    status: 'pending',\n                    voice_agent_url: \"\".concat(_shared_integration_config_js__WEBPACK_IMPORTED_MODULE_0__.INTEGRATION_CONFIG.VOICE_AGENT_URL, \"/interview/external?sessionId=\").concat(sessionId),\n                    integration_version: _shared_integration_config_js__WEBPACK_IMPORTED_MODULE_0__.INTEGRATION_CONFIG.INTEGRATION_VERSION\n                }\n            ]).select().single();\n            if (sessionError) {\n                throw new Error(\"Failed to create session: \".concat(sessionError.message));\n            }\n            // Create session in voice agent\n            const voiceAgentResponse = await this.createInterviewSession(sessionData);\n            // Update session with voice agent details\n            await supabase.from('Interview_Sessions').update({\n                voice_agent_interview_id: voiceAgentResponse.voiceAgentInterviewId,\n                status: 'ready'\n            }).eq('id', session.id);\n            (0,_shared_integration_config_js__WEBPACK_IMPORTED_MODULE_0__.logIntegrationEvent)('info', 'main_platform', 'interview_process_ready', 'Interview process ready, redirecting to voice agent', sessionId, {\n                voiceAgentUrl: session.voice_agent_url\n            });\n            return {\n                sessionId,\n                voiceAgentUrl: session.voice_agent_url,\n                voiceAgentInterviewId: voiceAgentResponse.voiceAgentInterviewId,\n                estimatedDuration: sessionData.duration,\n                questionCount: questions.length\n            };\n        } catch (error) {\n            (0,_shared_integration_config_js__WEBPACK_IMPORTED_MODULE_0__.logIntegrationEvent)('error', 'main_platform', 'interview_start_failed', 'Failed to start interview process', null, {\n                error: error.message\n            });\n            throw error;\n        }\n    }\n    // Get feedback status\n    async getFeedbackStatus(sessionId) {\n        try {\n            const { createClient } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @supabase/supabase-js */ \"(app-pages-browser)/../node_modules/@supabase/supabase-js/dist/module/index.js\"));\n            const supabase = createClient(\"https://aimmcbxnlckivomgsdnt.supabase.co\", process.env.SUPABASE_SERVICE_ROLE_KEY);\n            const { data: feedback, error } = await supabase.from('Interview_Feedback_Bridge').select('*').eq('main_interview_id', sessionId).single();\n            if (error && error.code !== 'PGRST116') {\n                throw new Error(\"Database error: \".concat(error.message));\n            }\n            return feedback;\n        } catch (error) {\n            (0,_shared_integration_config_js__WEBPACK_IMPORTED_MODULE_0__.logIntegrationEvent)('error', 'main_platform', 'get_feedback_status_failed', 'Failed to get feedback status', sessionId, {\n                error: error.message\n            });\n            throw error;\n        }\n    }\n    constructor(){\n        this.apiKey = process.env.INTEGRATION_API_KEY || 'main-platform-api-key';\n        this.retryCount = 0;\n        this.maxRetries = _shared_integration_config_js__WEBPACK_IMPORTED_MODULE_0__.INTEGRATION_CONFIG.MAX_RETRIES;\n    }\n}\n// Export singleton instance\nconst integrationClient = new IntegrationClient();\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/integration-client.js\n"));

/***/ })

});