"use client"
import { UserDetailContext } from '@/context/UserDetailContext';
import { supabase } from '@/services/supabaseClient'
import React, { useContext, useEffect, useState } from 'react'
import { useIsClient } from '@/hooks/useHydrationFix';
import { useLocalStorage } from '@/hooks/useLocalStorage';

function Provider({ children }) {
    const [user, setUser] = useState();
    const [company, setCompany] = useState();
    const isClient = useIsClient();
    const [userType, setUserType] = useLocalStorage('userType', 'client');

    useEffect(() => {
        // This effect should only run once on component mount and only on client
        if (!isClient) return;

        // Set up auth state change listener
        const { data: authListener } = supabase.auth.onAuthStateChange(
            async (event, session) => {
                console.log("Auth state changed:", event);
                if (event === 'SIGNED_OUT') {
                    setUser(null);
                    setCompany(null);
                    setUserType('client'); // Reset to default
                    if (isClient) {
                        window.location.href = '/';
                    }
                }
            }
        );

        // Clean up the listener
        return () => {
            if (authListener && authListener.subscription) {
                authListener.subscription.unsubscribe();
            }
        };
    }, [isClient]);

    // Separate effect for checking user type and authentication
    useEffect(() => {
        const checkUserAndType = async () => {
            if (!isClient) return;
            try {

                // Get current auth session
                const { data: { session } } = await supabase.auth.getSession();

                if (!session) {
                    // Not authenticated, don't do anything else
                    return;
                }

                const { data: { user } } = await supabase.auth.getUser();
                if (!user) return;

                console.log("User is authenticated:", user.email);
                console.log("Stored user type:", storedUserType);

                // User type is already managed by the useLocalStorage hook

                // Check if user exists in the appropriate table based on userType
                if (storedUserType === "company") {
                    console.log("Checking for company account with email:", user.email);

                    try {
                        // Check if company exists - handle multiple rows case
                        const { data: companyData, error } = await supabase
                            .from('Companies')
                            .select('*')
                            .eq('email', user.email)
                            .limit(1);

                        if (error) {
                            console.error("Error fetching company:", error);
                            console.error("Full error details:", JSON.stringify(error, null, 2));

                            // If table doesn't exist (42P01) or permission denied (42501), set temp company
                            if (error.code === '42P01' || error.code === '42501') {
                                console.log("Companies table not accessible, setting temporary company");
                                setCompany({
                                    id: 'temp-id',
                                    email: user.email,
                                    name: user.user_metadata?.name || 'Company Account',
                                    is_onboarded: false,
                                    industry_type: 'Technology'
                                });
                                return;
                            }

                            // Continue with company creation for other errors
                            console.log("Will attempt to create company despite fetch error");

                            // Create a new company record if fetch failed
                            const { data: newCompany, error: insertError } = await supabase
                                .from('Companies')
                                .insert([
                                    {
                                        email: user.email,
                                        name: user.user_metadata?.name || 'Company Account',
                                        picture: user.user_metadata?.picture || '',
                                        is_onboarded: false,
                                        industry_type: 'Technology',
                                        created_at: new Date().toISOString()
                                    }
                                ])
                                .select();

                            if (insertError) {
                                console.error("Error creating company after fetch error:", insertError);
                                // Set a minimal company object to prevent null references
                                setCompany({
                                    id: 'temp-id',
                                    email: user.email,
                                    name: user.user_metadata?.name || 'Company Account',
                                    is_onboarded: false
                                });
                                return;
                            }

                            if (newCompany && newCompany.length > 0) {
                                console.log("New company created after fetch error:", newCompany[0]);
                                setCompany(newCompany[0]);
                            }
                            return;
                        }

                        if (companyData && companyData.length > 0) {
                            console.log("Found existing company:", companyData[0]);
                            setCompany(companyData[0]);

                            // Auto-redirect to company dashboard if not already there
                            if (isClient &&
                                !window.location.pathname.includes('/company') &&
                                !window.location.pathname.includes('/auth')) {
                                console.log("Redirecting to company dashboard...");
                                window.location.href = '/company/dashboard';
                            }
                        } else {
                            console.log("No company found, creating new company for:", user.email);
                            // Create a new company record if it doesn't exist
                            const { data: newCompany, error: insertError } = await supabase
                                .from('Companies')
                                .insert([
                                    {
                                        email: user.email,
                                        name: user.user_metadata?.name || 'Company Account',
                                        picture: user.user_metadata?.picture || '',
                                        is_onboarded: false,
                                        industry_type: 'Technology',
                                        created_at: new Date().toISOString()
                                    }
                                ])
                                .select();

                            if (insertError) {
                                console.error("Error creating company:", insertError);
                                console.error("Full insert error details:", JSON.stringify(insertError, null, 2));
                                // Set a minimal company object to prevent null references
                                setCompany({
                                    id: 'temp-id',
                                    email: user.email,
                                    name: user.user_metadata?.name || 'Company Account',
                                    is_onboarded: false
                                });
                                return;
                            }

                            if (newCompany && newCompany.length > 0) {
                                console.log("New company created:", newCompany[0]);
                                setCompany(newCompany[0]);

                                // Auto-redirect to company dashboard for new company
                                if (isClient &&
                                    !window.location.pathname.includes('/company') &&
                                    !window.location.pathname.includes('/auth')) {
                                    console.log("Redirecting new company to dashboard...");
                                    setTimeout(() => {
                                        window.location.href = '/company/dashboard';
                                    }, 1000); // Small delay to ensure state is set
                                }
                            } else {
                                console.error("No company data returned after insert");
                                // Set a minimal company object to prevent null references
                                setCompany({
                                    id: 'temp-id',
                                    email: user.email,
                                    name: user.user_metadata?.name || 'Company Account',
                                    is_onboarded: false
                                });
                            }
                        }
                    } catch (companyError) {
                        console.error("Exception in company handling:", companyError);
                        // Set a minimal company object to prevent null references
                        setCompany({
                            id: 'temp-id',
                            email: user.email,
                            name: user.user_metadata?.name || 'Company Account',
                            is_onboarded: false
                        });
                    }
                } else {
                    // Default to client user type (handled by useLocalStorage hook)
                    if (userType !== 'client') {
                        setUserType('client');
                    }

                    // Check if user exists in Users table
                    const { data: userData, error } = await supabase
                        .from('Users')
                        .select('*')
                        .eq('email', user.email)
                        .maybeSingle();

                    if (error) {
                        console.error("Error fetching user:", error);
                        return;
                    }

                    if (userData) {
                        console.log("Found existing user:", userData);
                        setUser(userData);

                        // Auto-redirect to user dashboard if not already there
                        if (typeof window !== 'undefined' &&
                            !window.location.pathname.includes('/dashboard') &&
                            !window.location.pathname.includes('/auth') &&
                            !window.location.pathname.includes('/jobs')) {
                            console.log("Redirecting to user dashboard...");
                            window.location.href = '/dashboard';
                        }
                    } else {
                        console.log("Creating new user for:", user.email);
                        // Create a new user record if it doesn't exist
                        const { data: newUser, error: insertError } = await supabase
                            .from('Users')
                            .insert([
                                {
                                    name: user.user_metadata?.name || user.email,
                                    email: user.email,
                                    picture: user.user_metadata?.picture || '',
                                    credits: 10
                                }
                            ])
                            .select();

                        if (insertError) {
                            console.error("Error creating user:", insertError);
                            return;
                        }

                        if (newUser && newUser.length > 0) {
                            console.log("New user created:", newUser[0]);
                            setUser(newUser[0]);

                            // Auto-redirect to user dashboard for new user
                            if (typeof window !== 'undefined' &&
                                !window.location.pathname.includes('/dashboard') &&
                                !window.location.pathname.includes('/auth')) {
                                console.log("Redirecting new user to dashboard...");
                                setTimeout(() => {
                                    window.location.href = '/dashboard';
                                }, 1000); // Small delay to ensure state is set
                            }
                        }
                    }
                }
            } catch (error) {
                console.error("Error in checkUserAndType:", error);
            }
        }
    };

    checkUserAndType();
}, [userType, isClient]);

return (
    <UserDetailContext.Provider value={{
        user,
        setUser,
        userType,
        setUserType,
        company,
        setCompany,
        isCompany: userType === "company"
    }}>
        <div>{children}</div>
    </UserDetailContext.Provider>
)
}

export default Provider

export const useUser = () => {
    const context = useContext(UserDetailContext);
    return context;
}