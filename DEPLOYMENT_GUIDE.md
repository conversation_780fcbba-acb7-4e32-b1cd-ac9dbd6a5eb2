# 🚀 Voice Agent Integration - Deployment Guide

This guide walks you through deploying and testing the complete integrated system.

## 📋 Pre-Deployment Checklist

### ✅ System Requirements
- [ ] Node.js 18+ installed
- [ ] Supabase project set up
- [ ] OpenRouter API key (for main platform)
- [ ] Google Gemini API key (for voice agent)
- [ ] Both project folders accessible

### ✅ Database Setup
- [ ] Supabase project created
- [ ] Integration schema deployed
- [ ] API keys configured
- [ ] Test data created

## 🔧 Step-by-Step Deployment

### Step 1: Database Configuration

1. **Run Integration Schema**
```sql
-- In Supabase SQL Editor, run these files in order:
-- 1. integration_schema.sql
-- 2. setup_integration.sql
```

2. **Verify Tables Created**
```sql
-- Check if integration tables exist
SELECT table_name FROM information_schema.tables 
WHERE table_name IN (
  'Interview_Sessions',
  'Interview_Feedback_Bridge', 
  'Integration_Logs',
  'Integration_API_Keys'
);
```

### Step 2: Environment Configuration

**Main Platform (.env.local):**
```env
# Supabase
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# AI Services
OPENROUTER_API_KEY=your_openrouter_api_key

# Integration
INTEGRATION_API_KEY=main-platform-api-key
VOICE_AGENT_URL=http://localhost:3001
INTEGRATION_SHARED_SECRET=your-secure-shared-secret-123

# Optional
LOG_LEVEL=info
NODE_ENV=development
```

**Voice Agent (.env.local):**
```env
# Supabase
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# AI Services
GOOGLE_GEMINI_API_KEY=your_gemini_api_key

# Integration
INTEGRATION_API_KEY=voice-agent-api-key
MAIN_PLATFORM_URL=http://localhost:3000
NEXT_PUBLIC_INTEGRATION_API_KEY=voice-agent-api-key

# Optional
LOG_LEVEL=info
NODE_ENV=development
```

### Step 3: Install Dependencies

```bash
# Main Platform
cd AI-Interview-Voice-Agent-for-IT-Jobs
npm install

# Voice Agent
cd "kamikaz-interview-ai (1)"
npm install
```

### Step 4: Start Services

```bash
# Terminal 1 - Main Platform (Port 3000)
cd AI-Interview-Voice-Agent-for-IT-Jobs
npm run dev

# Terminal 2 - Voice Agent (Port 3001)
cd "kamikaz-interview-ai (1)"
npm run dev
```

### Step 5: Verify Health

```bash
# Check main platform
curl http://localhost:3000/api/integration/health

# Check voice agent
curl http://localhost:3001/api/integration/health
```

Expected response:
```json
{
  "success": true,
  "data": {
    "status": "healthy",
    "system": "main_platform", // or "voice_agent"
    "services": {
      "database": {"status": "healthy"},
      "ai_service": {"status": "healthy"}
    }
  }
}
```

## 🧪 Testing the Integration

### Automated Testing

```bash
# Run integration test suite
node test_integration.js
```

Expected output:
```
🚀 Starting Integration Tests
============================================================
Main Platform: http://localhost:3000
Voice Agent: http://localhost:3001
============================================================

🧪 Running test: Main Platform Health Check
✅ Main Platform Health Check - PASSED (150ms)

🧪 Running test: Voice Agent Health Check
✅ Voice Agent Health Check - PASSED (120ms)

🧪 Running test: Create Interview Session
✅ Create Interview Session - PASSED (300ms)

🧪 Running test: Start Voice Agent Interview
✅ Start Voice Agent Interview - PASSED (250ms)

🧪 Running test: Submit Interview Feedback
✅ Submit Interview Feedback - PASSED (400ms)

🧪 Running test: Get Session Status
✅ Get Session Status - PASSED (100ms)

============================================================
🔍 INTEGRATION TEST SUMMARY
============================================================
Total Tests: 6
Passed: 6
Failed: 0
Total Time: 1320ms
============================================================

🎉 All tests passed!
```

### Manual Testing

1. **Test Job Application Flow**
   - Go to http://localhost:3000
   - Sign in/register
   - Browse jobs
   - Apply for a job
   - Verify redirect to voice agent

2. **Test Voice Interview**
   - Complete the voice interview
   - Check conversation recording
   - Verify feedback submission

3. **Test Feedback Flow**
   - Check interview status page
   - Verify feedback in company dashboard
   - Check database records

### Database Verification

```sql
-- Check recent sessions
SELECT 
  main_interview_id,
  user_email,
  job_title,
  status,
  created_at
FROM "Interview_Sessions" 
ORDER BY created_at DESC 
LIMIT 5;

-- Check feedback data
SELECT 
  main_interview_id,
  overall_score,
  recommendation,
  feedback_status,
  created_at
FROM "Interview_Feedback_Bridge" 
ORDER BY created_at DESC 
LIMIT 5;

-- Check integration logs
SELECT 
  log_level,
  component,
  action,
  message,
  created_at
FROM "Integration_Logs" 
ORDER BY created_at DESC 
LIMIT 10;
```

## 🐛 Troubleshooting

### Common Issues

1. **"API key validation failed"**
   ```bash
   # Check environment variables
   echo $INTEGRATION_API_KEY
   
   # Verify API keys in database
   SELECT key_name, system_name, is_active 
   FROM "Integration_API_Keys";
   ```

2. **"Service unavailable"**
   ```bash
   # Check if services are running
   curl http://localhost:3000/api/integration/health
   curl http://localhost:3001/api/integration/health
   
   # Check logs
   tail -f logs/integration.log
   ```

3. **"Database connection failed"**
   ```bash
   # Test Supabase connection
   node -e "
   const { createClient } = require('@supabase/supabase-js');
   const supabase = createClient(process.env.NEXT_PUBLIC_SUPABASE_URL, process.env.SUPABASE_SERVICE_ROLE_KEY);
   supabase.from('Interview_Sessions').select('count').then(console.log);
   "
   ```

4. **"Questions not generating"**
   ```bash
   # Test OpenRouter API
   curl -X POST http://localhost:3000/api/generate-questions \
     -H "Content-Type: application/json" \
     -d '{"jobTitle":"Software Engineer","questionCount":3}'
   ```

### Debug Mode

Enable detailed logging:
```env
LOG_LEVEL=debug
NODE_ENV=development
```

View debug logs:
```sql
SELECT * FROM "Integration_Logs" 
WHERE log_level = 'debug' 
ORDER BY created_at DESC 
LIMIT 20;
```

## 📊 Monitoring

### Key Metrics to Monitor

1. **Health Status**
   - Both services responding
   - Database connectivity
   - AI service availability

2. **Integration Flow**
   - Session creation rate
   - Interview completion rate
   - Feedback processing time

3. **Error Rates**
   - API authentication failures
   - Database errors
   - Timeout issues

### Monitoring Queries

```sql
-- Session success rate (last 24 hours)
SELECT 
  status,
  COUNT(*) as count,
  ROUND(COUNT(*) * 100.0 / SUM(COUNT(*)) OVER(), 2) as percentage
FROM "Interview_Sessions" 
WHERE created_at > NOW() - INTERVAL '24 hours'
GROUP BY status;

-- Average feedback processing time
SELECT 
  AVG(EXTRACT(EPOCH FROM (processed_at - created_at))) as avg_processing_seconds
FROM "Interview_Feedback_Bridge" 
WHERE processed_at IS NOT NULL;

-- Error rate by component
SELECT 
  component,
  COUNT(*) as error_count
FROM "Integration_Logs" 
WHERE log_level = 'error' 
  AND created_at > NOW() - INTERVAL '1 hour'
GROUP BY component;
```

## 🔄 Production Deployment

### Environment Updates

```env
# Production URLs
MAIN_PLATFORM_URL=https://your-domain.com
VOICE_AGENT_URL=https://voice-agent.your-domain.com

# Security
INTEGRATION_SHARED_SECRET=your-production-secret-key
LOG_LEVEL=info
NODE_ENV=production
```

### Security Checklist

- [ ] Use HTTPS for all communications
- [ ] Rotate API keys regularly
- [ ] Enable rate limiting
- [ ] Set up monitoring alerts
- [ ] Configure backup strategy
- [ ] Test disaster recovery

### Performance Optimization

- [ ] Enable caching
- [ ] Optimize database queries
- [ ] Set up CDN
- [ ] Configure load balancing
- [ ] Monitor resource usage

## ✅ Final Verification

Run this complete test:

```bash
# 1. Health checks
curl http://localhost:3000/api/integration/health
curl http://localhost:3001/api/integration/health

# 2. Integration tests
node test_integration.js

# 3. Manual flow test
# - Apply for job
# - Complete interview
# - Check feedback

# 4. Database verification
# - Check session records
# - Verify feedback data
# - Review logs
```

If all tests pass, your integration is ready! 🎉

## 📞 Support

- **Integration Issues**: Check logs in `Integration_Logs` table
- **API Problems**: Verify environment variables and API keys
- **Database Issues**: Check Supabase connection and permissions
- **Performance**: Monitor resource usage and optimize queries
