services:
  - type: web
    name: kamikaz-interview-ai
    runtime: node
    plan: free
    buildCommand: npm install --legacy-peer-deps && npm run build
    startCommand: npm start
    healthCheckPath: /
    envVars:
      - key: NODE_ENV
        value: production
      - key: NEXT_PUBLIC_SUPABASE_URL
        sync: false
      - key: NEXT_PUBLIC_SUPABASE_ANON_KEY
        sync: false
      - key: GOOGLE_GEMINI_API_KEY
        sync: false
      - key: SUPABASE_SERVICE_ROLE_KEY
        sync: false
      - key: SUPABASE_JWT_SECRET
        sync: false
      - key: XAI_API_KEY
        sync: false
      - key: GROQ_API_KEY
        sync: false
      - key: POSTGRES_URL
        sync: false
      - key: POSTGRES_PRISMA_URL
        sync: false
      - key: POSTGRES_URL_NON_POOLING
        sync: false
    autoDeploy: true
    branch: main
