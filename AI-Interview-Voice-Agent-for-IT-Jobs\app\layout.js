import { Geist, <PERSON><PERSON>st_Mono } from "next/font/google";
import "./globals.css";
import Provider from "./provider";
import { Toaster } from "@/components/ui/sonner";
import DatabaseInitializer from "./_components/DatabaseInitializer";
import ClientBodyWrapper from "./_components/ClientBodyWrapper";
import HydrationProvider from "./_components/HydrationProvider";

// Import development configuration to suppress hydration warnings
if (process.env.NODE_ENV === 'development') {
  import("@/lib/dev-config");
}

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata = {
  title: "AI Interview Schedule Voice Agent",
  description: "Generated by create next app",
};

export default function RootLayout({ children }) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
        suppressHydrationWarning={true}
      >
        <HydrationProvider>
          <ClientBodyWrapper>
            <Provider>
              <DatabaseInitializer />
              {children}
              <Toaster />
            </Provider>
          </ClientBodyWrapper>
        </HydrationProvider>
      </body>
    </html>
  );
}
