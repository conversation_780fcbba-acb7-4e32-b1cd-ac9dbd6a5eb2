// Feedback endpoint for voice agent integration
import { NextResponse } from 'next/server';
import { supabase } from '@/services/supabaseClient';

export async function POST(request) {
  try {
    const feedbackData = await request.json();

    console.log('📝 Received interview feedback from voice agent:', feedbackData);

    // Extract interview ID from sessionId (format: main_INTERVIEW_ID)
    const interviewId = feedbackData.sessionId.replace('main_', '');

    // Save feedback to interview-feedback table
    const feedbackRecord = {
      interview_id: interviewId,
      userName: feedbackData.userName,
      userEmail: feedbackData.userEmail,
      feedback: {
        overallScore: feedbackData.overallScore,
        technicalScore: feedbackData.technicalScore,
        communicationScore: feedbackData.communicationScore,
        problemSolvingScore: feedbackData.problemSolvingScore,
        recommendation: feedbackData.recommendation,
        strengths: feedbackData.strengths,
        areasForImprovement: feedbackData.areasForImprovement,
        overallAssessment: feedbackData.overallAssessment,
        duration: feedbackData.duration,
        questionsAsked: feedbackData.questionsAsked,
        questionsAnswered: feedbackData.questionsAnswered,
        conversationTranscript: feedbackData.conversationTranscript,
        completedAt: feedbackData.completedAt,
        interviewQuality: feedbackData.interviewQuality
      },
      recommended: feedbackData.recommendation === 'hire'
    };

    const { data: feedback, error: feedbackError } = await supabase
      .from('interview-feedback')
      .insert([feedbackRecord])
      .select()
      .single();

    if (feedbackError) {
      console.error('Error saving feedback:', feedbackError);
      throw new Error('Failed to save feedback to database');
    }

    // Update interview status to completed
    const { error: updateError } = await supabase
      .from('Interviews')
      .update({
        status: 'completed',
        completed_at: new Date().toISOString()
      })
      .eq('interview_id', interviewId);

    if (updateError) {
      console.error('Error updating interview status:', updateError);
      // Don't throw error here, feedback is already saved
    }

    console.log('✅ Feedback saved successfully:', feedback);

    return NextResponse.json({
      success: true,
      message: 'Feedback received and saved successfully',
      feedbackId: feedback.id
    });

  } catch (error) {
    console.error('❌ Error processing feedback:', error);

    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Interview feedback endpoint is active',
    timestamp: new Date().toISOString()
  });
}
