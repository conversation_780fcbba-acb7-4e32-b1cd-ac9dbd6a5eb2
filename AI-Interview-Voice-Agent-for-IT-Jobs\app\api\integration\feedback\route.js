// Simple feedback endpoint for voice agent integration
import { NextResponse } from 'next/server';
import { simpleIntegrationClient } from '@/lib/simple-integration-client';

export async function POST(request) {
  try {
    const feedbackData = await request.json();
    
    console.log('📝 Received interview feedback:', feedbackData);
    
    // Process the feedback using the simple integration client
    const result = await simpleIntegrationClient.handleInterviewFeedback(feedbackData);
    
    return NextResponse.json({
      success: true,
      message: 'Feedback received successfully',
      data: result
    });
    
  } catch (error) {
    console.error('❌ Error processing feedback:', error);
    
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Interview feedback endpoint is active',
    timestamp: new Date().toISOString()
  });
}
