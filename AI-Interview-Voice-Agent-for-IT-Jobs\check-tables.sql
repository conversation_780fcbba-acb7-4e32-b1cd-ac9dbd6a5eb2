-- Check what tables exist in the database
-- Run this first to see the actual table names

SELECT 
    table_name,
    table_schema
FROM information_schema.tables 
WHERE table_schema = 'public' 
    AND table_type = 'BASE TABLE'
    AND (
        table_name ILIKE '%company%' OR 
        table_name ILIKE '%companies%' OR
        table_name ILIKE '%user%' OR
        table_name ILIKE '%job%'
    )
ORDER BY table_name;

-- Also check for any tables with different casing
SELECT 
    table_name,
    table_schema
FROM information_schema.tables 
WHERE table_schema = 'public' 
    AND table_type = 'BASE TABLE'
ORDER BY table_name;
