"use client"
import React from 'react'

export const generatePDFContent = (feedback, jobDetails, userName = 'Candidate') => {
    const currentDate = new Date().toLocaleDateString();
    
    return `
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>Interview Feedback Report - ${userName}</title>
        <style>
            body {
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                line-height: 1.6;
                color: #333;
                max-width: 800px;
                margin: 0 auto;
                padding: 20px;
                background: white;
            }
            .header {
                text-align: center;
                border-bottom: 3px solid #3b82f6;
                padding-bottom: 20px;
                margin-bottom: 30px;
            }
            .header h1 {
                color: #1e40af;
                margin: 0;
                font-size: 28px;
            }
            .header p {
                color: #6b7280;
                margin: 5px 0;
            }
            .score-section {
                background: linear-gradient(135deg, #3b82f6, #8b5cf6);
                color: white;
                padding: 20px;
                border-radius: 10px;
                text-align: center;
                margin-bottom: 30px;
            }
            .score-section h2 {
                margin: 0;
                font-size: 24px;
            }
            .overall-score {
                font-size: 48px;
                font-weight: bold;
                margin: 10px 0;
            }
            .section {
                margin-bottom: 30px;
                page-break-inside: avoid;
            }
            .section h3 {
                color: #1e40af;
                border-bottom: 2px solid #e5e7eb;
                padding-bottom: 5px;
                margin-bottom: 15px;
            }
            .ratings-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 15px;
                margin-bottom: 20px;
            }
            .rating-item {
                background: #f8fafc;
                padding: 15px;
                border-radius: 8px;
                border-left: 4px solid #3b82f6;
            }
            .rating-item h4 {
                margin: 0 0 5px 0;
                color: #374151;
            }
            .rating-score {
                font-size: 24px;
                font-weight: bold;
                color: #3b82f6;
            }
            .progress-bar {
                width: 100%;
                height: 8px;
                background: #e5e7eb;
                border-radius: 4px;
                overflow: hidden;
                margin-top: 5px;
            }
            .progress-fill {
                height: 100%;
                background: #3b82f6;
                transition: width 0.3s ease;
            }
            .strengths, .improvements {
                background: #f8fafc;
                padding: 20px;
                border-radius: 8px;
                margin-bottom: 20px;
            }
            .strengths {
                border-left: 4px solid #10b981;
            }
            .improvements {
                border-left: 4px solid #f59e0b;
            }
            .strengths h4 {
                color: #059669;
                margin-top: 0;
            }
            .improvements h4 {
                color: #d97706;
                margin-top: 0;
            }
            .list-item {
                margin-bottom: 10px;
                padding-left: 20px;
                position: relative;
            }
            .list-item::before {
                content: "✓";
                position: absolute;
                left: 0;
                color: #10b981;
                font-weight: bold;
            }
            .improvements .list-item::before {
                content: "→";
                color: #f59e0b;
            }
            .detailed-analysis {
                background: #f8fafc;
                padding: 20px;
                border-radius: 8px;
                margin-bottom: 20px;
            }
            .analysis-item {
                margin-bottom: 15px;
                padding-bottom: 15px;
                border-bottom: 1px solid #e5e7eb;
            }
            .analysis-item:last-child {
                border-bottom: none;
                margin-bottom: 0;
                padding-bottom: 0;
            }
            .analysis-item h5 {
                color: #374151;
                margin: 0 0 8px 0;
                font-size: 16px;
            }
            .next-steps {
                background: #eff6ff;
                padding: 20px;
                border-radius: 8px;
                border-left: 4px solid #3b82f6;
            }
            .next-steps h4 {
                color: #1e40af;
                margin-top: 0;
            }
            .step-item {
                display: flex;
                align-items: flex-start;
                margin-bottom: 10px;
            }
            .step-number {
                background: #3b82f6;
                color: white;
                width: 24px;
                height: 24px;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 12px;
                font-weight: bold;
                margin-right: 10px;
                flex-shrink: 0;
            }
            .footer {
                text-align: center;
                margin-top: 40px;
                padding-top: 20px;
                border-top: 1px solid #e5e7eb;
                color: #6b7280;
                font-size: 14px;
            }
            @media print {
                body { margin: 0; }
                .score-section { background: #3b82f6 !important; }
            }
        </style>
    </head>
    <body>
        <div class="header">
            <h1>Interview Feedback Report</h1>
            <p><strong>Candidate:</strong> ${userName}</p>
            <p><strong>Position:</strong> ${jobDetails?.jobPosition || 'Practice Interview'}</p>
            <p><strong>Date:</strong> ${currentDate}</p>
        </div>

        <div class="score-section">
            <h2>${feedback.recommendation ? 'Recommended for Next Round' : 'Needs Improvement'}</h2>
            <div class="overall-score">${feedback.overallScore}%</div>
            <p>${feedback.recommendationMessage}</p>
        </div>

        <div class="section">
            <h3>Performance Ratings</h3>
            <div class="ratings-grid">
                <div class="rating-item">
                    <h4>Technical Skills</h4>
                    <div class="rating-score">${feedback.ratings?.technicalSkills}/10</div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: ${feedback.ratings?.technicalSkills * 10}%"></div>
                    </div>
                </div>
                <div class="rating-item">
                    <h4>Communication</h4>
                    <div class="rating-score">${feedback.ratings?.communication}/10</div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: ${feedback.ratings?.communication * 10}%"></div>
                    </div>
                </div>
                <div class="rating-item">
                    <h4>Problem Solving</h4>
                    <div class="rating-score">${feedback.ratings?.problemSolving}/10</div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: ${feedback.ratings?.problemSolving * 10}%"></div>
                    </div>
                </div>
                <div class="rating-item">
                    <h4>Experience</h4>
                    <div class="rating-score">${feedback.ratings?.experience}/10</div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: ${feedback.ratings?.experience * 10}%"></div>
                    </div>
                </div>
                <div class="rating-item">
                    <h4>Cultural Fit</h4>
                    <div class="rating-score">${feedback.ratings?.culturalFit}/10</div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: ${feedback.ratings?.culturalFit * 10}%"></div>
                    </div>
                </div>
                <div class="rating-item">
                    <h4>Enthusiasm</h4>
                    <div class="rating-score">${feedback.ratings?.enthusiasm}/10</div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: ${feedback.ratings?.enthusiasm * 10}%"></div>
                    </div>
                </div>
            </div>
        </div>

        <div class="section">
            <div class="strengths">
                <h4>Key Strengths</h4>
                ${feedback.strengths?.map(strength => `<div class="list-item">${strength}</div>`).join('') || '<p>No specific strengths identified.</p>'}
            </div>
        </div>

        <div class="section">
            <div class="improvements">
                <h4>Areas for Improvement</h4>
                ${feedback.areasForImprovement?.map(area => `
                    <div class="list-item">
                        <strong>${area.area}:</strong> ${area.issue}<br>
                        <em>Suggestion: ${area.suggestion}</em>
                    </div>
                `).join('') || '<p>No specific improvements identified.</p>'}
            </div>
        </div>

        <div class="section">
            <h3>Detailed Analysis</h3>
            <div class="detailed-analysis">
                <div class="analysis-item">
                    <h5>Technical Competency</h5>
                    <p>${feedback.detailedAnalysis?.technicalCompetency || 'No detailed analysis available.'}</p>
                </div>
                <div class="analysis-item">
                    <h5>Communication Style</h5>
                    <p>${feedback.detailedAnalysis?.communicationStyle || 'No detailed analysis available.'}</p>
                </div>
                <div class="analysis-item">
                    <h5>Problem-Solving Approach</h5>
                    <p>${feedback.detailedAnalysis?.problemSolvingApproach || 'No detailed analysis available.'}</p>
                </div>
                <div class="analysis-item">
                    <h5>Experience Relevance</h5>
                    <p>${feedback.detailedAnalysis?.experienceRelevance || 'No detailed analysis available.'}</p>
                </div>
            </div>
        </div>

        <div class="section">
            <div class="next-steps">
                <h4>Recommended Next Steps</h4>
                ${feedback.nextSteps?.map((step, index) => `
                    <div class="step-item">
                        <div class="step-number">${index + 1}</div>
                        <div>${step}</div>
                    </div>
                `).join('') || '<p>No specific next steps provided.</p>'}
            </div>
        </div>

        <div class="footer">
            <p>This report was generated by AI Interview Assistant on ${currentDate}</p>
            <p>For questions about this feedback, please contact your interviewer or HR representative.</p>
        </div>
    </body>
    </html>
    `;
};

export const downloadPDF = (feedback, jobDetails, userName = 'Candidate') => {
    const htmlContent = generatePDFContent(feedback, jobDetails, userName);
    
    // Create a new window for printing
    const printWindow = window.open('', '_blank');
    printWindow.document.write(htmlContent);
    printWindow.document.close();
    
    // Wait for content to load, then print
    printWindow.onload = () => {
        printWindow.print();
        // Close the window after printing (optional)
        printWindow.onafterprint = () => {
            printWindow.close();
        };
    };
};
