/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_lib_dev-config_js";
exports.ids = ["_ssr_lib_dev-config_js"];
exports.modules = {

/***/ "(ssr)/./lib/dev-config.js":
/*!***************************!*\
  !*** ./lib/dev-config.js ***!
  \***************************/
/***/ (() => {

eval("// Development Configuration\n// This file contains development-specific configurations to improve the development experience\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/dev-config.js\n");

/***/ })

};
;