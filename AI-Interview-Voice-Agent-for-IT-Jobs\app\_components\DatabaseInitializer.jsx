"use client"

import { useEffect } from 'react';
import { supabase } from '@/services/supabaseClient';

export default function DatabaseInitializer() {
  useEffect(() => {
    const initializeDatabase = async () => {
      console.log('Checking database tables...');

      try {
        // Check if Interviews table exists and has the is_guest column
        const { data: interviewsData, error: interviewsError } = await supabase
          .from('Interviews')
          .select('count(*)')
          .limit(1)
          .single();

        if (interviewsError && interviewsError.code === '42P01') {
          console.log('Interviews table does not exist, creating it...');
          await createInterviewsTable();
        } else {
          console.log('Interviews table exists');
          // Check if is_guest column exists
          await checkAndAddGuestColumn();
        }

        // Check if Companies table exists and create it if needed
        const { data: companiesData, error: companiesError } = await supabase
          .from('Companies')
          .select('count(*)')
          .limit(1)
          .single();

        if (companiesError && companiesError.code === '42P01') {
          console.log('Companies table does not exist, creating it...');
          await createCompaniesTable();
        } else {
          console.log('Companies table exists');
        }

        // Check if Jobs table exists and create it if needed
        const { data: jobsData, error: jobsError } = await supabase
          .from('Jobs')
          .select('count(*)')
          .limit(1)
          .single();

        if (jobsError && jobsError.code === '42P01') {
          console.log('Jobs table does not exist, creating it...');
          await createJobsTable();
        } else {
          console.log('Jobs table exists');
        }

        // Check if Job_Submissions table exists and create it if needed
        const { data: submissionsData, error: submissionsError } = await supabase
          .from('Job_Submissions')
          .select('count(*)')
          .limit(1)
          .single();

        if (submissionsError && submissionsError.code === '42P01') {
          console.log('Job_Submissions table does not exist, creating it...');
          await createJobSubmissionsTable();
        } else {
          console.log('Job_Submissions table exists');
        }

        console.log('Database initialization complete');
      } catch (error) {
        console.error('Error initializing database:', error);
      }
    };
    
    // Create Companies table
    const createCompaniesTable = async () => {
      const { error } = await supabase.rpc('create_companies_table');
      if (error) {
        console.error('Error creating Companies table:', error);
        
        // If RPC fails, try direct SQL (this requires more permissions)
        console.log('Attempting to create Companies table directly...');
        await createCompaniesTableDirect();
      }
    };
    
    // Create Jobs table
    const createJobsTable = async () => {
      const { error } = await supabase.rpc('create_jobs_table');
      if (error) {
        console.error('Error creating Jobs table:', error);
        
        // If RPC fails, try direct SQL (this requires more permissions)
        console.log('Attempting to create Jobs table directly...');
        await createJobsTableDirect();
      }
    };
    
    // Create Job_Submissions table
    const createJobSubmissionsTable = async () => {
      const { error } = await supabase.rpc('create_job_submissions_table');
      if (error) {
        console.error('Error creating Job_Submissions table:', error);
        
        // If RPC fails, try direct SQL (this requires more permissions)
        console.log('Attempting to create Job_Submissions table directly...');
        await createJobSubmissionsTableDirect();
      }
    };
    
    // Direct SQL methods (these require more permissions)
    const createCompaniesTableDirect = async () => {
      // This is a fallback method that requires more permissions
      console.log('Please create the Companies table manually in Supabase');
      alert('Database setup required: Please create the Companies table in Supabase');
    };
    
    const createJobsTableDirect = async () => {
      // This is a fallback method that requires more permissions
      console.log('Please create the Jobs table manually in Supabase');
      alert('Database setup required: Please create the Jobs table in Supabase');
    };
    
    const createJobSubmissionsTableDirect = async () => {
      // This is a fallback method that requires more permissions
      console.log('Please create the Job_Submissions table manually in Supabase');
      alert('Database setup required: Please create the Job_Submissions table in Supabase');
    };
    
    // Check and add guest column to Interviews table
    const checkAndAddGuestColumn = async () => {
      try {
        // Try to select is_guest column to see if it exists
        const { data, error } = await supabase
          .from('Interviews')
          .select('is_guest')
          .limit(1);

        if (error && error.message.includes('column "is_guest" does not exist')) {
          console.log('Adding is_guest column to Interviews table...');
          // This would require admin privileges, so we'll just log it
          console.log('Please run the fix_guest_interviews.sql script in your Supabase dashboard');
        } else {
          console.log('is_guest column exists in Interviews table');
        }
      } catch (err) {
        console.log('Could not check is_guest column:', err.message);
      }
    };

    // Create Interviews table
    const createInterviewsTable = async () => {
      console.log('Please create the Interviews table manually in Supabase using the database_fix.sql script');
    };

    // Run the initialization
    initializeDatabase();
  }, []);

  // This component doesn't render anything
  return null;
}
