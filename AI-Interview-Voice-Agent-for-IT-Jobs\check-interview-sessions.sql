-- =====================================================
-- INTERVIEW SESSIONS TABLE SETUP
-- This table is used by the integration client to store interview session data
-- =====================================================

-- Create the Interview_Sessions table if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_schema = 'public'
        AND table_name = 'Interview_Sessions'
    ) THEN
        CREATE TABLE public.Interview_Sessions (
            id SERIAL PRIMARY KEY,
            main_interview_id VARCHAR(255) UNIQUE NOT NULL,
            job_id INTEGER,
            company_id INTEGER,
            user_email VARCHAR(255) NOT NULL,
            user_name VARCHAR(255),
            job_title VARCHAR(255),
            company_name VARCHAR(255),
            interview_type VARCHAR(50) DEFAULT 'job_application',
            status VARCHAR(50) DEFAULT 'pending',
            session_data JSONB,
            voice_agent_session_id VARCHAR(255),
            feedback_data JSONB,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            completed_at TIMESTAMP WITH TIME ZONE
        );

        RAISE NOTICE 'Created Interview_Sessions table';
    ELSE
        RAISE NOTICE 'Interview_Sessions table already exists';
    END IF;
END $$;

-- Create indexes for better performance
DO $$
BEGIN
    -- Drop existing indexes if they exist
    DROP INDEX IF EXISTS idx_interview_sessions_main_id;
    DROP INDEX IF EXISTS idx_interview_sessions_user_email;
    DROP INDEX IF EXISTS idx_interview_sessions_job_id;
    DROP INDEX IF EXISTS idx_interview_sessions_company_id;
    DROP INDEX IF EXISTS idx_interview_sessions_status;

    -- Create indexes
    CREATE INDEX idx_interview_sessions_main_id ON public.Interview_Sessions(main_interview_id);
    CREATE INDEX idx_interview_sessions_user_email ON public.Interview_Sessions(user_email);
    CREATE INDEX idx_interview_sessions_job_id ON public.Interview_Sessions(job_id);
    CREATE INDEX idx_interview_sessions_company_id ON public.Interview_Sessions(company_id);
    CREATE INDEX idx_interview_sessions_status ON public.Interview_Sessions(status);

    RAISE NOTICE 'Created indexes for Interview_Sessions table';
END $$;

-- Enable RLS (Row Level Security)
ALTER TABLE public.Interview_Sessions ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Users can view their own interview sessions" ON public.Interview_Sessions;
DROP POLICY IF EXISTS "Companies can view sessions for their jobs" ON public.Interview_Sessions;
DROP POLICY IF EXISTS "Service can insert interview sessions" ON public.Interview_Sessions;
DROP POLICY IF EXISTS "Service can update interview sessions" ON public.Interview_Sessions;

-- Create policies for RLS
-- Allow users to see their own interview sessions
CREATE POLICY "Users can view their own interview sessions" ON public.Interview_Sessions
    FOR SELECT USING (user_email = auth.jwt() ->> 'email');

-- Allow companies to see sessions for their jobs
CREATE POLICY "Companies can view sessions for their jobs" ON public.Interview_Sessions
    FOR SELECT USING (
        company_id IN (
            SELECT id FROM public.Companies
            WHERE email = auth.jwt() ->> 'email'
        )
    );

-- Allow the service to insert new sessions
CREATE POLICY "Service can insert interview sessions" ON public.Interview_Sessions
    FOR INSERT WITH CHECK (true);

-- Allow the service to update sessions
CREATE POLICY "Service can update interview sessions" ON public.Interview_Sessions
    FOR UPDATE USING (true);

-- Grant necessary permissions
DO $$
BEGIN
    -- Grant table permissions
    GRANT SELECT, INSERT, UPDATE ON public.Interview_Sessions TO anon;
    GRANT SELECT, INSERT, UPDATE ON public.Interview_Sessions TO authenticated;

    -- Grant sequence permissions
    GRANT USAGE, SELECT ON SEQUENCE Interview_Sessions_id_seq TO anon;
    GRANT USAGE, SELECT ON SEQUENCE Interview_Sessions_id_seq TO authenticated;

    RAISE NOTICE 'Granted permissions on Interview_Sessions table and sequence';
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE 'Could not grant some permissions: %', SQLERRM;
END $$;

-- Final verification
DO $$
BEGIN
    RAISE NOTICE 'Interview_Sessions table setup completed successfully!';
    RAISE NOTICE 'Table structure:';
    RAISE NOTICE '- Primary key: id (SERIAL)';
    RAISE NOTICE '- Unique identifier: main_interview_id';
    RAISE NOTICE '- Foreign keys: job_id, company_id (without constraints for flexibility)';
    RAISE NOTICE '- User info: user_email, user_name';
    RAISE NOTICE '- Interview data: job_title, company_name, interview_type';
    RAISE NOTICE '- Status tracking: status, created_at, updated_at, completed_at';
    RAISE NOTICE '- JSON data: session_data, feedback_data';
    RAISE NOTICE '- Voice agent integration: voice_agent_session_id';
END $$;
