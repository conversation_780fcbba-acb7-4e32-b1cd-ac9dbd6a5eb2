-- Check if Interview_Sessions table exists and create it if needed
-- This table is used by the integration client to store interview session data

-- Check if table exists
SELECT EXISTS (
   SELECT FROM information_schema.tables 
   WHERE table_schema = 'public'
   AND table_name = 'Interview_Sessions'
);

-- Create the table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.Interview_Sessions (
    id SERIAL PRIMARY KEY,
    main_interview_id VARCHAR(255) UNIQUE NOT NULL,
    job_id INTEGER REFERENCES public.Jobs(id) ON DELETE SET NULL,
    company_id INTEGER REFERENCES public.Companies(id) ON DELETE SET NULL,
    user_email VARCHAR(255) NOT NULL,
    user_name VARCHAR(255),
    job_title VARCHAR(255),
    company_name VARCHAR(255),
    interview_type VARCHAR(50) DEFAULT 'job_application',
    status VARCHAR(50) DEFAULT 'pending',
    session_data JSONB,
    voice_agent_session_id VARCHAR(255),
    feedback_data JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_interview_sessions_main_id ON public.Interview_Sessions(main_interview_id);
CREATE INDEX IF NOT EXISTS idx_interview_sessions_user_email ON public.Interview_Sessions(user_email);
CREATE INDEX IF NOT EXISTS idx_interview_sessions_job_id ON public.Interview_Sessions(job_id);
CREATE INDEX IF NOT EXISTS idx_interview_sessions_company_id ON public.Interview_Sessions(company_id);
CREATE INDEX IF NOT EXISTS idx_interview_sessions_status ON public.Interview_Sessions(status);

-- Enable RLS (Row Level Security)
ALTER TABLE public.Interview_Sessions ENABLE ROW LEVEL SECURITY;

-- Create policies for RLS
-- Allow users to see their own interview sessions
CREATE POLICY IF NOT EXISTS "Users can view their own interview sessions" ON public.Interview_Sessions
    FOR SELECT USING (user_email = auth.jwt() ->> 'email');

-- Allow companies to see sessions for their jobs
CREATE POLICY IF NOT EXISTS "Companies can view sessions for their jobs" ON public.Interview_Sessions
    FOR SELECT USING (
        company_id IN (
            SELECT id FROM public.Companies 
            WHERE email = auth.jwt() ->> 'email'
        )
    );

-- Allow the service to insert new sessions
CREATE POLICY IF NOT EXISTS "Service can insert interview sessions" ON public.Interview_Sessions
    FOR INSERT WITH CHECK (true);

-- Allow the service to update sessions
CREATE POLICY IF NOT EXISTS "Service can update interview sessions" ON public.Interview_Sessions
    FOR UPDATE USING (true);

-- Grant necessary permissions
GRANT SELECT, INSERT, UPDATE ON public.Interview_Sessions TO anon;
GRANT SELECT, INSERT, UPDATE ON public.Interview_Sessions TO authenticated;
GRANT USAGE, SELECT ON SEQUENCE Interview_Sessions_id_seq TO anon;
GRANT USAGE, SELECT ON SEQUENCE Interview_Sessions_id_seq TO authenticated;
