"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(main)/jobs/[id]/page",{

/***/ "(app-pages-browser)/./lib/integration-client.js":
/*!***********************************!*\
  !*** ./lib/integration-client.js ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   IntegrationClient: () => (/* binding */ IntegrationClient),\n/* harmony export */   integrationClient: () => (/* binding */ integrationClient)\n/* harmony export */ });\n/* harmony import */ var _shared_integration_config_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../shared/integration-config.js */ \"(app-pages-browser)/../shared/integration-config.js\");\n/* harmony import */ var _services_supabaseClient_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../services/supabaseClient.js */ \"(app-pages-browser)/./services/supabaseClient.js\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n// Integration Client for Voice Agent Communication\n// This utility handles communication between the main platform and voice agent\n\n\nclass IntegrationClient {\n    // Create headers for API requests\n    createHeaders() {\n        return {\n            'Content-Type': 'application/json',\n            [_shared_integration_config_js__WEBPACK_IMPORTED_MODULE_0__.INTEGRATION_CONFIG.API_KEY_HEADER]: this.apiKey\n        };\n    }\n    // Make API request with retry logic\n    async makeRequest(url) {\n        let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n        const requestOptions = {\n            ...options,\n            headers: {\n                ...this.createHeaders(),\n                ...options.headers\n            },\n            signal: AbortSignal.timeout(_shared_integration_config_js__WEBPACK_IMPORTED_MODULE_0__.INTEGRATION_CONFIG.REQUEST_TIMEOUT)\n        };\n        for(let attempt = 0; attempt <= this.maxRetries; attempt++){\n            try {\n                const response = await fetch(url, requestOptions);\n                if (response.ok) {\n                    return await response.json();\n                }\n                // If it's a client error (4xx), don't retry\n                if (response.status >= 400 && response.status < 500) {\n                    var _errorData_error;\n                    const errorData = await response.json().catch(()=>({}));\n                    throw new Error(\"Client error \".concat(response.status, \": \").concat(((_errorData_error = errorData.error) === null || _errorData_error === void 0 ? void 0 : _errorData_error.message) || 'Unknown error'));\n                }\n                // For server errors (5xx), retry\n                if (attempt < this.maxRetries) {\n                    await this.delay(_shared_integration_config_js__WEBPACK_IMPORTED_MODULE_0__.INTEGRATION_CONFIG.RETRY_DELAY * (attempt + 1));\n                    continue;\n                }\n                throw new Error(\"Server error \".concat(response.status, \" after \").concat(this.maxRetries, \" retries\"));\n            } catch (error) {\n                var _error_name;\n                if (attempt < this.maxRetries && !((_error_name = error.name) === null || _error_name === void 0 ? void 0 : _error_name.includes('AbortError'))) {\n                    await this.delay(_shared_integration_config_js__WEBPACK_IMPORTED_MODULE_0__.INTEGRATION_CONFIG.RETRY_DELAY * (attempt + 1));\n                    continue;\n                }\n                throw error;\n            }\n        }\n    }\n    // Delay utility for retries\n    delay(ms) {\n        return new Promise((resolve)=>setTimeout(resolve, ms));\n    }\n    // Create interview session in voice agent\n    async createInterviewSession(sessionData) {\n        try {\n            (0,_shared_integration_config_js__WEBPACK_IMPORTED_MODULE_0__.logIntegrationEvent)('info', 'main_platform', 'creating_voice_agent_session', 'Creating interview session in voice agent', sessionData.sessionId);\n            const url = \"\".concat(_shared_integration_config_js__WEBPACK_IMPORTED_MODULE_0__.INTEGRATION_CONFIG.VOICE_AGENT_URL, \"/api/integration/interview/start\");\n            const response = await this.makeRequest(url, {\n                method: 'POST',\n                body: JSON.stringify(sessionData)\n            });\n            if (!response.success) {\n                var _response_error;\n                throw new Error(((_response_error = response.error) === null || _response_error === void 0 ? void 0 : _response_error.message) || 'Failed to create session in voice agent');\n            }\n            (0,_shared_integration_config_js__WEBPACK_IMPORTED_MODULE_0__.logIntegrationEvent)('info', 'main_platform', 'voice_agent_session_created', 'Interview session created in voice agent', sessionData.sessionId, {\n                voiceAgentInterviewId: response.data.voiceAgentInterviewId\n            });\n            return response.data;\n        } catch (error) {\n            (0,_shared_integration_config_js__WEBPACK_IMPORTED_MODULE_0__.logIntegrationEvent)('error', 'main_platform', 'voice_agent_session_failed', 'Failed to create session in voice agent', sessionData.sessionId, {\n                error: error.message\n            });\n            throw error;\n        }\n    }\n    // Get interview status from voice agent\n    async getInterviewStatus(sessionId) {\n        let voiceAgentInterviewId = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : null;\n        try {\n            const params = new URLSearchParams();\n            if (sessionId) params.append('sessionId', sessionId);\n            if (voiceAgentInterviewId) params.append('voiceAgentInterviewId', voiceAgentInterviewId);\n            const url = \"\".concat(_shared_integration_config_js__WEBPACK_IMPORTED_MODULE_0__.INTEGRATION_CONFIG.VOICE_AGENT_URL, \"/api/integration/interview/start?\").concat(params);\n            const response = await this.makeRequest(url, {\n                method: 'GET'\n            });\n            if (!response.success) {\n                var _response_error;\n                throw new Error(((_response_error = response.error) === null || _response_error === void 0 ? void 0 : _response_error.message) || 'Failed to get interview status');\n            }\n            return response.data;\n        } catch (error) {\n            (0,_shared_integration_config_js__WEBPACK_IMPORTED_MODULE_0__.logIntegrationEvent)('error', 'main_platform', 'get_status_failed', 'Failed to get interview status from voice agent', sessionId, {\n                error: error.message\n            });\n            throw error;\n        }\n    }\n    // Check voice agent health\n    async checkVoiceAgentHealth() {\n        try {\n            const url = \"\".concat(_shared_integration_config_js__WEBPACK_IMPORTED_MODULE_0__.INTEGRATION_CONFIG.VOICE_AGENT_URL, \"/api/integration/health\");\n            const response = await this.makeRequest(url, {\n                method: 'GET'\n            });\n            return response.data;\n        } catch (error) {\n            (0,_shared_integration_config_js__WEBPACK_IMPORTED_MODULE_0__.logIntegrationEvent)('warning', 'main_platform', 'voice_agent_health_check_failed', 'Voice agent health check failed', null, {\n                error: error.message\n            });\n            return {\n                status: 'unhealthy',\n                error: error.message,\n                timestamp: new Date().toISOString()\n            };\n        }\n    }\n    // Generate interview questions\n    async generateQuestions(jobData) {\n        try {\n            (0,_shared_integration_config_js__WEBPACK_IMPORTED_MODULE_0__.logIntegrationEvent)('info', 'main_platform', 'generating_questions', 'Generating interview questions', null, {\n                jobTitle: jobData.jobTitle\n            });\n            const response = await fetch('/api/generate-questions', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    jobTitle: jobData.jobTitle,\n                    jobDescription: jobData.jobDescription,\n                    requiredSkills: jobData.requiredSkills,\n                    experienceLevel: jobData.experienceLevel,\n                    questionCount: jobData.questionCount || _shared_integration_config_js__WEBPACK_IMPORTED_MODULE_0__.INTEGRATION_CONFIG.DEFAULT_QUESTION_COUNT\n                })\n            });\n            const questionsData = await response.json();\n            if (!response.ok) {\n                throw new Error(questionsData.error || 'Failed to generate questions');\n            }\n            // Format questions for voice agent\n            const formattedQuestions = (questionsData.questions || []).map((q, index)=>({\n                    id: \"q_\".concat(index + 1),\n                    question: q,\n                    type: 'technical',\n                    difficulty: 'medium',\n                    expectedDuration: 120,\n                    followUpAllowed: true,\n                    metadata: {\n                        generated: true,\n                        jobTitle: jobData.jobTitle\n                    }\n                }));\n            (0,_shared_integration_config_js__WEBPACK_IMPORTED_MODULE_0__.logIntegrationEvent)('info', 'main_platform', 'questions_generated', 'Interview questions generated successfully', null, {\n                questionCount: formattedQuestions.length\n            });\n            return formattedQuestions;\n        } catch (error) {\n            (0,_shared_integration_config_js__WEBPACK_IMPORTED_MODULE_0__.logIntegrationEvent)('error', 'main_platform', 'question_generation_failed', 'Failed to generate interview questions', null, {\n                error: error.message\n            });\n            // Return default questions as fallback\n            return [\n                {\n                    id: 'q_default_1',\n                    question: 'Tell me about yourself and your experience.',\n                    type: 'behavioral',\n                    difficulty: 'easy',\n                    expectedDuration: 120,\n                    followUpAllowed: true,\n                    metadata: {\n                        fallback: true\n                    }\n                },\n                {\n                    id: 'q_default_2',\n                    question: \"What interests you about the \".concat(jobData.jobTitle, \" position?\"),\n                    type: 'behavioral',\n                    difficulty: 'easy',\n                    expectedDuration: 120,\n                    followUpAllowed: true,\n                    metadata: {\n                        fallback: true\n                    }\n                }\n            ];\n        }\n    }\n    // Start interview process (creates session and redirects to voice agent)\n    async startInterview(interviewData) {\n        try {\n            const sessionId = \"session_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substr(2, 9));\n            (0,_shared_integration_config_js__WEBPACK_IMPORTED_MODULE_0__.logIntegrationEvent)('info', 'main_platform', 'starting_interview_process', 'Starting integrated interview process', sessionId, {\n                userEmail: interviewData.userEmail,\n                jobTitle: interviewData.jobTitle\n            });\n            // Generate questions if not provided\n            let questions = interviewData.questions;\n            if (!questions || questions.length === 0) {\n                questions = await this.generateQuestions({\n                    jobTitle: interviewData.jobTitle,\n                    jobDescription: interviewData.jobDescription,\n                    requiredSkills: interviewData.requiredSkills,\n                    experienceLevel: interviewData.experienceLevel,\n                    questionCount: interviewData.questionCount\n                });\n            }\n            // Prepare session data for voice agent\n            const sessionData = {\n                sessionId,\n                userEmail: interviewData.userEmail,\n                userName: interviewData.userName,\n                jobTitle: interviewData.jobTitle,\n                jobDescription: interviewData.jobDescription,\n                companyName: interviewData.companyName,\n                interviewType: interviewData.interviewType || 'job_application',\n                duration: interviewData.duration || _shared_integration_config_js__WEBPACK_IMPORTED_MODULE_0__.INTEGRATION_CONFIG.DEFAULT_INTERVIEW_DURATION,\n                experienceLevel: interviewData.experienceLevel,\n                requiredSkills: interviewData.requiredSkills,\n                companyCriteria: interviewData.companyCriteria,\n                questions,\n                questionCount: questions.length,\n                callbackUrl: \"\".concat(_shared_integration_config_js__WEBPACK_IMPORTED_MODULE_0__.INTEGRATION_CONFIG.MAIN_PLATFORM_URL, \"/api/integration/interview/feedback\"),\n                webhookSecret: process.env.INTEGRATION_SHARED_SECRET || 'webhook-secret'\n            };\n            // Create session in main platform database\n            // Using the existing supabase client from services\n            const { data: session, error: sessionError } = await _services_supabaseClient_js__WEBPACK_IMPORTED_MODULE_1__.supabase.from('Interview_Sessions').insert([\n                {\n                    main_interview_id: sessionId,\n                    job_id: interviewData.jobId || null,\n                    company_id: interviewData.companyId || null,\n                    user_email: interviewData.userEmail,\n                    user_name: interviewData.userName,\n                    job_title: interviewData.jobTitle,\n                    company_name: interviewData.companyName,\n                    interview_type: sessionData.interviewType,\n                    status: 'pending',\n                    session_data: {\n                        jobDescription: interviewData.jobDescription,\n                        requiredSkills: interviewData.requiredSkills,\n                        experienceLevel: interviewData.experienceLevel,\n                        companyCriteria: interviewData.companyCriteria,\n                        duration: sessionData.duration,\n                        questionCount: questions.length,\n                        questions: questions,\n                        voiceAgentUrl: \"\".concat(_shared_integration_config_js__WEBPACK_IMPORTED_MODULE_0__.INTEGRATION_CONFIG.VOICE_AGENT_URL, \"/interview/external?sessionId=\").concat(sessionId),\n                        integrationVersion: _shared_integration_config_js__WEBPACK_IMPORTED_MODULE_0__.INTEGRATION_CONFIG.INTEGRATION_VERSION\n                    }\n                }\n            ]).select().single();\n            if (sessionError) {\n                console.error('Session creation error:', sessionError);\n                const errorMessage = sessionError.message || sessionError.details || JSON.stringify(sessionError);\n                throw new Error(\"Failed to create session: \".concat(errorMessage));\n            }\n            // Create session in voice agent\n            let voiceAgentResponse;\n            try {\n                voiceAgentResponse = await this.createInterviewSession(sessionData);\n            } catch (voiceAgentError) {\n                console.error('Voice agent error:', voiceAgentError);\n                // Update session status to indicate voice agent failure\n                await _services_supabaseClient_js__WEBPACK_IMPORTED_MODULE_1__.supabase.from('Interview_Sessions').update({\n                    status: 'voice_agent_error',\n                    session_data: {\n                        ...session.session_data,\n                        error: voiceAgentError.message,\n                        errorTime: new Date().toISOString()\n                    }\n                }).eq('id', session.id);\n                throw new Error(\"Voice agent connection failed: \".concat(voiceAgentError.message, \". Please ensure the voice agent is running on \").concat(_shared_integration_config_js__WEBPACK_IMPORTED_MODULE_0__.INTEGRATION_CONFIG.VOICE_AGENT_URL));\n            }\n            // Update session with voice agent details\n            await _services_supabaseClient_js__WEBPACK_IMPORTED_MODULE_1__.supabase.from('Interview_Sessions').update({\n                voice_agent_session_id: voiceAgentResponse.voiceAgentInterviewId,\n                status: 'ready'\n            }).eq('id', session.id);\n            const voiceAgentUrl = \"\".concat(_shared_integration_config_js__WEBPACK_IMPORTED_MODULE_0__.INTEGRATION_CONFIG.VOICE_AGENT_URL, \"/interview/external?sessionId=\").concat(sessionId);\n            (0,_shared_integration_config_js__WEBPACK_IMPORTED_MODULE_0__.logIntegrationEvent)('info', 'main_platform', 'interview_process_ready', 'Interview process ready, redirecting to voice agent', sessionId, {\n                voiceAgentUrl: voiceAgentUrl\n            });\n            return {\n                sessionId,\n                voiceAgentUrl: voiceAgentUrl,\n                voiceAgentInterviewId: voiceAgentResponse.voiceAgentInterviewId,\n                estimatedDuration: sessionData.duration,\n                questionCount: questions.length\n            };\n        } catch (error) {\n            (0,_shared_integration_config_js__WEBPACK_IMPORTED_MODULE_0__.logIntegrationEvent)('error', 'main_platform', 'interview_start_failed', 'Failed to start interview process', null, {\n                error: error.message\n            });\n            throw error;\n        }\n    }\n    // Get feedback status\n    async getFeedbackStatus(sessionId) {\n        try {\n            // Using the existing supabase client from services\n            const { data: feedback, error } = await _services_supabaseClient_js__WEBPACK_IMPORTED_MODULE_1__.supabase.from('Interview_Feedback_Bridge').select('*').eq('main_interview_id', sessionId).single();\n            if (error && error.code !== 'PGRST116') {\n                throw new Error(\"Database error: \".concat(error.message));\n            }\n            return feedback;\n        } catch (error) {\n            (0,_shared_integration_config_js__WEBPACK_IMPORTED_MODULE_0__.logIntegrationEvent)('error', 'main_platform', 'get_feedback_status_failed', 'Failed to get feedback status', sessionId, {\n                error: error.message\n            });\n            throw error;\n        }\n    }\n    constructor(){\n        this.apiKey = process.env.INTEGRATION_API_KEY || 'main-platform-api-key';\n        this.retryCount = 0;\n        this.maxRetries = _shared_integration_config_js__WEBPACK_IMPORTED_MODULE_0__.INTEGRATION_CONFIG.MAX_RETRIES;\n    }\n}\n// Export singleton instance\nconst integrationClient = new IntegrationClient();\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/integration-client.js\n"));

/***/ })

});