"use client"

import { useEffect, useState } from 'react';

/**
 * HydrationProvider - Ensures consistent hydration across the app
 * This component prevents hydration mismatches by ensuring the client
 * and server render the same content initially
 */
export default function HydrationProvider({ children }) {
  const [isHydrated, setIsHydrated] = useState(false);

  useEffect(() => {
    // Mark as hydrated after the first client-side render
    setIsHydrated(true);

    // Clean up any browser extension attributes that might cause hydration issues
    const cleanupBrowserExtensions = () => {
      if (typeof document === 'undefined') return;

      const body = document.body;
      const html = document.documentElement;

      // List of problematic attributes added by browser extensions
      const problematicAttributes = [
        'cz-shortcut-listen',           // ColorZilla
        'data-new-gr-c-s-check-loaded', // Grammarly
        'data-gr-ext-installed',        // Grammarly
        'data-gramm',                   // Grammarly
        'data-gramm_editor',            // Grammarly
        'data-enable-grammarly',        // Grammarly
        'spellcheck',                   // Various spell checkers
        'data-ms-editor',               // Microsoft Editor
        'data-lt-installed',            // LanguageTool
        'data-darkreader-mode',         // Dark Reader
        'data-darkreader-scheme',       // Dark Reader
        'data-adblock',                 // Ad blockers
        'data-honey-extension',         // Honey
        'data-lastpass',                // LastPass
        'data-bitwarden',               // Bitwarden
        'data-dashlane',                // Dashlane
        'data-1password',               // 1Password
        'data-rh-extension',            // RoboForm
        'data-keeper',                  // Keeper
        'data-nordpass',                // NordPass
      ];

      // Clean body attributes
      problematicAttributes.forEach(attr => {
        if (body.hasAttribute(attr)) {
          body.removeAttribute(attr);
        }
        if (html.hasAttribute(attr)) {
          html.removeAttribute(attr);
        }
      });

      // Also remove any data-* attributes that look like extension attributes
      [body, html].forEach(element => {
        Array.from(element.attributes).forEach(attr => {
          if (attr.name.startsWith('data-') && 
              (attr.name.includes('extension') || 
               attr.name.includes('plugin') || 
               attr.name.includes('addon') ||
               attr.name.includes('ext-') ||
               attr.name.includes('-ext'))) {
            element.removeAttribute(attr.name);
          }
        });
      });
    };

    // Clean up immediately
    cleanupBrowserExtensions();

    // Set up a mutation observer to handle dynamically added attributes
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'attributes') {
          const target = mutation.target;
          const attributeName = mutation.attributeName;
          
          // Only process body and html elements
          if (target !== document.body && target !== document.documentElement) {
            return;
          }
          
          // Check if it's a problematic extension attribute
          if (attributeName && (
            attributeName.startsWith('cz-') ||
            attributeName.startsWith('data-gr-') ||
            attributeName.startsWith('data-gramm') ||
            attributeName.startsWith('data-ms-') ||
            attributeName.startsWith('data-lt-') ||
            attributeName.startsWith('data-darkreader') ||
            attributeName.startsWith('data-rh-') ||
            attributeName.includes('extension') ||
            attributeName.includes('plugin') ||
            attributeName.includes('addon') ||
            attributeName.includes('ext-') ||
            attributeName.includes('-ext')
          )) {
            target.removeAttribute(attributeName);
          }
        }
      });
    });

    // Observe both body and html elements
    observer.observe(document.body, {
      attributes: true,
      attributeOldValue: true
    });

    observer.observe(document.documentElement, {
      attributes: true,
      attributeOldValue: true
    });

    // Cleanup function
    return () => {
      observer.disconnect();
    };
  }, []);

  // Suppress hydration warnings for the initial render
  return (
    <div suppressHydrationWarning={true}>
      {children}
    </div>
  );
}
