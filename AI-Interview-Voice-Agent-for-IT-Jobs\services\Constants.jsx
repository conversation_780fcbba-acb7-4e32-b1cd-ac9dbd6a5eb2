import {
    BriefcaseBusinessIcon,
    Building2,
    Calendar,
    ClipboardList,
    Code2Icon,
    Component,
    FileText,
    LayoutDashboard,
    List,
    Puzzle,
    Settings,
    User,
    User2Icon,
    Users,
    WalletCards
} from "lucide-react";

export const SideBarOptions = [
    {
        name: 'Dashboard',
        icon: LayoutDashboard,
        path: '/dashboard'
    },
    {
        name: 'Find Jobs',
        icon: BriefcaseBusinessIcon,
        path: '/jobs'
    },
    {
        name: 'My Applications',
        icon: FileText,
        path: '/dashboard/applications'
    },
    {
        name: 'My Profile',
        icon: User,
        path: '/dashboard/profile'
    },
    {
        name: 'Billing',
        icon: WalletCards,
        path: '/billing'
    },
    {
        name: 'Settings',
        icon: Settings,
        path: '/settings'
    },
]

export const CompanySideBarOptions = [
    {
        name: 'Dashboard',
        icon: LayoutDashboard,
        path: '/company/dashboard'
    },
    {
        name: 'Jobs',
        icon: BriefcaseBusinessIcon,
        path: '/company/jobs'
    },
    {
        name: 'Submissions',
        icon: ClipboardList,
        path: '/company/submissions'
    },
    {
        name: 'Candidates',
        icon: Users,
        path: '/company/candidates'
    },
    {
        name: 'Company Profile',
        icon: Building2,
        path: '/company/profile'
    },
    {
        name: 'Settings',
        icon: Settings,
        path: '/company/settings'
    },
]

export const InterviewType = [
    {
        title: 'Technical',
        icon: Code2Icon
    },
    {
        title: 'Behavioral',
        icon: User2Icon
    },
    {
        title: 'Experience',
        icon: BriefcaseBusinessIcon
    },
    {
        title: 'Problem Solving',
        icon: Puzzle
    },
    {
        title: 'Leadership',
        icon: Component
    }
]

export const EmploymentTypes = [
    "Full-time",
    "Part-time",
    "Contract",
    "Temporary",
    "Internship",
    "Freelance"
]

export const LocationTypes = [
    "Remote",
    "On-site",
    "Hybrid"
]

export const ExperienceLevels = [
    "Entry Level",
    "Junior",
    "Mid-Level",
    "Senior",
    "Lead",
    "Manager",
    "Director",
    "Executive"
]

export const QUESTIONS_PROMPT = `You are an expert technical interviewer.
Based on the following inputs, generate a well-structured list of high-quality interview questions:

Job Title: {{jobTitle}}

Job Description: {{jobDescription}}

Interview Duration: {{duration}}

Interview Type: {{type}}

Experience Level: {{experienceLevel}}

Required Skills: {{requiredSkills}}

Company Criteria: {{companyCriteria}}

Number of Questions: {{questionCount}}

📝 Your task:

Analyze the job description to identify key responsibilities, required skills, and expected experience.

Generate a list of interview questions based on the interview duration and requested number of questions.

Adjust the number and depth of questions to match the interview duration.

Ensure the questions match the tone and structure of a real-life {{type}} interview.

Pay special attention to the company's specific criteria for the ideal candidate.

🧩 Format your response in JSON format with array list of questions.
format: interviewQuestions=[
{
 question:'',
 type:'Technical/Behavioral/Experience/Problem Solving/Leadership'
},{
...
}]

🎯 The goal is to create a structured, relevant, and time-optimized interview plan for a {{jobTitle}} role.`



export const FEEDBACK_PROMPT = `You are an expert HR interviewer and career coach. Analyze the following interview conversation and provide comprehensive feedback.

INTERVIEW CONVERSATION:
{{conversation}}

JOB DETAILS:
- Job Title: {{jobTitle}}
- Job Description: {{jobDescription}}
- Required Skills: {{requiredSkills}}
- Company Criteria: {{companyCriteria}}

ANALYSIS INSTRUCTIONS:
1. Evaluate the candidate's performance across multiple dimensions
2. Provide specific examples from the conversation
3. Give actionable improvement suggestions
4. Rate each area on a scale of 1-10
5. Calculate an overall match score based on job requirements

Provide your response in the following JSON format:
{
    "feedback": {
        "overallScore": <number 1-100>,
        "recommendation": <true/false>,
        "recommendationMessage": "<one line recommendation>",
        "ratings": {
            "technicalSkills": <number 1-10>,
            "communication": <number 1-10>,
            "problemSolving": <number 1-10>,
            "experience": <number 1-10>,
            "culturalFit": <number 1-10>,
            "enthusiasm": <number 1-10>
        },
        "strengths": [
            "<specific strength with example>",
            "<specific strength with example>",
            "<specific strength with example>"
        ],
        "areasForImprovement": [
            {
                "area": "<area name>",
                "issue": "<specific issue>",
                "suggestion": "<actionable improvement suggestion>"
            },
            {
                "area": "<area name>",
                "issue": "<specific issue>",
                "suggestion": "<actionable improvement suggestion>"
            }
        ],
        "detailedAnalysis": {
            "technicalCompetency": "<detailed analysis of technical skills>",
            "communicationStyle": "<analysis of communication effectiveness>",
            "problemSolvingApproach": "<analysis of problem-solving methodology>",
            "experienceRelevance": "<how well experience matches the role>"
        },
        "interviewPerformance": {
            "questionHandling": "<how well they answered questions>",
            "clarityOfResponses": "<clarity and structure of answers>",
            "examplesProvided": "<quality of examples given>",
            "professionalDemeanor": "<professional presentation>"
        },
        "careerAdvice": [
            "<specific career development suggestion>",
            "<specific skill to develop>",
            "<specific resource or action to take>"
        ],
        "matchAnalysis": {
            "jobRequirementMatch": <percentage 0-100>,
            "skillGaps": ["<skill gap 1>", "<skill gap 2>"],
            "strongMatches": ["<strong match 1>", "<strong match 2>"],
            "growthPotential": "<assessment of growth potential>"
        },
        "nextSteps": [
            "<immediate action item>",
            "<short-term goal>",
            "<long-term development area>"
        ]
    }
}
`



