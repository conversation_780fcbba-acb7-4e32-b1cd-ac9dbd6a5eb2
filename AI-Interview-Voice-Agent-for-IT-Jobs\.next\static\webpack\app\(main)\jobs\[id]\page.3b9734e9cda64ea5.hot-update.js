"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(main)/jobs/[id]/page",{

/***/ "(app-pages-browser)/./lib/simple-integration-client.js":
/*!******************************************!*\
  !*** ./lib/simple-integration-client.js ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SimpleIntegrationClient: () => (/* binding */ SimpleIntegrationClient),\n/* harmony export */   simpleIntegrationClient: () => (/* binding */ simpleIntegrationClient)\n/* harmony export */ });\n/* harmony import */ var _shared_integration_config_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../shared/integration-config.js */ \"(app-pages-browser)/../shared/integration-config.js\");\n// Simple Integration Client - URL-based communication\n// This approach avoids database sharing and uses URL parameters to pass data\n\nclass SimpleIntegrationClient {\n    // Generate interview questions using AI\n    async generateQuestions(param) {\n        let { jobTitle, jobDescription = '', requiredSkills = '', experienceLevel = 'Mid-Level', questionCount = 5 } = param;\n        try {\n            console.log('Generating questions for:', {\n                jobTitle,\n                experienceLevel,\n                questionCount\n            });\n            // Simple fallback questions based on job title and experience level\n            const baseQuestions = [\n                {\n                    id: 1,\n                    question: \"Tell me about your experience with \".concat(jobTitle, \" roles.\"),\n                    category: 'experience',\n                    difficulty: experienceLevel.toLowerCase(),\n                    expectedDuration: 120\n                },\n                {\n                    id: 2,\n                    question: \"What interests you most about this \".concat(jobTitle, \" position?\"),\n                    category: 'motivation',\n                    difficulty: 'basic',\n                    expectedDuration: 90\n                },\n                {\n                    id: 3,\n                    question: \"How do you handle challenging situations in your work?\",\n                    category: 'problem_solving',\n                    difficulty: 'intermediate',\n                    expectedDuration: 120\n                },\n                {\n                    id: 4,\n                    question: \"Where do you see yourself in your career in the next few years?\",\n                    category: 'career_goals',\n                    difficulty: 'basic',\n                    expectedDuration: 90\n                },\n                {\n                    id: 5,\n                    question: \"Do you have any questions about the role or our company?\",\n                    category: 'engagement',\n                    difficulty: 'basic',\n                    expectedDuration: 60\n                }\n            ];\n            // Add skill-specific questions if skills are provided\n            if (requiredSkills) {\n                const skillsArray = requiredSkills.split(',').map((s)=>s.trim());\n                if (skillsArray.length > 0) {\n                    baseQuestions.splice(2, 0, {\n                        id: 6,\n                        question: \"Can you describe your experience with \".concat(skillsArray[0], \"?\"),\n                        category: 'technical_skills',\n                        difficulty: experienceLevel.toLowerCase(),\n                        expectedDuration: 150\n                    });\n                }\n            }\n            return baseQuestions.slice(0, questionCount);\n        } catch (error) {\n            console.error('Error generating questions:', error);\n            // Return fallback questions\n            return [\n                {\n                    id: 1,\n                    question: \"Tell me about yourself and your background.\",\n                    category: 'general',\n                    difficulty: 'basic',\n                    expectedDuration: 120\n                },\n                {\n                    id: 2,\n                    question: \"Why are you interested in this position?\",\n                    category: 'motivation',\n                    difficulty: 'basic',\n                    expectedDuration: 90\n                },\n                {\n                    id: 3,\n                    question: \"What are your strengths and weaknesses?\",\n                    category: 'self_assessment',\n                    difficulty: 'intermediate',\n                    expectedDuration: 120\n                }\n            ];\n        }\n    }\n    // Start interview process - Simple URL-based approach\n    async startInterview(interviewData) {\n        try {\n            (0,_shared_integration_config_js__WEBPACK_IMPORTED_MODULE_0__.logIntegrationEvent)('info', 'main_platform', 'interview_start_requested', 'Starting simple interview process', null, interviewData);\n            // Generate unique session ID\n            const sessionId = \"main_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substr(2, 9));\n            // Validate required data\n            if (!interviewData.userEmail || !interviewData.jobTitle) {\n                throw new Error('Missing required interview data: userEmail and jobTitle are required');\n            }\n            // Generate questions if not provided\n            let questions = interviewData.questions;\n            if (!questions || questions.length === 0) {\n                questions = await this.generateQuestions({\n                    jobTitle: interviewData.jobTitle,\n                    jobDescription: interviewData.jobDescription,\n                    requiredSkills: interviewData.requiredSkills,\n                    experienceLevel: interviewData.experienceLevel,\n                    questionCount: interviewData.questionCount || 5\n                });\n            }\n            // Prepare interview data to pass via URL parameters\n            const interviewParams = {\n                sessionId: sessionId,\n                userEmail: interviewData.userEmail,\n                userName: interviewData.userName || 'User',\n                jobTitle: interviewData.jobTitle,\n                jobDescription: interviewData.jobDescription || '',\n                companyName: interviewData.companyName || 'Company',\n                interviewType: interviewData.interviewType || 'job_application',\n                duration: interviewData.duration || 30,\n                experienceLevel: interviewData.experienceLevel || 'Mid-Level',\n                requiredSkills: interviewData.requiredSkills || '',\n                companyCriteria: interviewData.companyCriteria || '',\n                questionCount: questions.length,\n                jobId: interviewData.jobId || '',\n                companyId: interviewData.companyId || '',\n                questions: questions\n            };\n            // Encode the interview data as base64 to pass safely in URL\n            // Use encodeURIComponent + btoa to handle Unicode characters safely\n            const jsonString = JSON.stringify(interviewParams);\n            const encodedData = btoa(encodeURIComponent(jsonString));\n            // Create the voice agent URL with encoded data\n            const voiceAgentUrl = \"\".concat(_shared_integration_config_js__WEBPACK_IMPORTED_MODULE_0__.INTEGRATION_CONFIG.VOICE_AGENT_URL, \"/interview/external?data=\").concat(encodedData);\n            (0,_shared_integration_config_js__WEBPACK_IMPORTED_MODULE_0__.logIntegrationEvent)('info', 'main_platform', 'interview_process_ready', 'Interview process ready, redirecting to voice agent', sessionId, {\n                voiceAgentUrl: voiceAgentUrl\n            });\n            console.log('✅ Interview session created successfully:', {\n                sessionId,\n                userEmail: interviewData.userEmail,\n                jobTitle: interviewData.jobTitle,\n                questionCount: questions.length\n            });\n            return {\n                sessionId,\n                voiceAgentUrl: voiceAgentUrl,\n                estimatedDuration: interviewParams.duration,\n                questionCount: questions.length,\n                success: true\n            };\n        } catch (error) {\n            console.error('❌ Failed to start interview:', error);\n            (0,_shared_integration_config_js__WEBPACK_IMPORTED_MODULE_0__.logIntegrationEvent)('error', 'main_platform', 'interview_start_failed', 'Failed to start interview process', null, {\n                error: error.message\n            });\n            throw error;\n        }\n    }\n    // Handle feedback from voice agent (via webhook or API call)\n    async handleInterviewFeedback(feedbackData) {\n        try {\n            (0,_shared_integration_config_js__WEBPACK_IMPORTED_MODULE_0__.logIntegrationEvent)('info', 'main_platform', 'feedback_received', 'Received interview feedback from voice agent', feedbackData.sessionId, feedbackData);\n            // Process the feedback data\n            // This could involve saving to database, sending emails, etc.\n            console.log('📝 Interview feedback received:', {\n                sessionId: feedbackData.sessionId,\n                score: feedbackData.overallScore,\n                status: feedbackData.status\n            });\n            return {\n                success: true,\n                message: 'Feedback processed successfully'\n            };\n        } catch (error) {\n            console.error('❌ Failed to process feedback:', error);\n            (0,_shared_integration_config_js__WEBPACK_IMPORTED_MODULE_0__.logIntegrationEvent)('error', 'main_platform', 'feedback_processing_failed', 'Failed to process interview feedback', feedbackData === null || feedbackData === void 0 ? void 0 : feedbackData.sessionId, {\n                error: error.message\n            });\n            throw error;\n        }\n    }\n    // Check if voice agent is available\n    async checkVoiceAgentHealth() {\n        try {\n            const response = await fetch(\"\".concat(_shared_integration_config_js__WEBPACK_IMPORTED_MODULE_0__.INTEGRATION_CONFIG.VOICE_AGENT_URL, \"/api/health\"), {\n                method: 'GET',\n                headers: {\n                    'Content-Type': 'application/json'\n                }\n            });\n            return {\n                available: response.ok,\n                status: response.status,\n                url: _shared_integration_config_js__WEBPACK_IMPORTED_MODULE_0__.INTEGRATION_CONFIG.VOICE_AGENT_URL\n            };\n        } catch (error) {\n            console.warn('Voice agent health check failed:', error.message);\n            return {\n                available: false,\n                error: error.message,\n                url: _shared_integration_config_js__WEBPACK_IMPORTED_MODULE_0__.INTEGRATION_CONFIG.VOICE_AGENT_URL\n            };\n        }\n    }\n    constructor(){\n        this.baseUrl = _shared_integration_config_js__WEBPACK_IMPORTED_MODULE_0__.INTEGRATION_CONFIG.VOICE_AGENT_URL;\n    }\n}\n// Export singleton instance\nconst simpleIntegrationClient = new SimpleIntegrationClient();\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/simple-integration-client.js\n"));

/***/ })

});