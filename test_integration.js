#!/usr/bin/env node

// Integration Test Script
// This script tests the communication between the main platform and voice agent

const fetch = require('node-fetch');
const { performance } = require('perf_hooks');

// Configuration
const CONFIG = {
  MAIN_PLATFORM_URL: process.env.MAIN_PLATFORM_URL || 'http://localhost:3000',
  VOICE_AGENT_URL: process.env.VOICE_AGENT_URL || 'http://localhost:3001',
  API_KEY_HEADER: 'X-Integration-API-Key',
  MAIN_PLATFORM_API_KEY: process.env.INTEGRATION_API_KEY || 'main-platform-api-key',
  VOICE_AGENT_API_KEY: process.env.INTEGRATION_API_KEY || 'voice-agent-api-key',
  REQUEST_TIMEOUT: 10000
};

// Test utilities
class IntegrationTester {
  constructor() {
    this.results = [];
    this.startTime = performance.now();
  }

  async runTest(name, testFunction) {
    console.log(`\n🧪 Running test: ${name}`);
    const testStart = performance.now();
    
    try {
      const result = await testFunction();
      const duration = Math.round(performance.now() - testStart);
      
      this.results.push({
        name,
        status: 'PASS',
        duration,
        result
      });
      
      console.log(`✅ ${name} - PASSED (${duration}ms)`);
      return result;
    } catch (error) {
      const duration = Math.round(performance.now() - testStart);
      
      this.results.push({
        name,
        status: 'FAIL',
        duration,
        error: error.message
      });
      
      console.log(`❌ ${name} - FAILED (${duration}ms)`);
      console.log(`   Error: ${error.message}`);
      throw error;
    }
  }

  async makeRequest(url, options = {}) {
    const controller = new AbortController();
    const timeout = setTimeout(() => controller.abort(), CONFIG.REQUEST_TIMEOUT);
    
    try {
      const response = await fetch(url, {
        ...options,
        signal: controller.signal
      });
      
      clearTimeout(timeout);
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${data.error?.message || 'Unknown error'}`);
      }
      
      return data;
    } catch (error) {
      clearTimeout(timeout);
      throw error;
    }
  }

  printSummary() {
    const totalTime = Math.round(performance.now() - this.startTime);
    const passed = this.results.filter(r => r.status === 'PASS').length;
    const failed = this.results.filter(r => r.status === 'FAIL').length;
    
    console.log('\n' + '='.repeat(60));
    console.log('🔍 INTEGRATION TEST SUMMARY');
    console.log('='.repeat(60));
    console.log(`Total Tests: ${this.results.length}`);
    console.log(`Passed: ${passed}`);
    console.log(`Failed: ${failed}`);
    console.log(`Total Time: ${totalTime}ms`);
    console.log('='.repeat(60));
    
    if (failed > 0) {
      console.log('\n❌ Failed Tests:');
      this.results
        .filter(r => r.status === 'FAIL')
        .forEach(r => {
          console.log(`   - ${r.name}: ${r.error}`);
        });
    }
    
    console.log(`\n${failed === 0 ? '🎉 All tests passed!' : '⚠️  Some tests failed.'}`);
  }
}

// Test functions
async function testMainPlatformHealth(tester) {
  const url = `${CONFIG.MAIN_PLATFORM_URL}/api/integration/health`;
  const response = await tester.makeRequest(url);
  
  if (!response.success || !response.data) {
    throw new Error('Invalid health check response format');
  }
  
  const health = response.data;
  if (health.status !== 'healthy' && health.status !== 'degraded') {
    throw new Error(`Main platform status: ${health.status}`);
  }
  
  return health;
}

async function testVoiceAgentHealth(tester) {
  const url = `${CONFIG.VOICE_AGENT_URL}/api/integration/health`;
  const response = await tester.makeRequest(url);
  
  if (!response.success || !response.data) {
    throw new Error('Invalid health check response format');
  }
  
  const health = response.data;
  if (health.status !== 'healthy' && health.status !== 'degraded') {
    throw new Error(`Voice agent status: ${health.status}`);
  }
  
  return health;
}

async function testCreateInterviewSession(tester) {
  const sessionId = `test_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  
  const sessionData = {
    sessionId,
    userEmail: '<EMAIL>',
    userName: 'Test User',
    jobTitle: 'Software Engineer',
    jobDescription: 'Full-stack development role',
    companyName: 'Test Company',
    interviewType: 'job_application',
    duration: 30,
    experienceLevel: 'Mid-Level',
    requiredSkills: 'JavaScript, React, Node.js',
    companyCriteria: 'Strong problem-solving skills',
    questions: [
      {
        id: 'q1',
        question: 'Tell me about yourself.',
        type: 'behavioral',
        difficulty: 'easy',
        expectedDuration: 120,
        followUpAllowed: true,
        metadata: {}
      }
    ],
    questionCount: 1,
    callbackUrl: `${CONFIG.MAIN_PLATFORM_URL}/api/integration/interview/feedback`,
    webhookSecret: 'test-secret'
  };
  
  const url = `${CONFIG.MAIN_PLATFORM_URL}/api/integration/interview/create`;
  const response = await tester.makeRequest(url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      [CONFIG.API_KEY_HEADER]: CONFIG.MAIN_PLATFORM_API_KEY
    },
    body: JSON.stringify(sessionData)
  });
  
  if (!response.success || !response.data) {
    throw new Error('Failed to create interview session');
  }
  
  return { sessionId, sessionData: response.data };
}

async function testStartVoiceAgentInterview(tester, sessionData) {
  const url = `${CONFIG.VOICE_AGENT_URL}/api/integration/interview/start`;
  const response = await tester.makeRequest(url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      [CONFIG.API_KEY_HEADER]: CONFIG.VOICE_AGENT_API_KEY
    },
    body: JSON.stringify(sessionData)
  });
  
  if (!response.success || !response.data) {
    throw new Error('Failed to start voice agent interview');
  }
  
  return response.data;
}

async function testSubmitFeedback(tester, sessionId, voiceAgentInterviewId) {
  const feedbackData = {
    sessionId,
    voiceAgentInterviewId,
    userEmail: '<EMAIL>',
    userName: 'Test User',
    overallScore: 85,
    technicalScore: 88,
    communicationScore: 82,
    problemSolvingScore: 87,
    recommendation: 'hire',
    strengths: ['Strong technical knowledge', 'Good communication'],
    areasForImprovement: ['Could provide more examples'],
    overallAssessment: 'Excellent candidate with strong technical skills.',
    duration: 25,
    questionsAsked: 1,
    questionsAnswered: 1,
    conversationTranscript: [
      {
        speaker: 'ai',
        message: 'Tell me about yourself.',
        timestamp: new Date().toISOString()
      },
      {
        speaker: 'user',
        message: 'I am a software engineer with 5 years of experience.',
        timestamp: new Date().toISOString()
      }
    ],
    completedAt: new Date().toISOString(),
    interviewQuality: 'excellent',
    callbackUrl: `${CONFIG.MAIN_PLATFORM_URL}/api/integration/interview/feedback`,
    webhookSecret: 'test-secret'
  };
  
  const url = `${CONFIG.VOICE_AGENT_URL}/api/integration/interview/feedback`;
  const response = await tester.makeRequest(url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      [CONFIG.API_KEY_HEADER]: CONFIG.VOICE_AGENT_API_KEY
    },
    body: JSON.stringify(feedbackData)
  });
  
  if (!response.success || !response.data) {
    throw new Error('Failed to submit feedback');
  }
  
  return response.data;
}

async function testGetSessionStatus(tester, sessionId) {
  const url = `${CONFIG.MAIN_PLATFORM_URL}/api/integration/interview/create?sessionId=${sessionId}`;
  const response = await tester.makeRequest(url, {
    headers: {
      [CONFIG.API_KEY_HEADER]: CONFIG.MAIN_PLATFORM_API_KEY
    }
  });
  
  if (!response.success || !response.data) {
    throw new Error('Failed to get session status');
  }
  
  return response.data;
}

// Main test runner
async function runIntegrationTests() {
  console.log('🚀 Starting Integration Tests');
  console.log('='.repeat(60));
  console.log(`Main Platform: ${CONFIG.MAIN_PLATFORM_URL}`);
  console.log(`Voice Agent: ${CONFIG.VOICE_AGENT_URL}`);
  console.log('='.repeat(60));
  
  const tester = new IntegrationTester();
  let sessionId, voiceAgentInterviewId;
  
  try {
    // Test health checks
    await tester.runTest('Main Platform Health Check', () => testMainPlatformHealth(tester));
    await tester.runTest('Voice Agent Health Check', () => testVoiceAgentHealth(tester));
    
    // Test session creation
    const sessionResult = await tester.runTest('Create Interview Session', () => testCreateInterviewSession(tester));
    sessionId = sessionResult.sessionId;
    
    // Test voice agent interview start
    const voiceAgentResult = await tester.runTest('Start Voice Agent Interview', () => 
      testStartVoiceAgentInterview(tester, {
        sessionId,
        userEmail: '<EMAIL>',
        userName: 'Test User',
        jobTitle: 'Software Engineer',
        jobDescription: 'Full-stack development role',
        companyName: 'Test Company',
        interviewType: 'job_application',
        duration: 30,
        experienceLevel: 'Mid-Level',
        requiredSkills: 'JavaScript, React, Node.js',
        companyCriteria: 'Strong problem-solving skills',
        questions: [
          {
            id: 'q1',
            question: 'Tell me about yourself.',
            type: 'behavioral',
            difficulty: 'easy',
            expectedDuration: 120,
            followUpAllowed: true,
            metadata: {}
          }
        ],
        questionCount: 1,
        callbackUrl: `${CONFIG.MAIN_PLATFORM_URL}/api/integration/interview/feedback`,
        webhookSecret: 'test-secret'
      })
    );
    voiceAgentInterviewId = voiceAgentResult.voiceAgentInterviewId;
    
    // Test feedback submission
    await tester.runTest('Submit Interview Feedback', () => 
      testSubmitFeedback(tester, sessionId, voiceAgentInterviewId)
    );
    
    // Test session status retrieval
    await tester.runTest('Get Session Status', () => testGetSessionStatus(tester, sessionId));
    
  } catch (error) {
    console.log(`\n💥 Test suite failed: ${error.message}`);
  }
  
  tester.printSummary();
  
  // Exit with appropriate code
  const failed = tester.results.filter(r => r.status === 'FAIL').length;
  process.exit(failed > 0 ? 1 : 0);
}

// Run tests if this script is executed directly
if (require.main === module) {
  runIntegrationTests().catch(error => {
    console.error('💥 Test runner failed:', error);
    process.exit(1);
  });
}

module.exports = { IntegrationTester, runIntegrationTests };
