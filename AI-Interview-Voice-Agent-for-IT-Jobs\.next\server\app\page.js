/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "(rsc)/./app/_components/ClientBodyWrapper.jsx":
/*!***********************************************!*\
  !*** ./app/_components/ClientBodyWrapper.jsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\_components\\\\ClientBodyWrapper.jsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\Voice-Agent-for-ITjobs\\AI-Interview-Voice-Agent-for-IT-Jobs\\app\\_components\\ClientBodyWrapper.jsx",
"default",
));


/***/ }),

/***/ "(rsc)/./app/_components/DatabaseInitializer.jsx":
/*!*************************************************!*\
  !*** ./app/_components/DatabaseInitializer.jsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\_components\\\\DatabaseInitializer.jsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\Voice-Agent-for-ITjobs\\AI-Interview-Voice-Agent-for-IT-Jobs\\app\\_components\\DatabaseInitializer.jsx",
"default",
));


/***/ }),

/***/ "(rsc)/./app/_components/HydrationProvider.jsx":
/*!***********************************************!*\
  !*** ./app/_components/HydrationProvider.jsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\_components\\\\HydrationProvider.jsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\Voice-Agent-for-ITjobs\\AI-Interview-Voice-Agent-for-IT-Jobs\\app\\_components\\HydrationProvider.jsx",
"default",
));


/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"d6bff50c718b\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcaHBcXERlc2t0b3BcXFZvaWNlLUFnZW50LWZvci1JVGpvYnNcXEFJLUludGVydmlldy1Wb2ljZS1BZ2VudC1mb3ItSVQtSm9ic1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImQ2YmZmNTBjNzE4YlwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.js":
/*!***********************!*\
  !*** ./app/layout.js ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_js_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.js\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.js\\\",\\\"import\\\":\\\"Geist\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-sans\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistSans\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_js_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_js_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_js_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.js\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.js\\\",\\\"import\\\":\\\"Geist_Mono\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-mono\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_js_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_js_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var _provider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./provider */ \"(rsc)/./app/provider.jsx\");\n/* harmony import */ var _components_ui_sonner__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/sonner */ \"(rsc)/./components/ui/sonner.jsx\");\n/* harmony import */ var _components_DatabaseInitializer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./_components/DatabaseInitializer */ \"(rsc)/./app/_components/DatabaseInitializer.jsx\");\n/* harmony import */ var _components_ClientBodyWrapper__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./_components/ClientBodyWrapper */ \"(rsc)/./app/_components/ClientBodyWrapper.jsx\");\n/* harmony import */ var _components_HydrationProvider__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./_components/HydrationProvider */ \"(rsc)/./app/_components/HydrationProvider.jsx\");\n\n\n\n\n\n\n\n\n\nconst metadata = {\n    title: \"AI Interview Schedule Voice Agent\",\n    description: \"Generated by create next app\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_app_layout_js_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_7___default().variable)} ${(next_font_google_target_css_path_app_layout_js_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_8___default().variable)} antialiased`,\n            suppressHydrationWarning: true,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_HydrationProvider__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ClientBodyWrapper__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_provider__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DatabaseInitializer__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\layout.js\",\n                                lineNumber: 36,\n                                columnNumber: 15\n                            }, this),\n                            children,\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sonner__WEBPACK_IMPORTED_MODULE_3__.Toaster, {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\layout.js\",\n                                lineNumber: 38,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\layout.js\",\n                        lineNumber: 35,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\layout.js\",\n                    lineNumber: 34,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\layout.js\",\n                lineNumber: 33,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\layout.js\",\n            lineNumber: 29,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\layout.js\",\n        lineNumber: 28,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.js\n");

/***/ }),

/***/ "(rsc)/./app/page.js":
/*!*********************!*\
  !*** ./app/page.js ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\page.js\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\Voice-Agent-for-ITjobs\\AI-Interview-Voice-Agent-for-IT-Jobs\\app\\page.js",
"default",
));


/***/ }),

/***/ "(rsc)/./app/provider.jsx":
/*!**************************!*\
  !*** ./app/provider.jsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__),
/* harmony export */   useUser: () => (/* binding */ useUser)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\provider.jsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\Voice-Agent-for-ITjobs\\AI-Interview-Voice-Agent-for-IT-Jobs\\app\\provider.jsx",
"default",
));
const useUser = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useUser() from the server but useUser is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\Voice-Agent-for-ITjobs\\AI-Interview-Voice-Agent-for-IT-Jobs\\app\\provider.jsx",
"useUser",
);

/***/ }),

/***/ "(rsc)/./components/ui/sonner.jsx":
/*!**********************************!*\
  !*** ./components/ui/sonner.jsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Toaster: () => (/* binding */ Toaster)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const Toaster = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call Toaster() from the server but Toaster is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\Voice-Agent-for-ITjobs\\AI-Interview-Voice-Agent-for-IT-Jobs\\components\\ui\\sonner.jsx",
"Toaster",
);

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.js&appDir=C%3A%5CUsers%5Chp%5CDesktop%5CVoice-Agent-for-ITjobs%5CAI-Interview-Voice-Agent-for-IT-Jobs%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Chp%5CDesktop%5CVoice-Agent-for-ITjobs%5CAI-Interview-Voice-Agent-for-IT-Jobs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.js&appDir=C%3A%5CUsers%5Chp%5CDesktop%5CVoice-Agent-for-ITjobs%5CAI-Interview-Voice-Agent-for-IT-Jobs%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Chp%5CDesktop%5CVoice-Agent-for-ITjobs%5CAI-Interview-Voice-Agent-for-IT-Jobs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.js */ \"(rsc)/./app/layout.js\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.js */ \"(rsc)/./app/page.js\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\page.js\"],\n          metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n        }]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\layout.js\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\page.js\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.js&appDir=C%3A%5CUsers%5Chp%5CDesktop%5CVoice-Agent-for-ITjobs%5CAI-Interview-Voice-Agent-for-IT-Jobs%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Chp%5CDesktop%5CVoice-Agent-for-ITjobs%5CAI-Interview-Voice-Agent-for-IT-Jobs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5CAI-Interview-Voice-Agent-for-IT-Jobs%5C%5Capp%5C%5C_components%5C%5CClientBodyWrapper.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5CAI-Interview-Voice-Agent-for-IT-Jobs%5C%5Capp%5C%5C_components%5C%5CDatabaseInitializer.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5CAI-Interview-Voice-Agent-for-IT-Jobs%5C%5Capp%5C%5C_components%5C%5CHydrationProvider.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5CAI-Interview-Voice-Agent-for-IT-Jobs%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5CAI-Interview-Voice-Agent-for-IT-Jobs%5C%5Capp%5C%5Cprovider.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5CAI-Interview-Voice-Agent-for-IT-Jobs%5C%5Ccomponents%5C%5Cui%5C%5Csonner.jsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5CAI-Interview-Voice-Agent-for-IT-Jobs%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5CAI-Interview-Voice-Agent-for-IT-Jobs%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5CAI-Interview-Voice-Agent-for-IT-Jobs%5C%5Capp%5C%5C_components%5C%5CClientBodyWrapper.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5CAI-Interview-Voice-Agent-for-IT-Jobs%5C%5Capp%5C%5C_components%5C%5CDatabaseInitializer.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5CAI-Interview-Voice-Agent-for-IT-Jobs%5C%5Capp%5C%5C_components%5C%5CHydrationProvider.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5CAI-Interview-Voice-Agent-for-IT-Jobs%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5CAI-Interview-Voice-Agent-for-IT-Jobs%5C%5Capp%5C%5Cprovider.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5CAI-Interview-Voice-Agent-for-IT-Jobs%5C%5Ccomponents%5C%5Cui%5C%5Csonner.jsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5CAI-Interview-Voice-Agent-for-IT-Jobs%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5CAI-Interview-Voice-Agent-for-IT-Jobs%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/_components/ClientBodyWrapper.jsx */ \"(rsc)/./app/_components/ClientBodyWrapper.jsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/_components/DatabaseInitializer.jsx */ \"(rsc)/./app/_components/DatabaseInitializer.jsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/_components/HydrationProvider.jsx */ \"(rsc)/./app/_components/HydrationProvider.jsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/provider.jsx */ \"(rsc)/./app/provider.jsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/ui/sonner.jsx */ \"(rsc)/./components/ui/sonner.jsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5CAI-Interview-Voice-Agent-for-IT-Jobs%5C%5Capp%5C%5C_components%5C%5CClientBodyWrapper.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5CAI-Interview-Voice-Agent-for-IT-Jobs%5C%5Capp%5C%5C_components%5C%5CDatabaseInitializer.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5CAI-Interview-Voice-Agent-for-IT-Jobs%5C%5Capp%5C%5C_components%5C%5CHydrationProvider.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5CAI-Interview-Voice-Agent-for-IT-Jobs%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5CAI-Interview-Voice-Agent-for-IT-Jobs%5C%5Capp%5C%5Cprovider.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5CAI-Interview-Voice-Agent-for-IT-Jobs%5C%5Ccomponents%5C%5Cui%5C%5Csonner.jsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5CAI-Interview-Voice-Agent-for-IT-Jobs%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5CAI-Interview-Voice-Agent-for-IT-Jobs%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5CAI-Interview-Voice-Agent-for-IT-Jobs%5C%5Capp%5C%5Cpage.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5CAI-Interview-Voice-Agent-for-IT-Jobs%5C%5Capp%5C%5Cpage.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.js */ \"(rsc)/./app/page.js\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2hwJTVDJTVDRGVza3RvcCU1QyU1Q1ZvaWNlLUFnZW50LWZvci1JVGpvYnMlNUMlNUNBSS1JbnRlcnZpZXctVm9pY2UtQWdlbnQtZm9yLUlULUpvYnMlNUMlNUNhcHAlNUMlNUNwYWdlLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxzSUFBdUkiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXGhwXFxcXERlc2t0b3BcXFxcVm9pY2UtQWdlbnQtZm9yLUlUam9ic1xcXFxBSS1JbnRlcnZpZXctVm9pY2UtQWdlbnQtZm9yLUlULUpvYnNcXFxcYXBwXFxcXHBhZ2UuanNcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5CAI-Interview-Voice-Agent-for-IT-Jobs%5C%5Capp%5C%5Cpage.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5CAI-Interview-Voice-Agent-for-IT-Jobs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5CAI-Interview-Voice-Agent-for-IT-Jobs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5CAI-Interview-Voice-Agent-for-IT-Jobs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5CAI-Interview-Voice-Agent-for-IT-Jobs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5CAI-Interview-Voice-Agent-for-IT-Jobs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5CAI-Interview-Voice-Agent-for-IT-Jobs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5CAI-Interview-Voice-Agent-for-IT-Jobs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5CAI-Interview-Voice-Agent-for-IT-Jobs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5CAI-Interview-Voice-Agent-for-IT-Jobs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5CAI-Interview-Voice-Agent-for-IT-Jobs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5CAI-Interview-Voice-Agent-for-IT-Jobs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5CAI-Interview-Voice-Agent-for-IT-Jobs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5CAI-Interview-Voice-Agent-for-IT-Jobs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5CAI-Interview-Voice-Agent-for-IT-Jobs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5CAI-Interview-Voice-Agent-for-IT-Jobs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5CAI-Interview-Voice-Agent-for-IT-Jobs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5CAI-Interview-Voice-Agent-for-IT-Jobs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5CAI-Interview-Voice-Agent-for-IT-Jobs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5CAI-Interview-Voice-Agent-for-IT-Jobs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5CAI-Interview-Voice-Agent-for-IT-Jobs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5CAI-Interview-Voice-Agent-for-IT-Jobs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5CAI-Interview-Voice-Agent-for-IT-Jobs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5CAI-Interview-Voice-Agent-for-IT-Jobs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5CAI-Interview-Voice-Agent-for-IT-Jobs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__":
/*!**********************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__ ***!
  \**********************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9hcHAvZmF2aWNvbi5pY28/X19uZXh0X21ldGFkYXRhX18iLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQUEsRUFBaUY7O0FBRWpGLEVBQUUsaUVBQWU7QUFDakIsdUJBQXVCO0FBQ3ZCLHFCQUFxQiw4RkFBbUI7O0FBRXhDO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxocFxcRGVza3RvcFxcVm9pY2UtQWdlbnQtZm9yLUlUam9ic1xcQUktSW50ZXJ2aWV3LVZvaWNlLUFnZW50LWZvci1JVC1Kb2JzXFxhcHBcXGZhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIl0sInNvdXJjZXNDb250ZW50IjpbIiAgaW1wb3J0IHsgZmlsbE1ldGFkYXRhU2VnbWVudCB9IGZyb20gJ25leHQvZGlzdC9saWIvbWV0YWRhdGEvZ2V0LW1ldGFkYXRhLXJvdXRlJ1xuXG4gIGV4cG9ydCBkZWZhdWx0IGFzeW5jIChwcm9wcykgPT4ge1xuICAgIGNvbnN0IGltYWdlRGF0YSA9IHtcInR5cGVcIjpcImltYWdlL3gtaWNvblwiLFwic2l6ZXNcIjpcIjE2eDE2XCJ9XG4gICAgY29uc3QgaW1hZ2VVcmwgPSBmaWxsTWV0YWRhdGFTZWdtZW50KFwiLlwiLCBhd2FpdCBwcm9wcy5wYXJhbXMsIFwiZmF2aWNvbi5pY29cIilcblxuICAgIHJldHVybiBbe1xuICAgICAgLi4uaW1hZ2VEYXRhLFxuICAgICAgdXJsOiBpbWFnZVVybCArIFwiXCIsXG4gICAgfV1cbiAgfSJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(ssr)/./app/_components/ClientBodyWrapper.jsx":
/*!***********************************************!*\
  !*** ./app/_components/ClientBodyWrapper.jsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ClientBodyWrapper)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _hooks_useHydrationFix__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../hooks/useHydrationFix */ \"(ssr)/./hooks/useHydrationFix.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction ClientBodyWrapper({ children }) {\n    // This hook handles all the hydration mismatch issues\n    (0,_hooks_useHydrationFix__WEBPACK_IMPORTED_MODULE_1__.useHydrationFix)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvX2NvbXBvbmVudHMvQ2xpZW50Qm9keVdyYXBwZXIuanN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBRThEO0FBRS9DLFNBQVNDLGtCQUFrQixFQUFFQyxRQUFRLEVBQUU7SUFDcEQsc0RBQXNEO0lBQ3RERix1RUFBZUE7SUFFZixxQkFBTztrQkFBR0U7O0FBQ1oiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcaHBcXERlc2t0b3BcXFZvaWNlLUFnZW50LWZvci1JVGpvYnNcXEFJLUludGVydmlldy1Wb2ljZS1BZ2VudC1mb3ItSVQtSm9ic1xcYXBwXFxfY29tcG9uZW50c1xcQ2xpZW50Qm9keVdyYXBwZXIuanN4Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiXG5cbmltcG9ydCB7IHVzZUh5ZHJhdGlvbkZpeCB9IGZyb20gJy4uLy4uL2hvb2tzL3VzZUh5ZHJhdGlvbkZpeCc7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIENsaWVudEJvZHlXcmFwcGVyKHsgY2hpbGRyZW4gfSkge1xuICAvLyBUaGlzIGhvb2sgaGFuZGxlcyBhbGwgdGhlIGh5ZHJhdGlvbiBtaXNtYXRjaCBpc3N1ZXNcbiAgdXNlSHlkcmF0aW9uRml4KCk7XG5cbiAgcmV0dXJuIDw+e2NoaWxkcmVufTwvPjtcbn1cbiJdLCJuYW1lcyI6WyJ1c2VIeWRyYXRpb25GaXgiLCJDbGllbnRCb2R5V3JhcHBlciIsImNoaWxkcmVuIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./app/_components/ClientBodyWrapper.jsx\n");

/***/ }),

/***/ "(ssr)/./app/_components/DatabaseInitializer.jsx":
/*!*************************************************!*\
  !*** ./app/_components/DatabaseInitializer.jsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DatabaseInitializer)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _services_supabaseClient__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/services/supabaseClient */ \"(ssr)/./services/supabaseClient.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction DatabaseInitializer() {\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"DatabaseInitializer.useEffect\": ()=>{\n            const initializeDatabase = {\n                \"DatabaseInitializer.useEffect.initializeDatabase\": async ()=>{\n                    console.log('Checking database tables...');\n                    try {\n                        // Check if Interviews table exists and has the is_guest column\n                        const { data: interviewsData, error: interviewsError } = await _services_supabaseClient__WEBPACK_IMPORTED_MODULE_1__.supabase.from('Interviews').select('count(*)').limit(1).single();\n                        if (interviewsError && interviewsError.code === '42P01') {\n                            console.log('Interviews table does not exist, creating it...');\n                            await createInterviewsTable();\n                        } else {\n                            console.log('Interviews table exists');\n                            // Check if is_guest column exists\n                            await checkAndAddGuestColumn();\n                        }\n                        // Check if Companies table exists and create it if needed\n                        const { data: companiesData, error: companiesError } = await _services_supabaseClient__WEBPACK_IMPORTED_MODULE_1__.supabase.from('Companies').select('count(*)').limit(1).single();\n                        if (companiesError && companiesError.code === '42P01') {\n                            console.log('Companies table does not exist, creating it...');\n                            await createCompaniesTable();\n                        } else {\n                            console.log('Companies table exists');\n                        }\n                        // Check if Jobs table exists and create it if needed\n                        const { data: jobsData, error: jobsError } = await _services_supabaseClient__WEBPACK_IMPORTED_MODULE_1__.supabase.from('Jobs').select('count(*)').limit(1).single();\n                        if (jobsError && jobsError.code === '42P01') {\n                            console.log('Jobs table does not exist, creating it...');\n                            await createJobsTable();\n                        } else {\n                            console.log('Jobs table exists');\n                        }\n                        // Check if Job_Submissions table exists and create it if needed\n                        const { data: submissionsData, error: submissionsError } = await _services_supabaseClient__WEBPACK_IMPORTED_MODULE_1__.supabase.from('Job_Submissions').select('count(*)').limit(1).single();\n                        if (submissionsError && submissionsError.code === '42P01') {\n                            console.log('Job_Submissions table does not exist, creating it...');\n                            await createJobSubmissionsTable();\n                        } else {\n                            console.log('Job_Submissions table exists');\n                        }\n                        console.log('Database initialization complete');\n                    } catch (error) {\n                        console.error('Error initializing database:', error);\n                    }\n                }\n            }[\"DatabaseInitializer.useEffect.initializeDatabase\"];\n            // Create Companies table\n            const createCompaniesTable = {\n                \"DatabaseInitializer.useEffect.createCompaniesTable\": async ()=>{\n                    const { error } = await _services_supabaseClient__WEBPACK_IMPORTED_MODULE_1__.supabase.rpc('create_companies_table');\n                    if (error) {\n                        console.error('Error creating Companies table:', error);\n                        // If RPC fails, try direct SQL (this requires more permissions)\n                        console.log('Attempting to create Companies table directly...');\n                        await createCompaniesTableDirect();\n                    }\n                }\n            }[\"DatabaseInitializer.useEffect.createCompaniesTable\"];\n            // Create Jobs table\n            const createJobsTable = {\n                \"DatabaseInitializer.useEffect.createJobsTable\": async ()=>{\n                    const { error } = await _services_supabaseClient__WEBPACK_IMPORTED_MODULE_1__.supabase.rpc('create_jobs_table');\n                    if (error) {\n                        console.error('Error creating Jobs table:', error);\n                        // If RPC fails, try direct SQL (this requires more permissions)\n                        console.log('Attempting to create Jobs table directly...');\n                        await createJobsTableDirect();\n                    }\n                }\n            }[\"DatabaseInitializer.useEffect.createJobsTable\"];\n            // Create Job_Submissions table\n            const createJobSubmissionsTable = {\n                \"DatabaseInitializer.useEffect.createJobSubmissionsTable\": async ()=>{\n                    const { error } = await _services_supabaseClient__WEBPACK_IMPORTED_MODULE_1__.supabase.rpc('create_job_submissions_table');\n                    if (error) {\n                        console.error('Error creating Job_Submissions table:', error);\n                        // If RPC fails, try direct SQL (this requires more permissions)\n                        console.log('Attempting to create Job_Submissions table directly...');\n                        await createJobSubmissionsTableDirect();\n                    }\n                }\n            }[\"DatabaseInitializer.useEffect.createJobSubmissionsTable\"];\n            // Direct SQL methods (these require more permissions)\n            const createCompaniesTableDirect = {\n                \"DatabaseInitializer.useEffect.createCompaniesTableDirect\": async ()=>{\n                    // This is a fallback method that requires more permissions\n                    console.log('Please create the Companies table manually in Supabase');\n                    alert('Database setup required: Please create the Companies table in Supabase');\n                }\n            }[\"DatabaseInitializer.useEffect.createCompaniesTableDirect\"];\n            const createJobsTableDirect = {\n                \"DatabaseInitializer.useEffect.createJobsTableDirect\": async ()=>{\n                    // This is a fallback method that requires more permissions\n                    console.log('Please create the Jobs table manually in Supabase');\n                    alert('Database setup required: Please create the Jobs table in Supabase');\n                }\n            }[\"DatabaseInitializer.useEffect.createJobsTableDirect\"];\n            const createJobSubmissionsTableDirect = {\n                \"DatabaseInitializer.useEffect.createJobSubmissionsTableDirect\": async ()=>{\n                    // This is a fallback method that requires more permissions\n                    console.log('Please create the Job_Submissions table manually in Supabase');\n                    alert('Database setup required: Please create the Job_Submissions table in Supabase');\n                }\n            }[\"DatabaseInitializer.useEffect.createJobSubmissionsTableDirect\"];\n            // Check and add guest column to Interviews table\n            const checkAndAddGuestColumn = {\n                \"DatabaseInitializer.useEffect.checkAndAddGuestColumn\": async ()=>{\n                    try {\n                        // Try to select is_guest column to see if it exists\n                        const { data, error } = await _services_supabaseClient__WEBPACK_IMPORTED_MODULE_1__.supabase.from('Interviews').select('is_guest').limit(1);\n                        if (error && error.message.includes('column \"is_guest\" does not exist')) {\n                            console.log('Adding is_guest column to Interviews table...');\n                            // This would require admin privileges, so we'll just log it\n                            console.log('Please run the fix_guest_interviews.sql script in your Supabase dashboard');\n                        } else {\n                            console.log('is_guest column exists in Interviews table');\n                        }\n                    } catch (err) {\n                        console.log('Could not check is_guest column:', err.message);\n                    }\n                }\n            }[\"DatabaseInitializer.useEffect.checkAndAddGuestColumn\"];\n            // Create Interviews table\n            const createInterviewsTable = {\n                \"DatabaseInitializer.useEffect.createInterviewsTable\": async ()=>{\n                    console.log('Please create the Interviews table manually in Supabase using the database_fix.sql script');\n                }\n            }[\"DatabaseInitializer.useEffect.createInterviewsTable\"];\n            // Run the initialization\n            initializeDatabase();\n        }\n    }[\"DatabaseInitializer.useEffect\"], []);\n    // This component doesn't render anything\n    return null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/_components/DatabaseInitializer.jsx\n");

/***/ }),

/***/ "(ssr)/./app/_components/HydrationProvider.jsx":
/*!***********************************************!*\
  !*** ./app/_components/HydrationProvider.jsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HydrationProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n/**\n * HydrationProvider - Ensures consistent hydration across the app\n * This component prevents hydration mismatches by ensuring the client\n * and server render the same content initially\n */ function HydrationProvider({ children }) {\n    const [isHydrated, setIsHydrated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HydrationProvider.useEffect\": ()=>{\n            // Mark as hydrated after the first client-side render\n            setIsHydrated(true);\n            // Load development configuration in development mode\n            if (true) {\n                __webpack_require__.e(/*! import() */ \"_ssr_lib_dev-config_js\").then(__webpack_require__.t.bind(__webpack_require__, /*! @/lib/dev-config */ \"(ssr)/./lib/dev-config.js\", 23)).catch({\n                    \"HydrationProvider.useEffect\": ()=>{\n                    // Ignore import errors in case the file doesn't exist\n                    }\n                }[\"HydrationProvider.useEffect\"]);\n            }\n            // Clean up any browser extension attributes that might cause hydration issues\n            const cleanupBrowserExtensions = {\n                \"HydrationProvider.useEffect.cleanupBrowserExtensions\": ()=>{\n                    if (typeof document === 'undefined') return;\n                    const body = document.body;\n                    const html = document.documentElement;\n                    // List of problematic attributes added by browser extensions\n                    const problematicAttributes = [\n                        'cz-shortcut-listen',\n                        'data-new-gr-c-s-check-loaded',\n                        'data-gr-ext-installed',\n                        'data-gramm',\n                        'data-gramm_editor',\n                        'data-enable-grammarly',\n                        'spellcheck',\n                        'data-ms-editor',\n                        'data-lt-installed',\n                        'data-darkreader-mode',\n                        'data-darkreader-scheme',\n                        'data-adblock',\n                        'data-honey-extension',\n                        'data-lastpass',\n                        'data-bitwarden',\n                        'data-dashlane',\n                        'data-1password',\n                        'data-rh-extension',\n                        'data-keeper',\n                        'data-nordpass'\n                    ];\n                    // Clean body attributes\n                    problematicAttributes.forEach({\n                        \"HydrationProvider.useEffect.cleanupBrowserExtensions\": (attr)=>{\n                            if (body.hasAttribute(attr)) {\n                                body.removeAttribute(attr);\n                            }\n                            if (html.hasAttribute(attr)) {\n                                html.removeAttribute(attr);\n                            }\n                        }\n                    }[\"HydrationProvider.useEffect.cleanupBrowserExtensions\"]);\n                    // Also remove any data-* attributes that look like extension attributes\n                    [\n                        body,\n                        html\n                    ].forEach({\n                        \"HydrationProvider.useEffect.cleanupBrowserExtensions\": (element)=>{\n                            Array.from(element.attributes).forEach({\n                                \"HydrationProvider.useEffect.cleanupBrowserExtensions\": (attr)=>{\n                                    if (attr.name.startsWith('data-') && (attr.name.includes('extension') || attr.name.includes('plugin') || attr.name.includes('addon') || attr.name.includes('ext-') || attr.name.includes('-ext'))) {\n                                        element.removeAttribute(attr.name);\n                                    }\n                                }\n                            }[\"HydrationProvider.useEffect.cleanupBrowserExtensions\"]);\n                        }\n                    }[\"HydrationProvider.useEffect.cleanupBrowserExtensions\"]);\n                }\n            }[\"HydrationProvider.useEffect.cleanupBrowserExtensions\"];\n            // Clean up immediately\n            cleanupBrowserExtensions();\n            // Set up a mutation observer to handle dynamically added attributes\n            const observer = new MutationObserver({\n                \"HydrationProvider.useEffect\": (mutations)=>{\n                    mutations.forEach({\n                        \"HydrationProvider.useEffect\": (mutation)=>{\n                            if (mutation.type === 'attributes') {\n                                const target = mutation.target;\n                                const attributeName = mutation.attributeName;\n                                // Only process body and html elements\n                                if (target !== document.body && target !== document.documentElement) {\n                                    return;\n                                }\n                                // Check if it's a problematic extension attribute\n                                if (attributeName && (attributeName.startsWith('cz-') || attributeName.startsWith('data-gr-') || attributeName.startsWith('data-gramm') || attributeName.startsWith('data-ms-') || attributeName.startsWith('data-lt-') || attributeName.startsWith('data-darkreader') || attributeName.startsWith('data-rh-') || attributeName.includes('extension') || attributeName.includes('plugin') || attributeName.includes('addon') || attributeName.includes('ext-') || attributeName.includes('-ext'))) {\n                                    target.removeAttribute(attributeName);\n                                }\n                            }\n                        }\n                    }[\"HydrationProvider.useEffect\"]);\n                }\n            }[\"HydrationProvider.useEffect\"]);\n            // Observe both body and html elements\n            observer.observe(document.body, {\n                attributes: true,\n                attributeOldValue: true\n            });\n            observer.observe(document.documentElement, {\n                attributes: true,\n                attributeOldValue: true\n            });\n            // Cleanup function\n            return ({\n                \"HydrationProvider.useEffect\": ()=>{\n                    observer.disconnect();\n                }\n            })[\"HydrationProvider.useEffect\"];\n        }\n    }[\"HydrationProvider.useEffect\"], []);\n    // Suppress hydration warnings for the initial render\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        suppressHydrationWarning: true,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\_components\\\\HydrationProvider.jsx\",\n        lineNumber: 135,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/_components/HydrationProvider.jsx\n");

/***/ }),

/***/ "(ssr)/./app/page.js":
/*!*********************!*\
  !*** ./app/page.js ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./components/ui/button.jsx\");\n/* harmony import */ var _services_supabaseClient__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/services/supabaseClient */ \"(ssr)/./services/supabaseClient.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Clock_Mic_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Clock,Mic,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Clock_Mic_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Clock,Mic,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Clock_Mic_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Clock,Mic,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Clock_Mic_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Clock,Mic,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Clock_Mic_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Clock,Mic,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mic.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _provider__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./provider */ \"(ssr)/./app/provider.jsx\");\n/* harmony import */ var _components_magicui_hero_video_dialog__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/magicui/hero-video-dialog */ \"(ssr)/./components/magicui/hero-video-dialog.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\nfunction Home() {\n    const { user } = (0,_provider__WEBPACK_IMPORTED_MODULE_6__.useUser)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"border-b w-full\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \" px-10 flex h-16 items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                src: '/logo2.png',\n                                alt: \"logo\",\n                                width: 140,\n                                height: 200\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\page.js\",\n                                lineNumber: 20,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\page.js\",\n                            lineNumber: 19,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"hidden md:flex items-center gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                    href: \"#features\",\n                                    className: \"text-sm font-medium hover:underline\",\n                                    children: \"Features\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\page.js\",\n                                    lineNumber: 23,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                    href: \"#how-it-works\",\n                                    className: \"text-sm font-medium hover:underline\",\n                                    children: \"How It Works\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\page.js\",\n                                    lineNumber: 26,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                    href: \"#pricing\",\n                                    className: \"text-sm font-medium hover:underline\",\n                                    children: \"Pricing\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\page.js\",\n                                    lineNumber: 29,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                    href: \"/jobs\",\n                                    className: \"text-sm font-medium hover:underline\",\n                                    children: \"Find Jobs\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\page.js\",\n                                    lineNumber: 32,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\page.js\",\n                            lineNumber: 22,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                    href: '/jobs',\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                        variant: \"outline\",\n                                        children: \"Find Jobs\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\page.js\",\n                                        lineNumber: 38,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\page.js\",\n                                    lineNumber: 37,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                    href: '/dashboard',\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                        children: \"Dashboard\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\page.js\",\n                                        lineNumber: 41,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\page.js\",\n                                    lineNumber: 40,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\page.js\",\n                            lineNumber: 36,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\page.js\",\n                    lineNumber: 18,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\page.js\",\n                lineNumber: 17,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"flex-1 items-center justify-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-20 flex items-center justify-center w-full md:py-28 bg-gradient-to-b from-blue-50 to-white\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container px-4 md:px-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid gap-6 lg:grid-cols-2 lg:gap-12 items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col justify-center space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl\",\n                                                        children: [\n                                                            \"AI-Powered \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-primary\",\n                                                                children: \" Interview Assistant \"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\page.js\",\n                                                                lineNumber: 55,\n                                                                columnNumber: 32\n                                                            }, this),\n                                                            \" for Modern Recruiters\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\page.js\",\n                                                        lineNumber: 54,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-500 md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed\",\n                                                        children: \"Let our AI voice agent conduct candidate interviews while you focus on finding the perfect match. Save time, reduce bias, and improve your hiring process.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\page.js\",\n                                                        lineNumber: 57,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\page.js\",\n                                                lineNumber: 53,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col gap-2 min-[400px]:flex-row\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                                        size: \"lg\",\n                                                        className: \"bg-primary hover:bg-blue-700\",\n                                                        children: [\n                                                            \"Create Interview \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Clock_Mic_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                className: \"ml-2 h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\page.js\",\n                                                                lineNumber: 64,\n                                                                columnNumber: 38\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\page.js\",\n                                                        lineNumber: 63,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                                        size: \"lg\",\n                                                        variant: \"outline\",\n                                                        children: \"Join Us\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\page.js\",\n                                                        lineNumber: 66,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\page.js\",\n                                                lineNumber: 62,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\page.js\",\n                                        lineNumber: 52,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mx-auto lg:mx-0 relative\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative h-[350px] w-full overflow-hidden rounded-xl bg-gray-100 shadow-lg\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_magicui_hero_video_dialog__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: \"block dark:hidden\",\n                                                        thumbnailSrc: \"/image.png\",\n                                                        thumbnailAlt: \"Hero Video\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\page.js\",\n                                                        lineNumber: 74,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_magicui_hero_video_dialog__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: \"hidden dark:block\",\n                                                        thumbnailSrc: \"/image.png\",\n                                                        thumbnailAlt: \"Hero Video\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\page.js\",\n                                                        lineNumber: 79,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\page.js\",\n                                                lineNumber: 73,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\page.js\",\n                                            lineNumber: 72,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\page.js\",\n                                        lineNumber: 71,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\page.js\",\n                                lineNumber: 51,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\page.js\",\n                            lineNumber: 50,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\page.js\",\n                        lineNumber: 49,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        id: \"features\",\n                        className: \"py-16 md:py-24 flex items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container px-4 md:px-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col items-center justify-center space-y-4 text-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-3xl font-bold tracking-tighter md:text-4xl\",\n                                                children: \"Streamline Your Hiring Process\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\page.js\",\n                                                lineNumber: 96,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"mx-auto max-w-[700px] text-gray-500 md:text-xl/relaxed\",\n                                                children: \"Skillin helps you save time and find better candidates with our advanced AI interview technology.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\page.js\",\n                                                lineNumber: 97,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\page.js\",\n                                        lineNumber: 95,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\page.js\",\n                                    lineNumber: 94,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mx-auto grid max-w-5xl grid-cols-1 gap-6 md:grid-cols-3 md:gap-8 mt-12\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col items-center space-y-2 rounded-lg border p-6 shadow-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Clock_Mic_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"h-12 w-12 text-primary\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\page.js\",\n                                                    lineNumber: 104,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-xl font-bold\",\n                                                    children: \"Save Time\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\page.js\",\n                                                    lineNumber: 105,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-center text-gray-500\",\n                                                    children: \"Automate initial screening interviews and focus on final candidates.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\page.js\",\n                                                    lineNumber: 106,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\page.js\",\n                                            lineNumber: 103,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col items-center space-y-2 rounded-lg border p-6 shadow-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Clock_Mic_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"h-12 w-12 text-primary\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\page.js\",\n                                                    lineNumber: 111,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-xl font-bold\",\n                                                    children: \"Data-Driven Insights\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\page.js\",\n                                                    lineNumber: 112,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-center text-gray-500\",\n                                                    children: \"Get detailed analytics and candidate comparisons based on interview responses.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\page.js\",\n                                                    lineNumber: 113,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\page.js\",\n                                            lineNumber: 110,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col items-center space-y-2 rounded-lg border p-6 shadow-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Clock_Mic_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"h-12 w-12 text-primary\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\page.js\",\n                                                    lineNumber: 118,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-xl font-bold\",\n                                                    children: \"Reduce Bias\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\page.js\",\n                                                    lineNumber: 119,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-center text-gray-500\",\n                                                    children: \"Standardized interviews help eliminate unconscious bias in the hiring process.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\page.js\",\n                                                    lineNumber: 120,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\page.js\",\n                                            lineNumber: 117,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\page.js\",\n                                    lineNumber: 102,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\page.js\",\n                            lineNumber: 93,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\page.js\",\n                        lineNumber: 92,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        id: \"how-it-works\",\n                        className: \"flex items-center justify-center py-16 md:py-24 bg-gray-50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container px-4 md:px-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col items-center justify-center space-y-4 text-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-3xl font-bold tracking-tighter md:text-4xl\",\n                                                children: \"How Skillin Works\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\page.js\",\n                                                lineNumber: 133,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"mx-auto max-w-[700px] text-gray-500 md:text-xl/relaxed\",\n                                                children: \"Three simple steps to transform your recruitment process\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\page.js\",\n                                                lineNumber: 134,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\page.js\",\n                                        lineNumber: 132,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\page.js\",\n                                    lineNumber: 131,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mx-auto grid max-w-5xl grid-cols-1 gap-8 md:grid-cols-3 mt-12\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col items-center space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex h-14 w-14 items-center justify-center rounded-full bg-blue-100 text-blue-900\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xl font-bold\",\n                                                        children: \"1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\page.js\",\n                                                        lineNumber: 142,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\page.js\",\n                                                    lineNumber: 141,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-xl font-bold\",\n                                                    children: \"Create Interview\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\page.js\",\n                                                    lineNumber: 144,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-center text-gray-500\",\n                                                    children: \"Set up your job requirements and customize interview questions.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\page.js\",\n                                                    lineNumber: 145,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\page.js\",\n                                            lineNumber: 140,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col items-center space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex h-14 w-14 items-center justify-center rounded-full bg-blue-100 text-blue-900\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xl font-bold\",\n                                                        children: \"2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\page.js\",\n                                                        lineNumber: 151,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\page.js\",\n                                                    lineNumber: 150,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-xl font-bold\",\n                                                    children: \"Share with Candidates\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\page.js\",\n                                                    lineNumber: 153,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-center text-gray-500\",\n                                                    children: \"Send interview links to candidates to complete at their convenience.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\page.js\",\n                                                    lineNumber: 154,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\page.js\",\n                                            lineNumber: 149,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col items-center space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex h-14 w-14 items-center justify-center rounded-full bg-blue-100 text-blue-900\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xl font-bold\",\n                                                        children: \"3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\page.js\",\n                                                        lineNumber: 160,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\page.js\",\n                                                    lineNumber: 159,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-xl font-bold\",\n                                                    children: \"Review Results\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\page.js\",\n                                                    lineNumber: 162,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-center text-gray-500\",\n                                                    children: \"Get AI-analyzed results, transcripts, and candidate comparisons.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\page.js\",\n                                                    lineNumber: 163,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\page.js\",\n                                            lineNumber: 158,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\page.js\",\n                                    lineNumber: 139,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\page.js\",\n                            lineNumber: 130,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\page.js\",\n                        lineNumber: 129,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-16 flex items-center justify-center md:py-24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container px-4 md:px-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col items-center justify-center space-y-4 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-3xl font-bold tracking-tighter md:text-4xl\",\n                                                children: \"Ready to Transform Your Hiring Process?\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\page.js\",\n                                                lineNumber: 176,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"mx-auto max-w-[700px] text-gray-500 md:text-xl/relaxed\",\n                                                children: \"Join hundreds of companies already using Skillin to find the best talent.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\page.js\",\n                                                lineNumber: 179,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\page.js\",\n                                        lineNumber: 175,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col gap-2 min-[400px]:flex-row\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                                size: \"lg\",\n                                                className: \"bg-primary hover:bg-blue-700\",\n                                                children: \"Get Started for Free\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\page.js\",\n                                                lineNumber: 184,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                                size: \"lg\",\n                                                variant: \"outline\",\n                                                children: \"Schedule a Demo\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\page.js\",\n                                                lineNumber: 187,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\page.js\",\n                                        lineNumber: 183,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\page.js\",\n                                lineNumber: 174,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\page.js\",\n                            lineNumber: 173,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\page.js\",\n                        lineNumber: 172,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\page.js\",\n                lineNumber: 47,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"border-t flex items-center justify-center bg-gray-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container flex flex-col gap-6 py-8 md:flex-row md:items-center md:justify-between md:py-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Clock_Mic_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"h-6 w-6 text-primary\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\page.js\",\n                                    lineNumber: 200,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xl font-bold\",\n                                    children: \"Skillin\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\page.js\",\n                                    lineNumber: 201,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\page.js\",\n                            lineNumber: 199,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"flex flex-wrap gap-4 md:gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                    href: \"#\",\n                                    className: \"text-sm hover:underline\",\n                                    children: \"Terms\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\page.js\",\n                                    lineNumber: 204,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                    href: \"#\",\n                                    className: \"text-sm hover:underline\",\n                                    children: \"Privacy\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\page.js\",\n                                    lineNumber: 207,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                    href: \"#\",\n                                    className: \"text-sm hover:underline\",\n                                    children: \"Contact\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\page.js\",\n                                    lineNumber: 210,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\page.js\",\n                            lineNumber: 203,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-gray-500\",\n                            children: \"\\xa9 2025 Skillin. All rights reserved.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\page.js\",\n                            lineNumber: 214,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\page.js\",\n                    lineNumber: 198,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\page.js\",\n                lineNumber: 197,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\page.js\",\n        lineNumber: 16,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/page.js\n");

/***/ }),

/***/ "(ssr)/./app/provider.jsx":
/*!**************************!*\
  !*** ./app/provider.jsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   useUser: () => (/* binding */ useUser)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _context_UserDetailContext__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/context/UserDetailContext */ \"(ssr)/./context/UserDetailContext.jsx\");\n/* harmony import */ var _services_supabaseClient__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/services/supabaseClient */ \"(ssr)/./services/supabaseClient.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _hooks_useHydrationFix__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useHydrationFix */ \"(ssr)/./hooks/useHydrationFix.js\");\n/* harmony import */ var _hooks_useLocalStorage__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/useLocalStorage */ \"(ssr)/./hooks/useLocalStorage.js\");\n/* __next_internal_client_entry_do_not_use__ default,useUser auto */ \n\n\n\n\n\nfunction Provider({ children }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)();\n    const [company, setCompany] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)();\n    const isClient = (0,_hooks_useHydrationFix__WEBPACK_IMPORTED_MODULE_4__.useIsClient)();\n    const [userType, setUserType] = (0,_hooks_useLocalStorage__WEBPACK_IMPORTED_MODULE_5__.useLocalStorage)('userType', 'client');\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"Provider.useEffect\": ()=>{\n            // This effect should only run once on component mount and only on client\n            if (!isClient) return;\n            // Set up auth state change listener\n            const { data: authListener } = _services_supabaseClient__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.onAuthStateChange({\n                \"Provider.useEffect\": async (event, session)=>{\n                    console.log(\"Auth state changed:\", event);\n                    if (event === 'SIGNED_OUT') {\n                        setUser(null);\n                        setCompany(null);\n                        setUserType('client'); // Reset to default\n                        if (isClient) {\n                            window.location.href = '/';\n                        }\n                    }\n                }\n            }[\"Provider.useEffect\"]);\n            // Clean up the listener\n            return ({\n                \"Provider.useEffect\": ()=>{\n                    if (authListener && authListener.subscription) {\n                        authListener.subscription.unsubscribe();\n                    }\n                }\n            })[\"Provider.useEffect\"];\n        }\n    }[\"Provider.useEffect\"], [\n        isClient\n    ]);\n    // Separate effect for checking user type and authentication\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"Provider.useEffect\": ()=>{\n            const checkUserAndType = {\n                \"Provider.useEffect.checkUserAndType\": async ()=>{\n                    if (!isClient) return;\n                    try {\n                        // Get current auth session\n                        const { data: { session } } = await _services_supabaseClient__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.getSession();\n                        if (!session) {\n                            // Not authenticated, don't do anything else\n                            return;\n                        }\n                        const { data: { user } } = await _services_supabaseClient__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.getUser();\n                        if (!user) return;\n                        console.log(\"User is authenticated:\", user.email);\n                        console.log(\"Current user type:\", userType);\n                        // User type is already managed by the useLocalStorage hook\n                        // Check if user exists in the appropriate table based on userType\n                        if (userType === \"company\") {\n                            console.log(\"Checking for company account with email:\", user.email);\n                            try {\n                                // Check if company exists - handle multiple rows case\n                                const { data: companyData, error } = await _services_supabaseClient__WEBPACK_IMPORTED_MODULE_2__.supabase.from('Companies').select('*').eq('email', user.email).limit(1);\n                                if (error) {\n                                    console.error(\"Error fetching company:\", error);\n                                    console.error(\"Full error details:\", JSON.stringify(error, null, 2));\n                                    // If table doesn't exist (42P01) or permission denied (42501), set temp company\n                                    if (error.code === '42P01' || error.code === '42501') {\n                                        console.log(\"Companies table not accessible, setting temporary company\");\n                                        setCompany({\n                                            id: 'temp-id',\n                                            email: user.email,\n                                            name: user.user_metadata?.name || 'Company Account',\n                                            is_onboarded: false,\n                                            industry_type: 'Technology'\n                                        });\n                                        return;\n                                    }\n                                    // Continue with company creation for other errors\n                                    console.log(\"Will attempt to create company despite fetch error\");\n                                    // Create a new company record if fetch failed\n                                    const { data: newCompany, error: insertError } = await _services_supabaseClient__WEBPACK_IMPORTED_MODULE_2__.supabase.from('Companies').insert([\n                                        {\n                                            email: user.email,\n                                            name: user.user_metadata?.name || 'Company Account',\n                                            picture: user.user_metadata?.picture || '',\n                                            is_onboarded: false,\n                                            industry_type: 'Technology',\n                                            created_at: new Date().toISOString()\n                                        }\n                                    ]).select();\n                                    if (insertError) {\n                                        console.error(\"Error creating company after fetch error:\", insertError);\n                                        // Set a minimal company object to prevent null references\n                                        setCompany({\n                                            id: 'temp-id',\n                                            email: user.email,\n                                            name: user.user_metadata?.name || 'Company Account',\n                                            is_onboarded: false\n                                        });\n                                        return;\n                                    }\n                                    if (newCompany && newCompany.length > 0) {\n                                        console.log(\"New company created after fetch error:\", newCompany[0]);\n                                        setCompany(newCompany[0]);\n                                    }\n                                    return;\n                                }\n                                if (companyData && companyData.length > 0) {\n                                    console.log(\"Found existing company:\", companyData[0]);\n                                    setCompany(companyData[0]);\n                                    // Auto-redirect to company dashboard if not already there\n                                    if (isClient && !window.location.pathname.includes('/company') && !window.location.pathname.includes('/auth')) {\n                                        console.log(\"Redirecting to company dashboard...\");\n                                        window.location.href = '/company/dashboard';\n                                    }\n                                } else {\n                                    console.log(\"No company found, creating new company for:\", user.email);\n                                    // Create a new company record if it doesn't exist\n                                    const { data: newCompany, error: insertError } = await _services_supabaseClient__WEBPACK_IMPORTED_MODULE_2__.supabase.from('Companies').insert([\n                                        {\n                                            email: user.email,\n                                            name: user.user_metadata?.name || 'Company Account',\n                                            picture: user.user_metadata?.picture || '',\n                                            is_onboarded: false,\n                                            industry_type: 'Technology',\n                                            created_at: new Date().toISOString()\n                                        }\n                                    ]).select();\n                                    if (insertError) {\n                                        console.error(\"Error creating company:\", insertError);\n                                        console.error(\"Full insert error details:\", JSON.stringify(insertError, null, 2));\n                                        // Set a minimal company object to prevent null references\n                                        setCompany({\n                                            id: 'temp-id',\n                                            email: user.email,\n                                            name: user.user_metadata?.name || 'Company Account',\n                                            is_onboarded: false\n                                        });\n                                        return;\n                                    }\n                                    if (newCompany && newCompany.length > 0) {\n                                        console.log(\"New company created:\", newCompany[0]);\n                                        setCompany(newCompany[0]);\n                                        // Auto-redirect to company dashboard for new company\n                                        if (isClient && !window.location.pathname.includes('/company') && !window.location.pathname.includes('/auth')) {\n                                            console.log(\"Redirecting new company to dashboard...\");\n                                            setTimeout({\n                                                \"Provider.useEffect.checkUserAndType\": ()=>{\n                                                    window.location.href = '/company/dashboard';\n                                                }\n                                            }[\"Provider.useEffect.checkUserAndType\"], 1000); // Small delay to ensure state is set\n                                        }\n                                    } else {\n                                        console.error(\"No company data returned after insert\");\n                                        // Set a minimal company object to prevent null references\n                                        setCompany({\n                                            id: 'temp-id',\n                                            email: user.email,\n                                            name: user.user_metadata?.name || 'Company Account',\n                                            is_onboarded: false\n                                        });\n                                    }\n                                }\n                            } catch (companyError) {\n                                console.error(\"Exception in company handling:\", companyError);\n                                // Set a minimal company object to prevent null references\n                                setCompany({\n                                    id: 'temp-id',\n                                    email: user.email,\n                                    name: user.user_metadata?.name || 'Company Account',\n                                    is_onboarded: false\n                                });\n                            }\n                        } else {\n                            // Default to client user type (handled by useLocalStorage hook)\n                            if (userType !== 'client') {\n                                setUserType('client');\n                            }\n                            // Check if user exists in Users table\n                            const { data: userData, error } = await _services_supabaseClient__WEBPACK_IMPORTED_MODULE_2__.supabase.from('Users').select('*').eq('email', user.email).maybeSingle();\n                            if (error) {\n                                console.error(\"Error fetching user:\", error);\n                                return;\n                            }\n                            if (userData) {\n                                console.log(\"Found existing user:\", userData);\n                                setUser(userData);\n                                // Auto-redirect to user dashboard if not already there\n                                if (false) {}\n                            } else {\n                                console.log(\"Creating new user for:\", user.email);\n                                // Create a new user record if it doesn't exist\n                                const { data: newUser, error: insertError } = await _services_supabaseClient__WEBPACK_IMPORTED_MODULE_2__.supabase.from('Users').insert([\n                                    {\n                                        name: user.user_metadata?.name || user.email,\n                                        email: user.email,\n                                        picture: user.user_metadata?.picture || '',\n                                        credits: 10\n                                    }\n                                ]).select();\n                                if (insertError) {\n                                    console.error(\"Error creating user:\", insertError);\n                                    return;\n                                }\n                                if (newUser && newUser.length > 0) {\n                                    console.log(\"New user created:\", newUser[0]);\n                                    setUser(newUser[0]);\n                                    // Auto-redirect to user dashboard for new user\n                                    if (false) {}\n                                }\n                            }\n                        }\n                    } catch (error) {\n                        console.error(\"Error in checkUserAndType:\", error);\n                    }\n                }\n            }[\"Provider.useEffect.checkUserAndType\"];\n            checkUserAndType();\n        }\n    }[\"Provider.useEffect\"], [\n        userType,\n        isClient\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_context_UserDetailContext__WEBPACK_IMPORTED_MODULE_1__.UserDetailContext.Provider, {\n        value: {\n            user,\n            setUser,\n            userType,\n            setUserType,\n            company,\n            setCompany,\n            isCompany: userType === \"company\"\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\provider.jsx\",\n            lineNumber: 288,\n            columnNumber: 13\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\provider.jsx\",\n        lineNumber: 279,\n        columnNumber: 9\n    }, this);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Provider);\nconst useUser = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_3__.useContext)(_context_UserDetailContext__WEBPACK_IMPORTED_MODULE_1__.UserDetailContext);\n    return context;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/provider.jsx\n");

/***/ }),

/***/ "(ssr)/./components/magicui/hero-video-dialog.jsx":
/*!**************************************************!*\
  !*** ./components/magicui/hero-video-dialog.jsx ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HeroVideoDialog)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_Play_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Play!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.js\");\n/* eslint-disable @next/next/no-img-element */ /* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction HeroVideoDialog({ thumbnailSrc, thumbnailAlt = \"Video thumbnail\", className }) {\n    // Static component with no video popup functionality\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"relative\", className),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"group relative\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                    src: thumbnailSrc,\n                    alt: thumbnailAlt,\n                    width: 1920,\n                    height: 1080,\n                    className: \"w-full rounded-md border shadow-lg\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\components\\\\magicui\\\\hero-video-dialog.jsx\",\n                    lineNumber: 15,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 flex items-center justify-center rounded-2xl\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex size-28 items-center justify-center rounded-full bg-primary/10 backdrop-blur-md\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative flex size-20 items-center justify-center rounded-full bg-gradient-to-b from-primary/30 to-primary shadow-md\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Play_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                className: \"size-8 fill-white text-white\",\n                                style: {\n                                    filter: \"drop-shadow(0 4px 3px rgb(0 0 0 / 0.07)) drop-shadow(0 2px 2px rgb(0 0 0 / 0.06))\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\components\\\\magicui\\\\hero-video-dialog.jsx\",\n                                lineNumber: 24,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\components\\\\magicui\\\\hero-video-dialog.jsx\",\n                            lineNumber: 23,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\components\\\\magicui\\\\hero-video-dialog.jsx\",\n                        lineNumber: 22,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\components\\\\magicui\\\\hero-video-dialog.jsx\",\n                    lineNumber: 21,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\components\\\\magicui\\\\hero-video-dialog.jsx\",\n            lineNumber: 14,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\components\\\\magicui\\\\hero-video-dialog.jsx\",\n        lineNumber: 13,\n        columnNumber: 6\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL21hZ2ljdWkvaGVyby12aWRlby1kaWFsb2cuanN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQUEsNENBQTRDO0FBRVI7QUFDSDtBQUVsQixTQUFTRSxnQkFBZ0IsRUFDdENDLFlBQVksRUFDWkMsZUFBZSxpQkFBaUIsRUFDaENDLFNBQVMsRUFDVjtJQUNDLHFEQUFxRDtJQUNyRCxxQkFDRyw4REFBQ0M7UUFBSUQsV0FBV0osOENBQUVBLENBQUMsWUFBWUk7a0JBQzlCLDRFQUFDQztZQUFJRCxXQUFVOzs4QkFDYiw4REFBQ0U7b0JBQ0NDLEtBQUtMO29CQUNMTSxLQUFLTDtvQkFDTE0sT0FBTztvQkFDUEMsUUFBUTtvQkFDUk4sV0FBVTs7Ozs7OzhCQUNaLDhEQUFDQztvQkFBSUQsV0FBVTs4QkFDYiw0RUFBQ0M7d0JBQUlELFdBQVU7a0NBQ2IsNEVBQUNDOzRCQUFJRCxXQUFVO3NDQUNiLDRFQUFDTCxnRkFBSUE7Z0NBQ0hLLFdBQVU7Z0NBQ1ZPLE9BQU87b0NBQ0xDLFFBQVE7Z0NBQ1Y7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBT2hCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGhwXFxEZXNrdG9wXFxWb2ljZS1BZ2VudC1mb3ItSVRqb2JzXFxBSS1JbnRlcnZpZXctVm9pY2UtQWdlbnQtZm9yLUlULUpvYnNcXGNvbXBvbmVudHNcXG1hZ2ljdWlcXGhlcm8tdmlkZW8tZGlhbG9nLmpzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIvKiBlc2xpbnQtZGlzYWJsZSBAbmV4dC9uZXh0L25vLWltZy1lbGVtZW50ICovXHJcblwidXNlIGNsaWVudFwiO1xyXG5pbXBvcnQgeyBQbGF5IH0gZnJvbSBcImx1Y2lkZS1yZWFjdFwiO1xyXG5pbXBvcnQgeyBjbiB9IGZyb20gXCJAL2xpYi91dGlsc1wiO1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gSGVyb1ZpZGVvRGlhbG9nKHtcclxuICB0aHVtYm5haWxTcmMsXHJcbiAgdGh1bWJuYWlsQWx0ID0gXCJWaWRlbyB0aHVtYm5haWxcIixcclxuICBjbGFzc05hbWVcclxufSkge1xyXG4gIC8vIFN0YXRpYyBjb21wb25lbnQgd2l0aCBubyB2aWRlbyBwb3B1cCBmdW5jdGlvbmFsaXR5XHJcbiAgcmV0dXJuIChcclxuICAgICg8ZGl2IGNsYXNzTmFtZT17Y24oXCJyZWxhdGl2ZVwiLCBjbGFzc05hbWUpfT5cclxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJncm91cCByZWxhdGl2ZVwiPlxyXG4gICAgICAgIDxpbWdcclxuICAgICAgICAgIHNyYz17dGh1bWJuYWlsU3JjfVxyXG4gICAgICAgICAgYWx0PXt0aHVtYm5haWxBbHR9XHJcbiAgICAgICAgICB3aWR0aD17MTkyMH1cclxuICAgICAgICAgIGhlaWdodD17MTA4MH1cclxuICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCByb3VuZGVkLW1kIGJvcmRlciBzaGFkb3ctbGdcIiAvPlxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgaW5zZXQtMCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciByb3VuZGVkLTJ4bFwiPlxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IHNpemUtMjggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHJvdW5kZWQtZnVsbCBiZy1wcmltYXJ5LzEwIGJhY2tkcm9wLWJsdXItbWRcIj5cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZSBmbGV4IHNpemUtMjAgaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHJvdW5kZWQtZnVsbCBiZy1ncmFkaWVudC10by1iIGZyb20tcHJpbWFyeS8zMCB0by1wcmltYXJ5IHNoYWRvdy1tZFwiPlxyXG4gICAgICAgICAgICAgIDxQbGF5XHJcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJzaXplLTggZmlsbC13aGl0ZSB0ZXh0LXdoaXRlXCJcclxuICAgICAgICAgICAgICAgIHN0eWxlPXt7XHJcbiAgICAgICAgICAgICAgICAgIGZpbHRlcjogXCJkcm9wLXNoYWRvdygwIDRweCAzcHggcmdiKDAgMCAwIC8gMC4wNykpIGRyb3Atc2hhZG93KDAgMnB4IDJweCByZ2IoMCAwIDAgLyAwLjA2KSlcIixcclxuICAgICAgICAgICAgICAgIH19IC8+XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgPC9kaXY+XHJcbiAgICAgIDwvZGl2PlxyXG4gICAgPC9kaXY+KVxyXG4gICk7XHJcbn1cclxuIl0sIm5hbWVzIjpbIlBsYXkiLCJjbiIsIkhlcm9WaWRlb0RpYWxvZyIsInRodW1ibmFpbFNyYyIsInRodW1ibmFpbEFsdCIsImNsYXNzTmFtZSIsImRpdiIsImltZyIsInNyYyIsImFsdCIsIndpZHRoIiwiaGVpZ2h0Iiwic3R5bGUiLCJmaWx0ZXIiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./components/magicui/hero-video-dialog.jsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/button.jsx":
/*!**********************************!*\
  !*** ./components/ui/button.jsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.js\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\", {\n    variants: {\n        variant: {\n            default: \"bg-primary cursor-pointer text-primary-foreground shadow hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90\",\n            outline: \"border cursor-pointer border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-9 px-4 py-2\",\n            sm: \"h-8 rounded-md px-3 text-xs\",\n            lg: \"h-10 rounded-md px-8\",\n            icon: \"h-9 w-9\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\components\\\\ui\\\\button.jsx\",\n        lineNumber: 43,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/button.jsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/sonner.jsx":
/*!**********************************!*\
  !*** ./components/ui/sonner.jsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Toaster: () => (/* binding */ Toaster)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-themes */ \"(ssr)/./node_modules/next-themes/dist/index.mjs\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! sonner */ \"(ssr)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _hooks_useHydrationFix__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useHydrationFix */ \"(ssr)/./hooks/useHydrationFix.js\");\n/* __next_internal_client_entry_do_not_use__ Toaster auto */ \n\n\n\nconst Toaster = ({ ...props })=>{\n    const { theme = \"system\" } = (0,next_themes__WEBPACK_IMPORTED_MODULE_1__.useTheme)();\n    const isClient = (0,_hooks_useHydrationFix__WEBPACK_IMPORTED_MODULE_3__.useIsClient)();\n    // Don't render on server to prevent hydration mismatch\n    if (!isClient) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(sonner__WEBPACK_IMPORTED_MODULE_2__.Toaster, {\n        theme: theme,\n        className: \"toaster group\",\n        style: {\n            \"--normal-bg\": \"var(--popover)\",\n            \"--normal-text\": \"var(--popover-foreground)\",\n            \"--normal-border\": \"var(--border)\"\n        },\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\components\\\\ui\\\\sonner.jsx\",\n        lineNumber: 19,\n        columnNumber: 6\n    }, undefined);\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3VpL3Nvbm5lci5qc3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUVzQztBQUNLO0FBQ1c7QUFFdEQsTUFBTUMsVUFBVSxDQUFDLEVBQ2YsR0FBR0csT0FDSjtJQUNDLE1BQU0sRUFBRUMsUUFBUSxRQUFRLEVBQUUsR0FBR0wscURBQVFBO0lBQ3JDLE1BQU1NLFdBQVdILG1FQUFXQTtJQUU1Qix1REFBdUQ7SUFDdkQsSUFBSSxDQUFDRyxVQUFVO1FBQ2IsT0FBTztJQUNUO0lBRUEscUJBQ0csOERBQUNKLDJDQUFNQTtRQUNORyxPQUFPQTtRQUNQRSxXQUFVO1FBQ1ZDLE9BQ0U7WUFDRSxlQUFlO1lBQ2YsaUJBQWlCO1lBQ2pCLG1CQUFtQjtRQUNyQjtRQUVELEdBQUdKLEtBQUs7Ozs7OztBQUVmO0FBRWtCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGhwXFxEZXNrdG9wXFxWb2ljZS1BZ2VudC1mb3ItSVRqb2JzXFxBSS1JbnRlcnZpZXctVm9pY2UtQWdlbnQtZm9yLUlULUpvYnNcXGNvbXBvbmVudHNcXHVpXFxzb25uZXIuanN4Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiXHJcblxyXG5pbXBvcnQgeyB1c2VUaGVtZSB9IGZyb20gXCJuZXh0LXRoZW1lc1wiXHJcbmltcG9ydCB7IFRvYXN0ZXIgYXMgU29ubmVyIH0gZnJvbSBcInNvbm5lclwiO1xyXG5pbXBvcnQgeyB1c2VJc0NsaWVudCB9IGZyb20gXCJAL2hvb2tzL3VzZUh5ZHJhdGlvbkZpeFwiO1xyXG5cclxuY29uc3QgVG9hc3RlciA9ICh7XHJcbiAgLi4ucHJvcHNcclxufSkgPT4ge1xyXG4gIGNvbnN0IHsgdGhlbWUgPSBcInN5c3RlbVwiIH0gPSB1c2VUaGVtZSgpXHJcbiAgY29uc3QgaXNDbGllbnQgPSB1c2VJc0NsaWVudCgpO1xyXG5cclxuICAvLyBEb24ndCByZW5kZXIgb24gc2VydmVyIHRvIHByZXZlbnQgaHlkcmF0aW9uIG1pc21hdGNoXHJcbiAgaWYgKCFpc0NsaWVudCkge1xyXG4gICAgcmV0dXJuIG51bGw7XHJcbiAgfVxyXG5cclxuICByZXR1cm4gKFxyXG4gICAgKDxTb25uZXJcclxuICAgICAgdGhlbWU9e3RoZW1lfVxyXG4gICAgICBjbGFzc05hbWU9XCJ0b2FzdGVyIGdyb3VwXCJcclxuICAgICAgc3R5bGU9e1xyXG4gICAgICAgIHtcclxuICAgICAgICAgIFwiLS1ub3JtYWwtYmdcIjogXCJ2YXIoLS1wb3BvdmVyKVwiLFxyXG4gICAgICAgICAgXCItLW5vcm1hbC10ZXh0XCI6IFwidmFyKC0tcG9wb3Zlci1mb3JlZ3JvdW5kKVwiLFxyXG4gICAgICAgICAgXCItLW5vcm1hbC1ib3JkZXJcIjogXCJ2YXIoLS1ib3JkZXIpXCJcclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuICAgICAgey4uLnByb3BzfSAvPilcclxuICApO1xyXG59XHJcblxyXG5leHBvcnQgeyBUb2FzdGVyIH1cclxuIl0sIm5hbWVzIjpbInVzZVRoZW1lIiwiVG9hc3RlciIsIlNvbm5lciIsInVzZUlzQ2xpZW50IiwicHJvcHMiLCJ0aGVtZSIsImlzQ2xpZW50IiwiY2xhc3NOYW1lIiwic3R5bGUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/sonner.jsx\n");

/***/ }),

/***/ "(ssr)/./context/UserDetailContext.jsx":
/*!***************************************!*\
  !*** ./context/UserDetailContext.jsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UserDetailContext: () => (/* binding */ UserDetailContext)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nconst UserDetailContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb250ZXh0L1VzZXJEZXRhaWxDb250ZXh0LmpzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBc0M7QUFFL0IsTUFBTUMsa0NBQW9CRCxvREFBYUEsR0FBRyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxocFxcRGVza3RvcFxcVm9pY2UtQWdlbnQtZm9yLUlUam9ic1xcQUktSW50ZXJ2aWV3LVZvaWNlLUFnZW50LWZvci1JVC1Kb2JzXFxjb250ZXh0XFxVc2VyRGV0YWlsQ29udGV4dC5qc3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY3JlYXRlQ29udGV4dCB9IGZyb20gXCJyZWFjdFwiO1xyXG5cclxuZXhwb3J0IGNvbnN0IFVzZXJEZXRhaWxDb250ZXh0ID0gY3JlYXRlQ29udGV4dCgpO1xyXG4iXSwibmFtZXMiOlsiY3JlYXRlQ29udGV4dCIsIlVzZXJEZXRhaWxDb250ZXh0Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./context/UserDetailContext.jsx\n");

/***/ }),

/***/ "(ssr)/./hooks/useHydrationFix.js":
/*!**********************************!*\
  !*** ./hooks/useHydrationFix.js ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HydrationBoundary: () => (/* binding */ HydrationBoundary),\n/* harmony export */   useHydrationFix: () => (/* binding */ useHydrationFix),\n/* harmony export */   useIsClient: () => (/* binding */ useIsClient)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ useHydrationFix,HydrationBoundary,useIsClient auto */ \n/**\n * Custom hook to handle hydration mismatches caused by browser extensions\n * This prevents the common hydration error when browser extensions modify the DOM\n */ function useHydrationFix() {\n    const [isHydrated, setIsHydrated] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useHydrationFix.useEffect\": ()=>{\n            // Mark as hydrated after client-side mount\n            setIsHydrated(true);\n            // Handle browser extension attributes that cause hydration mismatches\n            const cleanupExtensionAttributes = {\n                \"useHydrationFix.useEffect.cleanupExtensionAttributes\": ()=>{\n                    const body = document.body;\n                    // List of known problematic browser extension attributes\n                    const extensionAttributes = [\n                        'cz-shortcut-listen',\n                        'data-new-gr-c-s-check-loaded',\n                        'data-gr-ext-installed',\n                        'data-gramm',\n                        'data-gramm_editor',\n                        'data-enable-grammarly',\n                        'spellcheck',\n                        'data-ms-editor',\n                        'data-lt-installed',\n                        'data-darkreader-mode',\n                        'data-darkreader-scheme',\n                        'data-adblock',\n                        'data-honey-extension',\n                        'data-lastpass',\n                        'data-bitwarden',\n                        'data-dashlane',\n                        'data-1password'\n                    ];\n                    // Remove problematic attributes\n                    extensionAttributes.forEach({\n                        \"useHydrationFix.useEffect.cleanupExtensionAttributes\": (attr)=>{\n                            if (body.hasAttribute(attr)) {\n                                body.removeAttribute(attr);\n                            }\n                        }\n                    }[\"useHydrationFix.useEffect.cleanupExtensionAttributes\"]);\n                    // Also check for any data-* attributes that might be added by extensions\n                    Array.from(body.attributes).forEach({\n                        \"useHydrationFix.useEffect.cleanupExtensionAttributes\": (attr)=>{\n                            if (attr.name.startsWith('data-') && (attr.name.includes('extension') || attr.name.includes('plugin') || attr.name.includes('addon'))) {\n                                body.removeAttribute(attr.name);\n                            }\n                        }\n                    }[\"useHydrationFix.useEffect.cleanupExtensionAttributes\"]);\n                }\n            }[\"useHydrationFix.useEffect.cleanupExtensionAttributes\"];\n            // Clean up immediately\n            cleanupExtensionAttributes();\n            // Set up mutation observer to handle dynamically added attributes\n            const observer = new MutationObserver({\n                \"useHydrationFix.useEffect\": (mutations)=>{\n                    mutations.forEach({\n                        \"useHydrationFix.useEffect\": (mutation)=>{\n                            if (mutation.type === 'attributes' && mutation.target === document.body) {\n                                const attributeName = mutation.attributeName;\n                                // Check if it's a problematic extension attribute\n                                if (attributeName && (attributeName.startsWith('cz-') || attributeName.startsWith('data-gr-') || attributeName.startsWith('data-gramm') || attributeName.startsWith('data-ms-') || attributeName.startsWith('data-lt-') || attributeName.startsWith('data-darkreader') || attributeName.includes('extension') || attributeName.includes('plugin') || attributeName.includes('addon'))) {\n                                    document.body.removeAttribute(attributeName);\n                                }\n                            }\n                        }\n                    }[\"useHydrationFix.useEffect\"]);\n                }\n            }[\"useHydrationFix.useEffect\"]);\n            observer.observe(document.body, {\n                attributes: true,\n                attributeOldValue: true\n            });\n            // Cleanup function\n            return ({\n                \"useHydrationFix.useEffect\": ()=>{\n                    observer.disconnect();\n                }\n            })[\"useHydrationFix.useEffect\"];\n        }\n    }[\"useHydrationFix.useEffect\"], []);\n    return isHydrated;\n}\n/**\n * Component wrapper that prevents hydration mismatches\n * Use this to wrap components that might be affected by browser extensions\n */ function HydrationBoundary({ children, fallback = null }) {\n    const isHydrated = useHydrationFix();\n    if (!isHydrated) {\n        return fallback;\n    }\n    return children;\n}\n/**\n * Hook to safely use browser-only APIs\n * Returns true only after hydration is complete\n */ function useIsClient() {\n    const [isClient, setIsClient] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useIsClient.useEffect\": ()=>{\n            setIsClient(true);\n        }\n    }[\"useIsClient.useEffect\"], []);\n    return isClient;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./hooks/useHydrationFix.js\n");

/***/ }),

/***/ "(ssr)/./hooks/useLocalStorage.js":
/*!**********************************!*\
  !*** ./hooks/useLocalStorage.js ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useDocument: () => (/* binding */ useDocument),\n/* harmony export */   useLocalStorage: () => (/* binding */ useLocalStorage),\n/* harmony export */   useSessionStorage: () => (/* binding */ useSessionStorage),\n/* harmony export */   useWindow: () => (/* binding */ useWindow)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _useHydrationFix__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./useHydrationFix */ \"(ssr)/./hooks/useHydrationFix.js\");\n/* __next_internal_client_entry_do_not_use__ useLocalStorage,useSessionStorage,useWindow,useDocument auto */ \n\n/**\n * Safe localStorage hook that prevents hydration mismatches\n * @param {string} key - The localStorage key\n * @param {any} initialValue - The initial value if no stored value exists\n * @returns {[any, function]} - [storedValue, setValue]\n */ function useLocalStorage(key, initialValue) {\n    const isClient = (0,_useHydrationFix__WEBPACK_IMPORTED_MODULE_1__.useIsClient)();\n    // State to store our value\n    const [storedValue, setStoredValue] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(initialValue);\n    // Return a wrapped version of useState's setter function that persists the new value to localStorage\n    const setValue = (value)=>{\n        try {\n            // Allow value to be a function so we have the same API as useState\n            const valueToStore = value instanceof Function ? value(storedValue) : value;\n            // Save state\n            setStoredValue(valueToStore);\n            // Save to local storage only on client\n            if (isClient && \"undefined\" !== 'undefined') {}\n        } catch (error) {\n            // A more advanced implementation would handle the error case\n            console.warn(`Error setting localStorage key \"${key}\":`, error);\n        }\n    };\n    // Load value from localStorage on client mount\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useLocalStorage.useEffect\": ()=>{\n            if (!isClient) return;\n            try {\n                const item = window.localStorage.getItem(key);\n                if (item) {\n                    setStoredValue(JSON.parse(item));\n                }\n            } catch (error) {\n                console.warn(`Error reading localStorage key \"${key}\":`, error);\n                setStoredValue(initialValue);\n            }\n        }\n    }[\"useLocalStorage.useEffect\"], [\n        key,\n        initialValue,\n        isClient\n    ]);\n    return [\n        storedValue,\n        setValue\n    ];\n}\n/**\n * Safe sessionStorage hook that prevents hydration mismatches\n * @param {string} key - The sessionStorage key\n * @param {any} initialValue - The initial value if no stored value exists\n * @returns {[any, function]} - [storedValue, setValue]\n */ function useSessionStorage(key, initialValue) {\n    const isClient = (0,_useHydrationFix__WEBPACK_IMPORTED_MODULE_1__.useIsClient)();\n    const [storedValue, setStoredValue] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(initialValue);\n    const setValue = (value)=>{\n        try {\n            const valueToStore = value instanceof Function ? value(storedValue) : value;\n            setStoredValue(valueToStore);\n            if (isClient && \"undefined\" !== 'undefined') {}\n        } catch (error) {\n            console.warn(`Error setting sessionStorage key \"${key}\":`, error);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useSessionStorage.useEffect\": ()=>{\n            if (!isClient) return;\n            try {\n                const item = window.sessionStorage.getItem(key);\n                if (item) {\n                    setStoredValue(JSON.parse(item));\n                }\n            } catch (error) {\n                console.warn(`Error reading sessionStorage key \"${key}\":`, error);\n                setStoredValue(initialValue);\n            }\n        }\n    }[\"useSessionStorage.useEffect\"], [\n        key,\n        initialValue,\n        isClient\n    ]);\n    return [\n        storedValue,\n        setValue\n    ];\n}\n/**\n * Hook to safely access window object\n * @returns {Window | null} - Window object or null if not on client\n */ function useWindow() {\n    const isClient = (0,_useHydrationFix__WEBPACK_IMPORTED_MODULE_1__.useIsClient)();\n    return isClient ? window : null;\n}\n/**\n * Hook to safely access document object\n * @returns {Document | null} - Document object or null if not on client\n */ function useDocument() {\n    const isClient = (0,_useHydrationFix__WEBPACK_IMPORTED_MODULE_1__.useIsClient)();\n    return isClient ? document : null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./hooks/useLocalStorage.js\n");

/***/ }),

/***/ "(ssr)/./lib/utils.js":
/*!**********************!*\
  !*** ./lib/utils.js ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9saWIvdXRpbHMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQTRCO0FBQ1k7QUFFakMsU0FBU0UsR0FBRyxHQUFHQyxNQUFNO0lBQzFCLE9BQU9GLHVEQUFPQSxDQUFDRCwwQ0FBSUEsQ0FBQ0c7QUFDdEIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcaHBcXERlc2t0b3BcXFZvaWNlLUFnZW50LWZvci1JVGpvYnNcXEFJLUludGVydmlldy1Wb2ljZS1BZ2VudC1mb3ItSVQtSm9ic1xcbGliXFx1dGlscy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjbHN4IH0gZnJvbSBcImNsc3hcIjtcclxuaW1wb3J0IHsgdHdNZXJnZSB9IGZyb20gXCJ0YWlsd2luZC1tZXJnZVwiXHJcblxyXG5leHBvcnQgZnVuY3Rpb24gY24oLi4uaW5wdXRzKSB7XHJcbiAgcmV0dXJuIHR3TWVyZ2UoY2xzeChpbnB1dHMpKTtcclxufVxyXG4iXSwibmFtZXMiOlsiY2xzeCIsInR3TWVyZ2UiLCJjbiIsImlucHV0cyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./lib/utils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5CAI-Interview-Voice-Agent-for-IT-Jobs%5C%5Capp%5C%5C_components%5C%5CClientBodyWrapper.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5CAI-Interview-Voice-Agent-for-IT-Jobs%5C%5Capp%5C%5C_components%5C%5CDatabaseInitializer.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5CAI-Interview-Voice-Agent-for-IT-Jobs%5C%5Capp%5C%5C_components%5C%5CHydrationProvider.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5CAI-Interview-Voice-Agent-for-IT-Jobs%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5CAI-Interview-Voice-Agent-for-IT-Jobs%5C%5Capp%5C%5Cprovider.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5CAI-Interview-Voice-Agent-for-IT-Jobs%5C%5Ccomponents%5C%5Cui%5C%5Csonner.jsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5CAI-Interview-Voice-Agent-for-IT-Jobs%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5CAI-Interview-Voice-Agent-for-IT-Jobs%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5CAI-Interview-Voice-Agent-for-IT-Jobs%5C%5Capp%5C%5C_components%5C%5CClientBodyWrapper.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5CAI-Interview-Voice-Agent-for-IT-Jobs%5C%5Capp%5C%5C_components%5C%5CDatabaseInitializer.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5CAI-Interview-Voice-Agent-for-IT-Jobs%5C%5Capp%5C%5C_components%5C%5CHydrationProvider.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5CAI-Interview-Voice-Agent-for-IT-Jobs%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5CAI-Interview-Voice-Agent-for-IT-Jobs%5C%5Capp%5C%5Cprovider.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5CAI-Interview-Voice-Agent-for-IT-Jobs%5C%5Ccomponents%5C%5Cui%5C%5Csonner.jsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5CAI-Interview-Voice-Agent-for-IT-Jobs%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5CAI-Interview-Voice-Agent-for-IT-Jobs%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/_components/ClientBodyWrapper.jsx */ \"(ssr)/./app/_components/ClientBodyWrapper.jsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/_components/DatabaseInitializer.jsx */ \"(ssr)/./app/_components/DatabaseInitializer.jsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/_components/HydrationProvider.jsx */ \"(ssr)/./app/_components/HydrationProvider.jsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/provider.jsx */ \"(ssr)/./app/provider.jsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/ui/sonner.jsx */ \"(ssr)/./components/ui/sonner.jsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5CAI-Interview-Voice-Agent-for-IT-Jobs%5C%5Capp%5C%5C_components%5C%5CClientBodyWrapper.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5CAI-Interview-Voice-Agent-for-IT-Jobs%5C%5Capp%5C%5C_components%5C%5CDatabaseInitializer.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5CAI-Interview-Voice-Agent-for-IT-Jobs%5C%5Capp%5C%5C_components%5C%5CHydrationProvider.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5CAI-Interview-Voice-Agent-for-IT-Jobs%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5CAI-Interview-Voice-Agent-for-IT-Jobs%5C%5Capp%5C%5Cprovider.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5CAI-Interview-Voice-Agent-for-IT-Jobs%5C%5Ccomponents%5C%5Cui%5C%5Csonner.jsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5CAI-Interview-Voice-Agent-for-IT-Jobs%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5CAI-Interview-Voice-Agent-for-IT-Jobs%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5CAI-Interview-Voice-Agent-for-IT-Jobs%5C%5Capp%5C%5Cpage.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5CAI-Interview-Voice-Agent-for-IT-Jobs%5C%5Capp%5C%5Cpage.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.js */ \"(ssr)/./app/page.js\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2hwJTVDJTVDRGVza3RvcCU1QyU1Q1ZvaWNlLUFnZW50LWZvci1JVGpvYnMlNUMlNUNBSS1JbnRlcnZpZXctVm9pY2UtQWdlbnQtZm9yLUlULUpvYnMlNUMlNUNhcHAlNUMlNUNwYWdlLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxzSUFBdUkiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXGhwXFxcXERlc2t0b3BcXFxcVm9pY2UtQWdlbnQtZm9yLUlUam9ic1xcXFxBSS1JbnRlcnZpZXctVm9pY2UtQWdlbnQtZm9yLUlULUpvYnNcXFxcYXBwXFxcXHBhZ2UuanNcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5CAI-Interview-Voice-Agent-for-IT-Jobs%5C%5Capp%5C%5Cpage.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5CAI-Interview-Voice-Agent-for-IT-Jobs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5CAI-Interview-Voice-Agent-for-IT-Jobs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5CAI-Interview-Voice-Agent-for-IT-Jobs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5CAI-Interview-Voice-Agent-for-IT-Jobs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5CAI-Interview-Voice-Agent-for-IT-Jobs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5CAI-Interview-Voice-Agent-for-IT-Jobs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5CAI-Interview-Voice-Agent-for-IT-Jobs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5CAI-Interview-Voice-Agent-for-IT-Jobs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5CAI-Interview-Voice-Agent-for-IT-Jobs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5CAI-Interview-Voice-Agent-for-IT-Jobs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5CAI-Interview-Voice-Agent-for-IT-Jobs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5CAI-Interview-Voice-Agent-for-IT-Jobs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5CAI-Interview-Voice-Agent-for-IT-Jobs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5CAI-Interview-Voice-Agent-for-IT-Jobs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5CAI-Interview-Voice-Agent-for-IT-Jobs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5CAI-Interview-Voice-Agent-for-IT-Jobs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5CAI-Interview-Voice-Agent-for-IT-Jobs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5CAI-Interview-Voice-Agent-for-IT-Jobs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5CAI-Interview-Voice-Agent-for-IT-Jobs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5CAI-Interview-Voice-Agent-for-IT-Jobs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5CAI-Interview-Voice-Agent-for-IT-Jobs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5CAI-Interview-Voice-Agent-for-IT-Jobs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5CAI-Interview-Voice-Agent-for-IT-Jobs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5CAI-Interview-Voice-Agent-for-IT-Jobs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./services/supabaseClient.js":
/*!************************************!*\
  !*** ./services/supabaseClient.js ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   supabase: () => (/* binding */ supabase)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(ssr)/../node_modules/@supabase/supabase-js/dist/module/index.js\");\n\n// Create a single supabase client for interacting with your database\nconst supabaseUrl = \"https://aimmcbxnlckivomgsdnt.supabase.co\";\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFpbW1jYnhubGNraXZvbWdzZG50Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc1NjkzNDQsImV4cCI6MjA2MzE0NTM0NH0.wemJlkzSpml8KjSmZY5DvVHD1hLSSTw2ij_tJ10bk3c\";\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zZXJ2aWNlcy9zdXBhYmFzZUNsaWVudC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFvRDtBQUVwRCxxRUFBcUU7QUFDckUsTUFBTUMsY0FBY0MsMENBQW9DO0FBQ3hELE1BQU1HLGtCQUFrQkgsa05BQXlDO0FBQzFELE1BQU1LLFdBQVdQLG1FQUFZQSxDQUNoQ0MsYUFDQUksaUJBQ0giLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcaHBcXERlc2t0b3BcXFZvaWNlLUFnZW50LWZvci1JVGpvYnNcXEFJLUludGVydmlldy1Wb2ljZS1BZ2VudC1mb3ItSVQtSm9ic1xcc2VydmljZXNcXHN1cGFiYXNlQ2xpZW50LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNyZWF0ZUNsaWVudCB9IGZyb20gJ0BzdXBhYmFzZS9zdXBhYmFzZS1qcydcclxuXHJcbi8vIENyZWF0ZSBhIHNpbmdsZSBzdXBhYmFzZSBjbGllbnQgZm9yIGludGVyYWN0aW5nIHdpdGggeW91ciBkYXRhYmFzZVxyXG5jb25zdCBzdXBhYmFzZVVybCA9IHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX1NVUEFCQVNFX1VSTDtcclxuY29uc3Qgc3VwYWJhc2VBbm9uS2V5ID0gcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfU1VQQUJBU0VfQU5PTl9LRVk7XHJcbmV4cG9ydCBjb25zdCBzdXBhYmFzZSA9IGNyZWF0ZUNsaWVudChcclxuICAgIHN1cGFiYXNlVXJsLFxyXG4gICAgc3VwYWJhc2VBbm9uS2V5XHJcbikiXSwibmFtZXMiOlsiY3JlYXRlQ2xpZW50Iiwic3VwYWJhc2VVcmwiLCJwcm9jZXNzIiwiZW52IiwiTkVYVF9QVUJMSUNfU1VQQUJBU0VfVVJMIiwic3VwYWJhc2VBbm9uS2V5IiwiTkVYVF9QVUJMSUNfU1VQQUJBU0VfQU5PTl9LRVkiLCJzdXBhYmFzZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./services/supabaseClient.js\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?7f96":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?d1cc":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/tr46","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions","vendor-chunks/@supabase","vendor-chunks/ws","vendor-chunks/sonner","vendor-chunks/next-themes","vendor-chunks/@swc","vendor-chunks/isows","vendor-chunks/@radix-ui","vendor-chunks/tailwind-merge","vendor-chunks/lucide-react","vendor-chunks/class-variance-authority","vendor-chunks/clsx"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.js&appDir=C%3A%5CUsers%5Chp%5CDesktop%5CVoice-Agent-for-ITjobs%5CAI-Interview-Voice-Agent-for-IT-Jobs%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Chp%5CDesktop%5CVoice-Agent-for-ITjobs%5CAI-Interview-Voice-Agent-for-IT-Jobs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();