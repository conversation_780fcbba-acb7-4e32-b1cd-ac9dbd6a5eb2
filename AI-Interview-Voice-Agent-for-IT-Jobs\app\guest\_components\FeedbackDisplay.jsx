"use client"
import React from 'react'
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { Separator } from '@/components/ui/separator'
import { 
    CheckCircle, 
    XCircle, 
    TrendingUp, 
    TrendingDown, 
    Star, 
    Target, 
    Brain, 
    MessageCircle, 
    Lightbulb,
    Download,
    Award,
    BarChart3,
    User,
    Briefcase,
    Heart,
    Zap
} from 'lucide-react'

function FeedbackDisplay({ feedback, jobDetails, onDownloadPDF }) {
    if (!feedback) {
        return (
            <Card>
                <CardContent className="p-8 text-center">
                    <p className="text-gray-500">No feedback available</p>
                </CardContent>
            </Card>
        );
    }

    const { 
        overallScore, 
        recommendation, 
        recommendationMessage, 
        ratings, 
        strengths, 
        areasForImprovement, 
        detailedAnalysis,
        interviewPerformance,
        careerAdvice,
        matchAnalysis,
        nextSteps
    } = feedback;

    const getScoreColor = (score) => {
        if (score >= 80) return 'text-green-600 bg-green-100';
        if (score >= 60) return 'text-yellow-600 bg-yellow-100';
        return 'text-red-600 bg-red-100';
    };

    const getRatingColor = (rating) => {
        if (rating >= 8) return 'bg-green-500';
        if (rating >= 6) return 'bg-yellow-500';
        return 'bg-red-500';
    };

    return (
        <div className="space-y-6 max-w-4xl mx-auto">
            {/* Header with Overall Score */}
            <Card className={`border-2 ${recommendation ? 'border-green-200 bg-green-50' : 'border-orange-200 bg-orange-50'}`}>
                <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                        <div className="flex items-center gap-4">
                            {recommendation ? (
                                <CheckCircle className="w-12 h-12 text-green-600" />
                            ) : (
                                <XCircle className="w-12 h-12 text-orange-600" />
                            )}
                            <div>
                                <h2 className="text-2xl font-bold">Interview Feedback</h2>
                                <p className={`text-lg ${recommendation ? 'text-green-700' : 'text-orange-700'}`}>
                                    {recommendationMessage}
                                </p>
                            </div>
                        </div>
                        <div className="text-center">
                            <div className={`text-4xl font-bold p-4 rounded-full ${getScoreColor(overallScore)}`}>
                                {overallScore}%
                            </div>
                            <p className="text-sm text-gray-600 mt-2">Overall Score</p>
                        </div>
                    </div>
                </CardContent>
            </Card>

            {/* Ratings Grid */}
            <Card>
                <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                        <BarChart3 className="w-5 h-5" />
                        Performance Ratings
                    </CardTitle>
                </CardHeader>
                <CardContent>
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                        <div className="space-y-2">
                            <div className="flex items-center gap-2">
                                <Brain className="w-4 h-4 text-blue-500" />
                                <span className="text-sm font-medium">Technical Skills</span>
                            </div>
                            <Progress value={ratings?.technicalSkills * 10} className="h-2" />
                            <p className="text-xs text-gray-600">{ratings?.technicalSkills}/10</p>
                        </div>
                        
                        <div className="space-y-2">
                            <div className="flex items-center gap-2">
                                <MessageCircle className="w-4 h-4 text-green-500" />
                                <span className="text-sm font-medium">Communication</span>
                            </div>
                            <Progress value={ratings?.communication * 10} className="h-2" />
                            <p className="text-xs text-gray-600">{ratings?.communication}/10</p>
                        </div>
                        
                        <div className="space-y-2">
                            <div className="flex items-center gap-2">
                                <Lightbulb className="w-4 h-4 text-yellow-500" />
                                <span className="text-sm font-medium">Problem Solving</span>
                            </div>
                            <Progress value={ratings?.problemSolving * 10} className="h-2" />
                            <p className="text-xs text-gray-600">{ratings?.problemSolving}/10</p>
                        </div>
                        
                        <div className="space-y-2">
                            <div className="flex items-center gap-2">
                                <Briefcase className="w-4 h-4 text-purple-500" />
                                <span className="text-sm font-medium">Experience</span>
                            </div>
                            <Progress value={ratings?.experience * 10} className="h-2" />
                            <p className="text-xs text-gray-600">{ratings?.experience}/10</p>
                        </div>
                        
                        <div className="space-y-2">
                            <div className="flex items-center gap-2">
                                <Heart className="w-4 h-4 text-red-500" />
                                <span className="text-sm font-medium">Cultural Fit</span>
                            </div>
                            <Progress value={ratings?.culturalFit * 10} className="h-2" />
                            <p className="text-xs text-gray-600">{ratings?.culturalFit}/10</p>
                        </div>
                        
                        <div className="space-y-2">
                            <div className="flex items-center gap-2">
                                <Zap className="w-4 h-4 text-orange-500" />
                                <span className="text-sm font-medium">Enthusiasm</span>
                            </div>
                            <Progress value={ratings?.enthusiasm * 10} className="h-2" />
                            <p className="text-xs text-gray-600">{ratings?.enthusiasm}/10</p>
                        </div>
                    </div>
                </CardContent>
            </Card>

            {/* Strengths and Areas for Improvement */}
            <div className="grid md:grid-cols-2 gap-6">
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2 text-green-600">
                            <Star className="w-5 h-5" />
                            Key Strengths
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-3">
                            {strengths?.map((strength, index) => (
                                <div key={index} className="flex items-start gap-2">
                                    <CheckCircle className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                                    <p className="text-sm">{strength}</p>
                                </div>
                            ))}
                        </div>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2 text-orange-600">
                            <Target className="w-5 h-5" />
                            Areas for Improvement
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-4">
                            {areasForImprovement?.map((area, index) => (
                                <div key={index} className="space-y-2">
                                    <div className="flex items-center gap-2">
                                        <TrendingUp className="w-4 h-4 text-orange-500" />
                                        <span className="font-medium text-sm">{area.area}</span>
                                    </div>
                                    <p className="text-xs text-gray-600 ml-6">{area.issue}</p>
                                    <p className="text-xs text-blue-600 ml-6 font-medium">{area.suggestion}</p>
                                </div>
                            ))}
                        </div>
                    </CardContent>
                </Card>
            </div>

            {/* Download Button */}
            <div className="flex justify-center">
                <Button 
                    onClick={onDownloadPDF}
                    size="lg"
                    className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700"
                >
                    <Download className="w-5 h-5 mr-2" />
                    Download Detailed Report
                </Button>
            </div>
        </div>
    );
}

export default FeedbackDisplay;
