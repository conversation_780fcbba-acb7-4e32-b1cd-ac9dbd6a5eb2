"use client"

import { useEffect, useState } from 'react';

/**
 * Custom hook to handle hydration mismatches caused by browser extensions
 * This prevents the common hydration error when browser extensions modify the DOM
 */
export function useHydrationFix() {
  const [isHydrated, setIsHydrated] = useState(false);

  useEffect(() => {
    // Mark as hydrated after client-side mount
    setIsHydrated(true);

    // Handle browser extension attributes that cause hydration mismatches
    const cleanupExtensionAttributes = () => {
      const body = document.body;
      
      // List of known problematic browser extension attributes
      const extensionAttributes = [
        'cz-shortcut-listen',           // ColorZilla
        'data-new-gr-c-s-check-loaded', // Grammarly
        'data-gr-ext-installed',        // Grammarly
        'data-gramm',                   // Grammarly
        'data-gramm_editor',            // Grammarly
        'data-enable-grammarly',        // Grammarly
        'spellcheck',                   // Various spell checkers
        'data-ms-editor',               // Microsoft Editor
        'data-lt-installed',            // LanguageTool
        'data-darkreader-mode',         // Dark Reader
        'data-darkreader-scheme',       // Dark Reader
        'data-adblock',                 // Ad blockers
        'data-honey-extension',         // Honey
        'data-lastpass',                // LastPass
        'data-bitwarden',               // Bitwarden
        'data-dashlane',                // Dashlane
        'data-1password',               // 1Password
      ];
      
      // Remove problematic attributes
      extensionAttributes.forEach(attr => {
        if (body.hasAttribute(attr)) {
          body.removeAttribute(attr);
        }
      });
      
      // Also check for any data-* attributes that might be added by extensions
      Array.from(body.attributes).forEach(attr => {
        if (attr.name.startsWith('data-') && 
            (attr.name.includes('extension') || 
             attr.name.includes('plugin') || 
             attr.name.includes('addon'))) {
          body.removeAttribute(attr.name);
        }
      });
    };

    // Clean up immediately
    cleanupExtensionAttributes();

    // Set up mutation observer to handle dynamically added attributes
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'attributes' && mutation.target === document.body) {
          const attributeName = mutation.attributeName;
          
          // Check if it's a problematic extension attribute
          if (attributeName && (
            attributeName.startsWith('cz-') ||
            attributeName.startsWith('data-gr-') ||
            attributeName.startsWith('data-gramm') ||
            attributeName.startsWith('data-ms-') ||
            attributeName.startsWith('data-lt-') ||
            attributeName.startsWith('data-darkreader') ||
            attributeName.includes('extension') ||
            attributeName.includes('plugin') ||
            attributeName.includes('addon')
          )) {
            document.body.removeAttribute(attributeName);
          }
        }
      });
    });

    observer.observe(document.body, {
      attributes: true,
      attributeOldValue: true
    });

    // Cleanup function
    return () => {
      observer.disconnect();
    };
  }, []);

  return isHydrated;
}

/**
 * Component wrapper that prevents hydration mismatches
 * Use this to wrap components that might be affected by browser extensions
 */
export function HydrationBoundary({ children, fallback = null }) {
  const isHydrated = useHydrationFix();
  
  if (!isHydrated) {
    return fallback;
  }
  
  return children;
}

/**
 * Hook to safely use browser-only APIs
 * Returns true only after hydration is complete
 */
export function useIsClient() {
  const [isClient, setIsClient] = useState(false);
  
  useEffect(() => {
    setIsClient(true);
  }, []);
  
  return isClient;
}
