// Integration Client for Voice Agent Communication
// This utility handles communication between the main platform and voice agent

import {
  INTEGRATION_CONFIG,
  ERROR_CODES,
  createApiResponse,
  logIntegrationEvent
} from '../../shared/integration-config.js';
import { supabase } from '../services/supabaseClient.js';

export class IntegrationClient {
  constructor() {
    this.apiKey = process.env.INTEGRATION_API_KEY || 'main-platform-api-key';
    this.retryCount = 0;
    this.maxRetries = INTEGRATION_CONFIG.MAX_RETRIES;
  }

  // Create headers for API requests
  createHeaders() {
    return {
      'Content-Type': 'application/json',
      [INTEGRATION_CONFIG.API_KEY_HEADER]: this.apiKey
    };
  }

  // Make API request with retry logic
  async makeRequest(url, options = {}) {
    const requestOptions = {
      ...options,
      headers: {
        ...this.createHeaders(),
        ...options.headers
      },
      signal: AbortSignal.timeout(INTEGRATION_CONFIG.REQUEST_TIMEOUT)
    };

    for (let attempt = 0; attempt <= this.maxRetries; attempt++) {
      try {
        const response = await fetch(url, requestOptions);

        if (response.ok) {
          return await response.json();
        }

        // If it's a client error (4xx), don't retry
        if (response.status >= 400 && response.status < 500) {
          const errorData = await response.json().catch(() => ({}));
          throw new Error(`Client error ${response.status}: ${errorData.error?.message || 'Unknown error'}`);
        }

        // For server errors (5xx), retry
        if (attempt < this.maxRetries) {
          await this.delay(INTEGRATION_CONFIG.RETRY_DELAY * (attempt + 1));
          continue;
        }

        throw new Error(`Server error ${response.status} after ${this.maxRetries} retries`);

      } catch (error) {
        if (attempt < this.maxRetries && !error.name?.includes('AbortError')) {
          await this.delay(INTEGRATION_CONFIG.RETRY_DELAY * (attempt + 1));
          continue;
        }
        throw error;
      }
    }
  }

  // Delay utility for retries
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // Create interview session in voice agent
  async createInterviewSession(sessionData) {
    try {
      logIntegrationEvent('info', 'main_platform', 'creating_voice_agent_session',
        'Creating interview session in voice agent', sessionData.sessionId);

      const url = `${INTEGRATION_CONFIG.VOICE_AGENT_URL}/api/integration/interview/start`;

      const response = await this.makeRequest(url, {
        method: 'POST',
        body: JSON.stringify(sessionData)
      });

      if (!response.success) {
        throw new Error(response.error?.message || 'Failed to create session in voice agent');
      }

      logIntegrationEvent('info', 'main_platform', 'voice_agent_session_created',
        'Interview session created in voice agent', sessionData.sessionId, {
        voiceAgentInterviewId: response.data.voiceAgentInterviewId
      });

      return response.data;

    } catch (error) {
      logIntegrationEvent('error', 'main_platform', 'voice_agent_session_failed',
        'Failed to create session in voice agent', sessionData.sessionId, {
        error: error.message
      });
      throw error;
    }
  }

  // Get interview status from voice agent
  async getInterviewStatus(sessionId, voiceAgentInterviewId = null) {
    try {
      const params = new URLSearchParams();
      if (sessionId) params.append('sessionId', sessionId);
      if (voiceAgentInterviewId) params.append('voiceAgentInterviewId', voiceAgentInterviewId);

      const url = `${INTEGRATION_CONFIG.VOICE_AGENT_URL}/api/integration/interview/start?${params}`;

      const response = await this.makeRequest(url, {
        method: 'GET'
      });

      if (!response.success) {
        throw new Error(response.error?.message || 'Failed to get interview status');
      }

      return response.data;

    } catch (error) {
      logIntegrationEvent('error', 'main_platform', 'get_status_failed',
        'Failed to get interview status from voice agent', sessionId, {
        error: error.message
      });
      throw error;
    }
  }

  // Check voice agent health
  async checkVoiceAgentHealth() {
    try {
      const url = `${INTEGRATION_CONFIG.VOICE_AGENT_URL}/api/integration/health`;

      const response = await this.makeRequest(url, {
        method: 'GET'
      });

      return response.data;

    } catch (error) {
      logIntegrationEvent('warning', 'main_platform', 'voice_agent_health_check_failed',
        'Voice agent health check failed', null, {
        error: error.message
      });
      return {
        status: 'unhealthy',
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }

  // Generate interview questions
  async generateQuestions(jobData) {
    try {
      logIntegrationEvent('info', 'main_platform', 'generating_questions',
        'Generating interview questions', null, {
        jobTitle: jobData.jobTitle
      });

      const response = await fetch('/api/generate-questions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          jobTitle: jobData.jobTitle,
          jobDescription: jobData.jobDescription,
          requiredSkills: jobData.requiredSkills,
          experienceLevel: jobData.experienceLevel,
          questionCount: jobData.questionCount || INTEGRATION_CONFIG.DEFAULT_QUESTION_COUNT
        })
      });

      const questionsData = await response.json();

      if (!response.ok) {
        throw new Error(questionsData.error || 'Failed to generate questions');
      }

      // Format questions for voice agent
      const formattedQuestions = (questionsData.questions || []).map((q, index) => ({
        id: `q_${index + 1}`,
        question: q,
        type: 'technical',
        difficulty: 'medium',
        expectedDuration: 120,
        followUpAllowed: true,
        metadata: {
          generated: true,
          jobTitle: jobData.jobTitle
        }
      }));

      logIntegrationEvent('info', 'main_platform', 'questions_generated',
        'Interview questions generated successfully', null, {
        questionCount: formattedQuestions.length
      });

      return formattedQuestions;

    } catch (error) {
      logIntegrationEvent('error', 'main_platform', 'question_generation_failed',
        'Failed to generate interview questions', null, {
        error: error.message
      });

      // Return default questions as fallback
      return [
        {
          id: 'q_default_1',
          question: 'Tell me about yourself and your experience.',
          type: 'behavioral',
          difficulty: 'easy',
          expectedDuration: 120,
          followUpAllowed: true,
          metadata: { fallback: true }
        },
        {
          id: 'q_default_2',
          question: `What interests you about the ${jobData.jobTitle} position?`,
          type: 'behavioral',
          difficulty: 'easy',
          expectedDuration: 120,
          followUpAllowed: true,
          metadata: { fallback: true }
        }
      ];
    }
  }

  // Start interview process (creates session and redirects to voice agent)
  async startInterview(interviewData) {
    try {
      const sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

      logIntegrationEvent('info', 'main_platform', 'starting_interview_process',
        'Starting integrated interview process', sessionId, {
        userEmail: interviewData.userEmail,
        jobTitle: interviewData.jobTitle
      });

      // Generate questions if not provided
      let questions = interviewData.questions;
      if (!questions || questions.length === 0) {
        questions = await this.generateQuestions({
          jobTitle: interviewData.jobTitle,
          jobDescription: interviewData.jobDescription,
          requiredSkills: interviewData.requiredSkills,
          experienceLevel: interviewData.experienceLevel,
          questionCount: interviewData.questionCount
        });
      }

      // Prepare session data for voice agent
      const sessionData = {
        sessionId,
        userEmail: interviewData.userEmail,
        userName: interviewData.userName,
        jobTitle: interviewData.jobTitle,
        jobDescription: interviewData.jobDescription,
        companyName: interviewData.companyName,
        interviewType: interviewData.interviewType || 'job_application',
        duration: interviewData.duration || INTEGRATION_CONFIG.DEFAULT_INTERVIEW_DURATION,
        experienceLevel: interviewData.experienceLevel,
        requiredSkills: interviewData.requiredSkills,
        companyCriteria: interviewData.companyCriteria,
        questions,
        questionCount: questions.length,
        callbackUrl: `${INTEGRATION_CONFIG.MAIN_PLATFORM_URL}/api/integration/interview/feedback`,
        webhookSecret: process.env.INTEGRATION_SHARED_SECRET || 'webhook-secret'
      };

      // Create session in main platform database
      // Using the existing supabase client from services
      console.log('Creating session with data:', {
        sessionId,
        userEmail: interviewData.userEmail,
        jobTitle: interviewData.jobTitle,
        companyName: interviewData.companyName
      });

      const { data: session, error: sessionError } = await supabase
        .from('Interview_Sessions')
        .insert([{
          main_interview_id: sessionId,
          job_id: interviewData.jobId || null,
          company_id: interviewData.companyId || null,
          user_email: interviewData.userEmail,
          user_name: interviewData.userName,
          job_title: interviewData.jobTitle,
          company_name: interviewData.companyName,
          interview_type: sessionData.interviewType,
          status: 'pending',
          session_data: {
            jobDescription: interviewData.jobDescription,
            requiredSkills: interviewData.requiredSkills,
            experienceLevel: interviewData.experienceLevel,
            companyCriteria: interviewData.companyCriteria,
            duration: sessionData.duration,
            questionCount: questions.length,
            questions: questions,
            voiceAgentUrl: `${INTEGRATION_CONFIG.VOICE_AGENT_URL}/interview/external?sessionId=${sessionId}`,
            integrationVersion: INTEGRATION_CONFIG.INTEGRATION_VERSION
          }
        }])
        .select()
        .single();

      if (sessionError) {
        console.error('Session creation error:', sessionError);
        const errorMessage = sessionError.message || sessionError.details || JSON.stringify(sessionError);
        throw new Error(`Failed to create session: ${errorMessage}`);
      }

      // Create session in voice agent
      let voiceAgentResponse;
      try {
        voiceAgentResponse = await this.createInterviewSession(sessionData);
      } catch (voiceAgentError) {
        console.error('Voice agent error:', voiceAgentError);
        // Update session status to indicate voice agent failure
        await supabase
          .from('Interview_Sessions')
          .update({
            status: 'voice_agent_error',
            session_data: {
              ...session.session_data,
              error: voiceAgentError.message,
              errorTime: new Date().toISOString()
            }
          })
          .eq('id', session.id);

        throw new Error(`Voice agent connection failed: ${voiceAgentError.message}. Please ensure the voice agent is running on ${INTEGRATION_CONFIG.VOICE_AGENT_URL}`);
      }

      // Update session with voice agent details
      await supabase
        .from('Interview_Sessions')
        .update({
          voice_agent_session_id: voiceAgentResponse.voiceAgentInterviewId,
          status: 'ready'
        })
        .eq('id', session.id);

      const voiceAgentUrl = `${INTEGRATION_CONFIG.VOICE_AGENT_URL}/interview/external?sessionId=${sessionId}`;

      logIntegrationEvent('info', 'main_platform', 'interview_process_ready',
        'Interview process ready, redirecting to voice agent', sessionId, {
        voiceAgentUrl: voiceAgentUrl
      });

      return {
        sessionId,
        voiceAgentUrl: voiceAgentUrl,
        voiceAgentInterviewId: voiceAgentResponse.voiceAgentInterviewId,
        estimatedDuration: sessionData.duration,
        questionCount: questions.length
      };

    } catch (error) {
      logIntegrationEvent('error', 'main_platform', 'interview_start_failed',
        'Failed to start interview process', null, {
        error: error.message
      });
      throw error;
    }
  }

  // Get feedback status
  async getFeedbackStatus(sessionId) {
    try {
      // Using the existing supabase client from services

      const { data: feedback, error } = await supabase
        .from('Interview_Feedback_Bridge')
        .select('*')
        .eq('main_interview_id', sessionId)
        .single();

      if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
        throw new Error(`Database error: ${error.message}`);
      }

      return feedback;

    } catch (error) {
      logIntegrationEvent('error', 'main_platform', 'get_feedback_status_failed',
        'Failed to get feedback status', sessionId, {
        error: error.message
      });
      throw error;
    }
  }
}

// Export singleton instance
export const integrationClient = new IntegrationClient();
