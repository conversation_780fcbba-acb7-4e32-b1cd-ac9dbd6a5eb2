// Development Configuration
// This file contains development-specific configurations to improve the development experience

if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  // Suppress specific console warnings in development
  const originalConsoleError = console.error;
  const originalConsoleWarn = console.warn;

  console.error = (...args) => {
    // Suppress hydration mismatch warnings caused by browser extensions
    const message = args[0];
    if (typeof message === 'string') {
      // Suppress hydration warnings
      if (message.includes('Hydration failed') || 
          message.includes('hydrated but some attributes') ||
          message.includes('cz-shortcut-listen') ||
          message.includes('data-gr-') ||
          message.includes('data-gramm') ||
          message.includes('browser extension')) {
        return;
      }
      
      // Suppress React DevTools warnings about browser extensions
      if (message.includes('Warning: Extra attributes from the server') ||
          message.includes('Warning: Prop') && message.includes('did not match')) {
        return;
      }
    }
    
    // Call original console.error for other messages
    originalConsoleError.apply(console, args);
  };

  console.warn = (...args) => {
    const message = args[0];
    if (typeof message === 'string') {
      // Suppress specific development warnings
      if (message.includes('useLayoutEffect does nothing on the server') ||
          message.includes('Warning: validateDOMNesting') ||
          message.includes('browser extension')) {
        return;
      }
    }
    
    // Call original console.warn for other messages
    originalConsoleWarn.apply(console, args);
  };

  // Add a global error handler for unhandled hydration errors
  window.addEventListener('error', (event) => {
    if (event.message && (
      event.message.includes('Hydration') ||
      event.message.includes('hydrated but some attributes') ||
      event.message.includes('cz-shortcut-listen')
    )) {
      event.preventDefault();
      event.stopPropagation();
      return false;
    }
  });

  // Add a global unhandled rejection handler
  window.addEventListener('unhandledrejection', (event) => {
    if (event.reason && event.reason.message && (
      event.reason.message.includes('Hydration') ||
      event.reason.message.includes('hydrated but some attributes')
    )) {
      event.preventDefault();
      return false;
    }
  });

  console.log('🔧 Development mode: Hydration warnings suppressed for browser extensions');
}
