"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(main)/jobs/[id]/page",{

/***/ "(app-pages-browser)/./app/(main)/jobs/[id]/page.jsx":
/*!***************************************!*\
  !*** ./app/(main)/jobs/[id]/page.jsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _app_provider__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/app/provider */ \"(app-pages-browser)/./app/provider.jsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.jsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.jsx\");\n/* harmony import */ var _services_supabaseClient__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/services/supabaseClient */ \"(app-pages-browser)/./services/supabaseClient.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BriefcaseBusiness_Building2_Calendar_Clock_DollarSign_ExternalLink_MapPin_Sparkles_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BriefcaseBusiness,Building2,Calendar,Clock,DollarSign,ExternalLink,MapPin,Sparkles,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BriefcaseBusiness_Building2_Calendar_Clock_DollarSign_ExternalLink_MapPin_Sparkles_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BriefcaseBusiness,Building2,Calendar,Clock,DollarSign,ExternalLink,MapPin,Sparkles,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building-2.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BriefcaseBusiness_Building2_Calendar_Clock_DollarSign_ExternalLink_MapPin_Sparkles_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BriefcaseBusiness,Building2,Calendar,Clock,DollarSign,ExternalLink,MapPin,Sparkles,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/briefcase-business.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BriefcaseBusiness_Building2_Calendar_Clock_DollarSign_ExternalLink_MapPin_Sparkles_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BriefcaseBusiness,Building2,Calendar,Clock,DollarSign,ExternalLink,MapPin,Sparkles,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BriefcaseBusiness_Building2_Calendar_Clock_DollarSign_ExternalLink_MapPin_Sparkles_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BriefcaseBusiness,Building2,Calendar,Clock,DollarSign,ExternalLink,MapPin,Sparkles,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BriefcaseBusiness_Building2_Calendar_Clock_DollarSign_ExternalLink_MapPin_Sparkles_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BriefcaseBusiness,Building2,Calendar,Clock,DollarSign,ExternalLink,MapPin,Sparkles,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BriefcaseBusiness_Building2_Calendar_Clock_DollarSign_ExternalLink_MapPin_Sparkles_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BriefcaseBusiness,Building2,Calendar,Clock,DollarSign,ExternalLink,MapPin,Sparkles,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BriefcaseBusiness_Building2_Calendar_Clock_DollarSign_ExternalLink_MapPin_Sparkles_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BriefcaseBusiness,Building2,Calendar,Clock,DollarSign,ExternalLink,MapPin,Sparkles,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.jsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./components/ui/separator.jsx\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nfunction JobDetailPage() {\n    var _job_Companies, _job_Companies1, _job_Companies2, _job_Companies3, _job_Companies4, _job_Companies5;\n    _s();\n    const [job, setJob] = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(true);\n    const [applying, setApplying] = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(false);\n    const { id } = (0,next_navigation__WEBPACK_IMPORTED_MODULE_6__.useParams)();\n    const { user } = (0,_app_provider__WEBPACK_IMPORTED_MODULE_1__.useUser)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_7__.useEffect)({\n        \"JobDetailPage.useEffect\": ()=>{\n            if (id) {\n                fetchJobDetails();\n            }\n        }\n    }[\"JobDetailPage.useEffect\"], [\n        id\n    ]);\n    const fetchJobDetails = async ()=>{\n        try {\n            setLoading(true);\n            // Try with specific foreign key first\n            let { data, error } = await _services_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.from('Jobs').select(\"\\n                    *,\\n                    Companies!Jobs_company_id_fkey(id, name, picture, industry_type, description, website)\\n                \").eq('id', id).single();\n            // If that fails, try alternative foreign key\n            if (error && (error.code === 'PGRST200' || error.code === 'PGRST201')) {\n                console.log(\"Trying alternative foreign key...\");\n                const { data: altData, error: altError } = await _services_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.from('Jobs').select(\"\\n                        *,\\n                        Companies!jobs_company_id_fkey(id, name, picture, industry_type, description, website)\\n                    \").eq('id', id).single();\n                if (!altError) {\n                    data = altData;\n                    error = null;\n                } else {\n                    console.log(\"Both foreign keys failed, fetching separately...\");\n                    // Fetch job and company separately\n                    const { data: jobData, error: jobError } = await _services_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.from('Jobs').select('*').eq('id', id).single();\n                    if (jobError) {\n                        console.error(\"Error fetching job:\", jobError);\n                        console.error(\"Full error details:\", JSON.stringify(jobError, null, 2));\n                        sonner__WEBPACK_IMPORTED_MODULE_10__.toast.error(\"Failed to load job details\");\n                        return;\n                    }\n                    if (jobData && jobData.company_id) {\n                        const { data: companyData, error: companyError } = await _services_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.from('Companies').select('id, name, picture, industry_type, description, website').eq('id', jobData.company_id).single();\n                        if (companyError) {\n                            console.error(\"Error fetching company:\", companyError);\n                            // Continue with job data only\n                            data = {\n                                ...jobData,\n                                Companies: null\n                            };\n                        } else {\n                            data = {\n                                ...jobData,\n                                Companies: companyData\n                            };\n                        }\n                        error = null;\n                    } else {\n                        data = jobData;\n                        error = null;\n                    }\n                }\n            }\n            if (error) {\n                console.error(\"Error fetching job details:\", error);\n                console.error(\"Full error details:\", JSON.stringify(error, null, 2));\n                sonner__WEBPACK_IMPORTED_MODULE_10__.toast.error(\"Failed to load job details\");\n            } else {\n                console.log(\"Fetched job details:\", data);\n                setJob(data);\n            }\n        } catch (error) {\n            console.error(\"Exception fetching job details:\", error);\n            console.error(\"Full exception details:\", JSON.stringify(error, null, 2));\n            sonner__WEBPACK_IMPORTED_MODULE_10__.toast.error(\"An error occurred while loading the job\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleApply = async ()=>{\n        if (!user) {\n            sonner__WEBPACK_IMPORTED_MODULE_10__.toast.error(\"Please sign in to apply for this job\");\n            router.push('/auth');\n            return;\n        }\n        // Check if user has credits for job application\n        try {\n            const { data: canApply, error: creditError } = await _services_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.rpc('can_apply_for_job', {\n                user_id_param: String(user.id)\n            });\n            if (creditError) {\n                console.error('Error checking credits:', creditError);\n                console.error('Full credit error:', JSON.stringify(creditError, null, 2));\n                // If function doesn't exist or has issues, continue without credit check\n                if (creditError.code === '42883' || creditError.code === 'PGRST203' || creditError.code === 'PGRST202') {\n                    console.log('Credit function not available or has issues, proceeding with application');\n                } else {\n                    console.log('Credit check failed, but proceeding with application anyway');\n                }\n            } else if (!canApply) {\n                sonner__WEBPACK_IMPORTED_MODULE_10__.toast.error('Insufficient credits for job application. Please purchase more credits to continue.');\n                router.push('/billing');\n                return;\n            }\n        } catch (error) {\n            console.error('Exception checking credits:', error);\n            // Continue without credit check if there's an error\n            console.log('Proceeding with application despite credit check error');\n        }\n        try {\n            var _job_Companies;\n            setApplying(true);\n            console.log(\"Starting job application process...\");\n            // Deduct credits for job application\n            try {\n                const { data: creditDeducted, error: creditDeductionError } = await _services_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.rpc('deduct_credits_for_application', {\n                    user_id_param: String(user.id),\n                    job_id_param: String(job.id)\n                });\n                if (creditDeductionError) {\n                    console.error(\"Error deducting credits:\", creditDeductionError);\n                    console.error(\"Full credit deduction error:\", JSON.stringify(creditDeductionError, null, 2));\n                    // If function doesn't exist or has issues, continue without credit deduction\n                    if (creditDeductionError.code === '42883' || creditDeductionError.code === 'PGRST203' || creditDeductionError.code === 'PGRST202') {\n                        console.log('Credit deduction function not available or has issues, proceeding with application');\n                    } else {\n                        console.log('Credit deduction failed, but proceeding with application anyway');\n                    }\n                } else if (!creditDeducted) {\n                    console.log(\"Credit deduction returned false, but proceeding with application anyway\");\n                }\n            } catch (error) {\n                console.error(\"Exception deducting credits:\", error);\n                // Continue without credit deduction if there's an error\n                console.log('Proceeding with application despite credit deduction error');\n            }\n            // Create a simple interview ID for the session\n            const interview_id = \"interview_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substr(2, 9));\n            // Store job and user data in localStorage for the interview\n            const interviewData = {\n                interview_id: interview_id,\n                job_id: job.id,\n                company_id: job.company_id,\n                user_id: user.id,\n                user_email: user.email,\n                user_name: user.name || 'Candidate',\n                job_title: job.job_title,\n                job_description: job.job_description,\n                company_name: ((_job_Companies = job.Companies) === null || _job_Companies === void 0 ? void 0 : _job_Companies.name) || 'Company',\n                experience_level: job.experience_level || 'Mid-Level',\n                required_skills: job.required_skills || '',\n                ai_criteria: job.ai_criteria || '',\n                question_count: job.question_count || 10,\n                duration: 30\n            };\n            // Store in localStorage for the interview page to access\n            localStorage.setItem(\"interview_\".concat(interview_id), JSON.stringify(interviewData));\n            console.log(\"Interview data prepared:\", interviewData);\n            sonner__WEBPACK_IMPORTED_MODULE_10__.toast.success(\"Application submitted! Redirecting to interview preparation...\");\n            // Redirect to the original interview form page\n            setTimeout(()=>{\n                router.push(\"/interview/\".concat(interview_id));\n            }, 1500);\n        } catch (error) {\n            console.error(\"Error applying for job:\", error);\n            sonner__WEBPACK_IMPORTED_MODULE_10__.toast.error(\"Failed to apply for this job: \".concat(error.message || \"Unknown error\"));\n            setApplying(false);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto py-12 flex justify-center items-center min-h-[60vh]\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                lineNumber: 221,\n                columnNumber: 17\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n            lineNumber: 220,\n            columnNumber: 13\n        }, this);\n    }\n    if (!job) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto py-12 text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-2xl font-bold mb-4\",\n                    children: \"Job Not Found\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                    lineNumber: 229,\n                    columnNumber: 17\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"mb-6\",\n                    children: \"The job you're looking for doesn't exist or has been removed.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                    lineNumber: 230,\n                    columnNumber: 17\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                    href: \"/jobs\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BriefcaseBusiness_Building2_Calendar_Clock_DollarSign_ExternalLink_MapPin_Sparkles_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                className: \"mr-2 h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                lineNumber: 233,\n                                columnNumber: 25\n                            }, this),\n                            \"Back to Jobs\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                        lineNumber: 232,\n                        columnNumber: 21\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                    lineNumber: 231,\n                    columnNumber: 17\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n            lineNumber: 228,\n            columnNumber: 13\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto py-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                href: \"/jobs\",\n                className: \"inline-flex items-center text-sm text-muted-foreground hover:text-foreground mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BriefcaseBusiness_Building2_Calendar_Clock_DollarSign_ExternalLink_MapPin_Sparkles_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                        className: \"mr-1 h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                        lineNumber: 244,\n                        columnNumber: 17\n                    }, this),\n                    \"Back to all jobs\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                lineNumber: 243,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-start\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                        className: \"text-2xl\",\n                                                        children: job.job_title\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                        lineNumber: 254,\n                                                        columnNumber: 37\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                                        className: \"flex items-center mt-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BriefcaseBusiness_Building2_Calendar_Clock_DollarSign_ExternalLink_MapPin_Sparkles_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"h-4 w-4 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                                lineNumber: 256,\n                                                                columnNumber: 41\n                                                            }, this),\n                                                            ((_job_Companies = job.Companies) === null || _job_Companies === void 0 ? void 0 : _job_Companies.name) || \"Company\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                        lineNumber: 255,\n                                                        columnNumber: 37\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                lineNumber: 253,\n                                                columnNumber: 33\n                                            }, this),\n                                            ((_job_Companies1 = job.Companies) === null || _job_Companies1 === void 0 ? void 0 : _job_Companies1.picture) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-16 w-16 rounded-lg bg-gray-100 flex items-center justify-center overflow-hidden\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                    src: job.Companies.picture,\n                                                    alt: job.Companies.name,\n                                                    className: \"h-full w-full object-cover\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 262,\n                                                    columnNumber: 41\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                lineNumber: 261,\n                                                columnNumber: 37\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                        lineNumber: 252,\n                                        columnNumber: 29\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 251,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-wrap gap-2 mb-6\",\n                                            children: [\n                                                job.employment_type && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                                    variant: \"outline\",\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BriefcaseBusiness_Building2_Calendar_Clock_DollarSign_ExternalLink_MapPin_Sparkles_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            className: \"h-3 w-3 mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 275,\n                                                            columnNumber: 41\n                                                        }, this),\n                                                        job.employment_type\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 274,\n                                                    columnNumber: 37\n                                                }, this),\n                                                job.location_type && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                                    variant: \"outline\",\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BriefcaseBusiness_Building2_Calendar_Clock_DollarSign_ExternalLink_MapPin_Sparkles_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                            className: \"h-3 w-3 mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 281,\n                                                            columnNumber: 41\n                                                        }, this),\n                                                        job.location_type\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 280,\n                                                    columnNumber: 37\n                                                }, this),\n                                                job.experience_level && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                                    variant: \"outline\",\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BriefcaseBusiness_Building2_Calendar_Clock_DollarSign_ExternalLink_MapPin_Sparkles_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            className: \"h-3 w-3 mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 287,\n                                                            columnNumber: 41\n                                                        }, this),\n                                                        job.experience_level\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 286,\n                                                    columnNumber: 37\n                                                }, this),\n                                                job.salary_range && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                                    variant: \"outline\",\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BriefcaseBusiness_Building2_Calendar_Clock_DollarSign_ExternalLink_MapPin_Sparkles_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            className: \"h-3 w-3 mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 293,\n                                                            columnNumber: 41\n                                                        }, this),\n                                                        job.salary_range\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 292,\n                                                    columnNumber: 37\n                                                }, this),\n                                                job.application_deadline && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                                    variant: \"outline\",\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BriefcaseBusiness_Building2_Calendar_Clock_DollarSign_ExternalLink_MapPin_Sparkles_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                            className: \"h-3 w-3 mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 299,\n                                                            columnNumber: 41\n                                                        }, this),\n                                                        \"Due: \",\n                                                        new Date(job.application_deadline).toLocaleDateString()\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 298,\n                                                    columnNumber: 37\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 272,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-lg font-semibold mb-2\",\n                                                            children: \"Job Description\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 307,\n                                                            columnNumber: 37\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"whitespace-pre-line\",\n                                                            children: job.job_description || \"No description provided.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 308,\n                                                            columnNumber: 37\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 306,\n                                                    columnNumber: 33\n                                                }, this),\n                                                job.required_skills && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-lg font-semibold mb-2\",\n                                                            children: \"Required Skills\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 313,\n                                                            columnNumber: 41\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"whitespace-pre-line\",\n                                                            children: job.required_skills\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 314,\n                                                            columnNumber: 41\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 312,\n                                                    columnNumber: 37\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 305,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 271,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardFooter, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        className: \"w-full\",\n                                        size: \"lg\",\n                                        onClick: handleApply,\n                                        disabled: applying,\n                                        children: applying ? \"Starting Application...\" : \"Apply with AI Interview\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                        lineNumber: 320,\n                                        columnNumber: 29\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 319,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                            lineNumber: 250,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                        lineNumber: 249,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            children: \"About the Company\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 335,\n                                            columnNumber: 29\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                        lineNumber: 334,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BriefcaseBusiness_Building2_Calendar_Clock_DollarSign_ExternalLink_MapPin_Sparkles_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                            className: \"h-5 w-5 mr-2 text-muted-foreground\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 340,\n                                                            columnNumber: 37\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: (_job_Companies2 = job.Companies) === null || _job_Companies2 === void 0 ? void 0 : _job_Companies2.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 341,\n                                                            columnNumber: 37\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 339,\n                                                    columnNumber: 33\n                                                }, this),\n                                                ((_job_Companies3 = job.Companies) === null || _job_Companies3 === void 0 ? void 0 : _job_Companies3.industry_type) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BriefcaseBusiness_Building2_Calendar_Clock_DollarSign_ExternalLink_MapPin_Sparkles_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            className: \"h-5 w-5 mr-2 text-muted-foreground\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 345,\n                                                            columnNumber: 41\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: job.Companies.industry_type\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 346,\n                                                            columnNumber: 41\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 344,\n                                                    columnNumber: 37\n                                                }, this),\n                                                ((_job_Companies4 = job.Companies) === null || _job_Companies4 === void 0 ? void 0 : _job_Companies4.website) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: job.Companies.website,\n                                                        target: \"_blank\",\n                                                        rel: \"noopener noreferrer\",\n                                                        className: \"text-primary hover:underline\",\n                                                        children: \"Visit Website\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                        lineNumber: 351,\n                                                        columnNumber: 41\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 350,\n                                                    columnNumber: 37\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_9__.Separator, {}, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 361,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-muted-foreground\",\n                                                        children: ((_job_Companies5 = job.Companies) === null || _job_Companies5 === void 0 ? void 0 : _job_Companies5.description) || \"No company description available.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                        lineNumber: 363,\n                                                        columnNumber: 37\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 362,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 338,\n                                            columnNumber: 29\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                        lineNumber: 337,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                lineNumber: 333,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                className: \"mt-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BriefcaseBusiness_Building2_Calendar_Clock_DollarSign_ExternalLink_MapPin_Sparkles_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    className: \"h-5 w-5 mr-2 text-yellow-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 374,\n                                                    columnNumber: 33\n                                                }, this),\n                                                \"AI Interview Process\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 373,\n                                            columnNumber: 29\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                        lineNumber: 372,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-muted-foreground mb-4\",\n                                                children: \"This job uses AI-powered interviews to assess candidates. Here's how it works:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                lineNumber: 379,\n                                                columnNumber: 29\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                                className: \"space-y-2 list-decimal list-inside text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: 'Click \"Apply with AI Interview\" to start'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                        lineNumber: 383,\n                                                        columnNumber: 33\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            \"Complete a \",\n                                                            job.question_count || 10,\n                                                            \"-question interview\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                        lineNumber: 384,\n                                                        columnNumber: 33\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Your responses will be analyzed by AI\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                        lineNumber: 385,\n                                                        columnNumber: 33\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"The company will review your results\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                        lineNumber: 386,\n                                                        columnNumber: 33\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"You'll be contacted if selected for next steps\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                        lineNumber: 387,\n                                                        columnNumber: 33\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                lineNumber: 382,\n                                                columnNumber: 29\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                        lineNumber: 378,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                lineNumber: 371,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                        lineNumber: 332,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                lineNumber: 248,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n        lineNumber: 242,\n        columnNumber: 9\n    }, this);\n}\n_s(JobDetailPage, \"Rlqc4I/yV/2ErfcAy4635RRVPMM=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_6__.useParams,\n        _app_provider__WEBPACK_IMPORTED_MODULE_1__.useUser,\n        next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter\n    ];\n});\n_c = JobDetailPage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (JobDetailPage);\nvar _c;\n$RefreshReg$(_c, \"JobDetailPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/(main)/jobs/[id]/page.jsx\n"));

/***/ })

});