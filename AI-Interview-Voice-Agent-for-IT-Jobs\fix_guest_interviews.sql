-- Fix database schema to support guest interviews
-- This script updates the Interviews table and RLS policies

-- First, let's update the Interviews table to support guest interviews
ALTER TABLE "Interviews" 
ADD COLUMN IF NOT EXISTS is_guest BOOLEAN DEFAULT FALSE;

-- Update RLS policies to allow guest interviews
DROP POLICY IF EXISTS "Interviews can be inserted by authenticated users" ON "Interviews";

-- Create new policy that allows both authenticated users and guest interviews
CREATE POLICY "Interviews can be inserted by authenticated users or guests"
  ON "Interviews" FOR INSERT
  WITH CHECK (
    auth.uid() IS NOT NULL OR 
    (is_guest = true AND userEmail = '<EMAIL>')
  );

-- Also update the interview-feedback table to support guest feedback
ALTER TABLE "interview-feedback" 
ADD COLUMN IF NOT EXISTS is_guest BOOLEAN DEFAULT FALSE;

-- Update RLS policy for interview-feedback to allow guest feedback
DROP POLICY IF EXISTS "interview-feedback can be inserted by authenticated users" ON "interview-feedback";

CREATE POLICY "interview-feedback can be inserted by authenticated users or guests"
  ON "interview-feedback" FOR INSERT
  WITH CHECK (
    auth.uid() IS NOT NULL OR 
    (is_guest = true AND userEmail = '<EMAIL>')
  );

-- Create a view for easier querying of guest interviews
CREATE OR REPLACE VIEW guest_interviews AS
SELECT 
  id,
  interview_id,
  userEmail,
  userName,
  jobPosition,
  jobDescription,
  type,
  duration,
  experienceLevel,
  requiredSkills,
  companyCriteria,
  questionList,
  is_guest,
  created_at
FROM "Interviews"
WHERE is_guest = true;

-- Grant access to the view
GRANT SELECT ON guest_interviews TO anon;
GRANT SELECT ON guest_interviews TO authenticated;
