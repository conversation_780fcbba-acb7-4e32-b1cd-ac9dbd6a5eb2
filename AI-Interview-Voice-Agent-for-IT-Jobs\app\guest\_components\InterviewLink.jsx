import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { ArrowLeft, Calendar, Clock, Copy, List, Mail, Plus, LogIn, ExternalLink, CheckCircle, Share2, Timer } from 'lucide-react'
import Image from 'next/image'
import Link from 'next/link'
import React, { useState } from 'react'
import { toast } from 'sonner'

function InterviewLink({ interview_id, formData }) {
    const [copied, setCopied] = useState(false);

    // Get the current host URL dynamically
    const getBaseUrl = () => {
        if (typeof window !== 'undefined') {
            return window.location.origin;
        }
        return process.env.NEXT_PUBLIC_HOST_URL || 'http://localhost:3000';
    };

    const url = `${getBaseUrl()}/interview/${interview_id}`;

    const GetInterviewUrl = () => {
        return url;
    }

    const onCopyLink = async () => {
        await navigator.clipboard.writeText(url);
        setCopied(true);
        toast.success('Interview link copied to clipboard!');
        setTimeout(() => setCopied(false), 3000);
    }

    const onNavigateToInterview = () => {
        window.open(url, '_blank');
    }

    return (
        <div className="space-y-6">
            {/* Success Header */}
            <Card className="border-green-200 bg-green-50">
                <CardContent className="pt-6">
                    <div className='flex items-center gap-5'>
                        <div className="relative">
                            <CheckCircle className="w-16 h-16 text-green-500" />
                        </div>
                        <div>
                            <h2 className='font-bold text-2xl text-green-600'>Awesome!</h2>
                            <p className='text-green-700'>Your Practice Interview is Ready</p>
                            <Badge variant="secondary" className="mt-2 bg-green-100 text-green-800">
                                <Timer className="w-3 h-3 mr-1" />
                                Valid for 30 Days
                            </Badge>
                        </div>
                    </div>
                </CardContent>
            </Card>

            {/* Interview Details */}
            <Card>
                <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                        <List className="w-5 h-5" />
                        Interview Details
                    </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                    <div className='flex items-center gap-3'>
                        <Calendar className='h-4 w-4 text-blue-500' />
                        <span className='text-sm text-gray-600'>Position:</span>
                        <Badge variant="outline">{formData?.jobPosition}</Badge>
                    </div>
                    <div className='flex items-center gap-3'>
                        <Clock className='h-4 w-4 text-blue-500' />
                        <span className='text-sm text-gray-600'>Duration:</span>
                        <Badge variant="outline">{formData?.duration}</Badge>
                    </div>
                    <div className='flex items-center gap-3'>
                        <List className='h-4 w-4 text-blue-500' />
                        <span className='text-sm text-gray-600'>Type:</span>
                        <Badge variant="outline">{Array.isArray(formData?.type) ? formData.type.join(', ') : formData?.type}</Badge>
                    </div>
                </CardContent>
            </Card>

            {/* Interview Link */}
            <Card>
                <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                        <ExternalLink className="w-5 h-5" />
                        Interview Link
                    </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                    <div className='flex gap-3 items-center'>
                        <Input
                            value={GetInterviewUrl()}
                            readOnly
                            className="font-mono text-sm"
                        />
                        <Button
                            onClick={onCopyLink}
                            variant={copied ? "default" : "outline"}
                            className={copied ? "bg-green-500 hover:bg-green-600" : ""}
                        >
                            {copied ? <CheckCircle className="w-4 h-4 mr-2" /> : <Copy className="w-4 h-4 mr-2" />}
                            {copied ? 'Copied!' : 'Copy'}
                        </Button>
                    </div>

                    {/* Direct Navigation Button */}
                    <div className="pt-2">
                        <Button
                            onClick={onNavigateToInterview}
                            size="lg"
                            className="w-full bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700"
                        >
                            <ExternalLink className="w-5 h-5 mr-2" />
                            Start Interview Now
                        </Button>
                        <p className="text-xs text-gray-500 text-center mt-2">
                            Opens in a new tab for the best interview experience
                        </p>
                    </div>
                </CardContent>
            </Card>

            {/* Share Options */}
            <Card>
                <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                        <Share2 className="w-5 h-5" />
                        Share Interview
                    </CardTitle>
                </CardHeader>
                <CardContent>
                    <div className='grid grid-cols-3 gap-4'>
                        <Button
                            variant={'outline'}
                            className='flex flex-col h-16 gap-1'
                            onClick={() => {
                                const subject = `Practice Interview: ${formData?.jobPosition}`;
                                const body = `I've created a practice interview for you!\n\nPosition: ${formData?.jobPosition}\nDuration: ${formData?.duration}\n\nInterview Link: ${url}`;
                                window.open(`mailto:?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`);
                            }}
                        >
                            <Mail className="w-5 h-5" />
                            <span className="text-xs">Email</span>
                        </Button>
                        <Button
                            variant={'outline'}
                            className='flex flex-col h-16 gap-1'
                            onClick={() => {
                                const text = `Practice Interview: ${formData?.jobPosition}\n${url}`;
                                window.open(`https://wa.me/?text=${encodeURIComponent(text)}`);
                            }}
                        >
                            <Share2 className="w-5 h-5" />
                            <span className="text-xs">WhatsApp</span>
                        </Button>
                        <Button
                            variant={'outline'}
                            className='flex flex-col h-16 gap-1'
                            onClick={() => {
                                if (navigator.share) {
                                    navigator.share({
                                        title: `Practice Interview: ${formData?.jobPosition}`,
                                        text: `Check out this practice interview!`,
                                        url: url
                                    });
                                } else {
                                    onCopyLink();
                                }
                            }}
                        >
                            <Share2 className="w-5 h-5" />
                            <span className="text-xs">Share</span>
                        </Button>
                    </div>
                </CardContent>
            </Card>

            {/* Navigation Buttons */}
            <div className='flex w-full gap-4 justify-between'>
                <Link href={'/guest'} className="flex-1">
                    <Button variant={'outline'} className="w-full">
                        <ArrowLeft className="w-4 h-4 mr-2" />
                        Back to Dashboard
                    </Button>
                </Link>
                <Link href={'/guest'} className="flex-1">
                    <Button className="w-full">
                        <Plus className="w-4 h-4 mr-2" />
                        Create New Interview
                    </Button>
                </Link>
            </div>

            {/* Encourage account creation */}
            <div className='mt-8 p-5 bg-blue-50 rounded-lg w-full text-center'>
                <h3 className='font-semibold mb-2'>Want to apply for real jobs?</h3>
                <p className='text-sm text-gray-600 mb-4'>
                    Create an account to apply for jobs, track applications, and get hired!
                </p>
                <Link href={'/auth'}>
                    <Button>
                        <LogIn className='mr-2 h-4 w-4' />
                        Create Account
                    </Button>
                </Link>
            </div>
        </div>
    )
}

export default InterviewLink
