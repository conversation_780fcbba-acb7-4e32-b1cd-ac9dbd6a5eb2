{"name": "ai-interview-scheduler-ai-voice-agent", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@google/generative-ai": "^0.24.1", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.6", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-tabs": "^1.1.11", "@radix-ui/react-toggle-group": "^1.1.9", "@radix-ui/react-tooltip": "^1.1.8", "@supabase/supabase-js": "^2.49.4", "@vapi-ai/web": "^2.2.5", "axios": "^1.8.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.487.0", "moment": "^2.30.1", "motion": "^12.6.3", "next": "15.2.4", "next-themes": "^0.4.6", "openai": "^4.91.1", "react": "^18.0.0", "react-dom": "^18.0.0", "sonner": "^2.0.3", "tailwind-merge": "^3.1.0", "tw-animate-css": "^1.2.5", "uuid": "^11.1.0"}, "devDependencies": {"@tailwindcss/postcss": "^4", "tailwindcss": "^4"}}