"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/interview/external/page",{

/***/ "(app-pages-browser)/./app/interview/external/page.tsx":
/*!*****************************************!*\
  !*** ./app/interview/external/page.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ExternalInterviewPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building_CheckCircle_Clock_Mic_MicOff_User_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building,CheckCircle,Clock,Mic,MicOff,User,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building_CheckCircle_Clock_Mic_MicOff_User_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building,CheckCircle,Clock,Mic,MicOff,User,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building_CheckCircle_Clock_Mic_MicOff_User_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building,CheckCircle,Clock,Mic,MicOff,User,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building_CheckCircle_Clock_Mic_MicOff_User_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building,CheckCircle,Clock,Mic,MicOff,User,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building_CheckCircle_Clock_Mic_MicOff_User_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building,CheckCircle,Clock,Mic,MicOff,User,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mic.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building_CheckCircle_Clock_Mic_MicOff_User_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building,CheckCircle,Clock,Mic,MicOff,User,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mic-off.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building_CheckCircle_Clock_Mic_MicOff_User_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building,CheckCircle,Clock,Mic,MicOff,User,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/volume-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building_CheckCircle_Clock_Mic_MicOff_User_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building,CheckCircle,Clock,Mic,MicOff,User,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _lib_gemini_voice__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/gemini-voice */ \"(app-pages-browser)/./lib/gemini-voice.ts\");\n/* harmony import */ var _shared_integration_config_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../../../shared/integration-config.js */ \"(app-pages-browser)/../shared/integration-config.js\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction ExternalInterviewPage() {\n    _s();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const sessionId = searchParams.get('sessionId');\n    const encodedData = searchParams.get('data');\n    // Session and interview state\n    const [sessionData, setSessionData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Interview state\n    const [isInterviewStarted, setIsInterviewStarted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isInterviewCompleted, setIsInterviewCompleted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentQuestionIndex, setCurrentQuestionIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [currentQuestion, setCurrentQuestion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isListening, setIsListening] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isSpeaking, setIsSpeaking] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [transcript, setTranscript] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [conversationHistory, setConversationHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Voice service\n    const voiceServiceRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [microphonePermission, setMicrophonePermission] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('prompt');\n    // Interview metrics\n    const [startTime, setStartTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [elapsedTime, setElapsedTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // Load session data on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ExternalInterviewPage.useEffect\": ()=>{\n            if (!sessionId && !encodedData) {\n                setError('No session ID or data provided');\n                setIsLoading(false);\n                return;\n            }\n            if (encodedData) {\n                // Handle direct data passing from main platform\n                loadDirectSessionData();\n            } else if (sessionId) {\n                // Handle traditional sessionId approach\n                loadSessionData();\n            }\n        }\n    }[\"ExternalInterviewPage.useEffect\"], [\n        sessionId,\n        encodedData\n    ]);\n    // Timer for elapsed time\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ExternalInterviewPage.useEffect\": ()=>{\n            let interval = null;\n            if (isInterviewStarted && startTime && !isInterviewCompleted) {\n                interval = setInterval({\n                    \"ExternalInterviewPage.useEffect\": ()=>{\n                        setElapsedTime(Math.floor((Date.now() - startTime.getTime()) / 1000));\n                    }\n                }[\"ExternalInterviewPage.useEffect\"], 1000);\n            }\n            return ({\n                \"ExternalInterviewPage.useEffect\": ()=>{\n                    if (interval) clearInterval(interval);\n                }\n            })[\"ExternalInterviewPage.useEffect\"];\n        }\n    }[\"ExternalInterviewPage.useEffect\"], [\n        isInterviewStarted,\n        startTime,\n        isInterviewCompleted\n    ]);\n    const loadDirectSessionData = async ()=>{\n        try {\n            setIsLoading(true);\n            if (!encodedData) {\n                throw new Error('No encoded data provided');\n            }\n            (0,_shared_integration_config_js__WEBPACK_IMPORTED_MODULE_7__.logIntegrationEvent)('info', 'voice_agent', 'loading_direct_session_data', 'Loading interview session from encoded data', 'direct_data');\n            // Decode the data passed from main platform\n            // First decode from base64, then decode URI component, then parse JSON\n            const decodedString = decodeURIComponent(atob(encodedData));\n            const interviewData = JSON.parse(decodedString);\n            console.log('📥 Received interview data:', interviewData);\n            // Transform the data to match our ExternalSessionData interface\n            const transformedSessionData = {\n                sessionId: interviewData.sessionId,\n                mainPlatformSession: true,\n                userEmail: interviewData.userEmail,\n                userName: interviewData.userName,\n                jobTitle: interviewData.jobTitle,\n                jobDescription: interviewData.jobDescription,\n                companyName: interviewData.companyName,\n                interviewType: interviewData.interviewType,\n                duration: interviewData.duration,\n                experienceLevel: interviewData.experienceLevel,\n                requiredSkills: interviewData.requiredSkills,\n                companyCriteria: interviewData.companyCriteria,\n                questions: interviewData.questions || [],\n                questionCount: interviewData.questionCount,\n                callbackUrl: \"\".concat(_shared_integration_config_js__WEBPACK_IMPORTED_MODULE_7__.INTEGRATION_CONFIG.MAIN_PLATFORM_URL, \"/api/integration/feedback\"),\n                webhookSecret: 'webhook-secret',\n                voiceAgentUserId: 'external_user',\n                voiceAgentInterviewId: interviewData.sessionId,\n                createdAt: new Date().toISOString()\n            };\n            setSessionData(transformedSessionData);\n            setIsLoading(false);\n            (0,_shared_integration_config_js__WEBPACK_IMPORTED_MODULE_7__.logIntegrationEvent)('info', 'voice_agent', 'session_data_loaded', 'Successfully loaded session data from encoded data', transformedSessionData.sessionId);\n        } catch (error) {\n            console.error('❌ Failed to load direct session data:', error);\n            setError(\"Failed to load interview data: \".concat(error.message));\n            setIsLoading(false);\n            (0,_shared_integration_config_js__WEBPACK_IMPORTED_MODULE_7__.logIntegrationEvent)('error', 'voice_agent', 'session_loading_failed', 'Failed to load session data from encoded data', 'direct_data', {\n                error: error.message\n            });\n        }\n    };\n    const loadSessionData = async ()=>{\n        try {\n            var _result_data;\n            setIsLoading(true);\n            (0,_shared_integration_config_js__WEBPACK_IMPORTED_MODULE_7__.logIntegrationEvent)('info', 'voice_agent', 'loading_external_session', 'Loading external interview session', sessionId);\n            // In a real implementation, you would fetch this from your backend\n            // For now, we'll check if there's session data in localStorage or make an API call\n            // Try to get session data from the main platform\n            const response = await fetch(\"\".concat(_shared_integration_config_js__WEBPACK_IMPORTED_MODULE_7__.INTEGRATION_CONFIG.MAIN_PLATFORM_URL, \"/api/integration/interview/create?sessionId=\").concat(sessionId), {\n                headers: {\n                    [_shared_integration_config_js__WEBPACK_IMPORTED_MODULE_7__.INTEGRATION_CONFIG.API_KEY_HEADER]: process.env.NEXT_PUBLIC_INTEGRATION_API_KEY || 'voice-agent-api-key'\n                }\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to load session: \".concat(response.status));\n            }\n            const result = await response.json();\n            if (!result.success) {\n                var _result_error;\n                throw new Error(((_result_error = result.error) === null || _result_error === void 0 ? void 0 : _result_error.message) || 'Failed to load session data');\n            }\n            // For demo purposes, create mock session data\n            // In production, this would come from the API response\n            const mockSessionData = {\n                sessionId: sessionId,\n                mainPlatformSession: true,\n                userEmail: '<EMAIL>',\n                userName: 'Interview Candidate',\n                jobTitle: 'Software Engineer',\n                jobDescription: 'Full-stack development role',\n                companyName: 'Tech Company',\n                interviewType: 'job_application',\n                duration: 30,\n                experienceLevel: 'Mid-Level',\n                requiredSkills: 'JavaScript, React, Node.js',\n                companyCriteria: 'Strong problem-solving skills',\n                questions: ((_result_data = result.data) === null || _result_data === void 0 ? void 0 : _result_data.questions) || [\n                    {\n                        id: 'q1',\n                        question: 'Tell me about yourself and your experience with software development.',\n                        type: 'behavioral',\n                        difficulty: 'easy',\n                        expectedDuration: 120,\n                        followUpAllowed: true,\n                        metadata: {}\n                    },\n                    {\n                        id: 'q2',\n                        question: 'Describe a challenging technical problem you solved recently.',\n                        type: 'technical',\n                        difficulty: 'medium',\n                        expectedDuration: 180,\n                        followUpAllowed: true,\n                        metadata: {}\n                    }\n                ],\n                questionCount: 2,\n                callbackUrl: \"\".concat(_shared_integration_config_js__WEBPACK_IMPORTED_MODULE_7__.INTEGRATION_CONFIG.MAIN_PLATFORM_URL, \"/api/integration/interview/feedback\"),\n                webhookSecret: 'webhook-secret',\n                voiceAgentUserId: 'user-123',\n                voiceAgentInterviewId: 'interview-456',\n                createdAt: new Date().toISOString()\n            };\n            setSessionData(mockSessionData);\n            (0,_shared_integration_config_js__WEBPACK_IMPORTED_MODULE_7__.logIntegrationEvent)('info', 'voice_agent', 'external_session_loaded', 'External interview session loaded successfully', sessionId, {\n                jobTitle: mockSessionData.jobTitle,\n                questionCount: mockSessionData.questions.length\n            });\n        } catch (err) {\n            const errorMessage = err instanceof Error ? err.message : 'Failed to load session';\n            setError(errorMessage);\n            (0,_shared_integration_config_js__WEBPACK_IMPORTED_MODULE_7__.logIntegrationEvent)('error', 'voice_agent', 'session_load_failed', 'Failed to load external session', sessionId, {\n                error: errorMessage\n            });\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const checkMicrophonePermission = async ()=>{\n        try {\n            const stream = await navigator.mediaDevices.getUserMedia({\n                audio: true\n            });\n            stream.getTracks().forEach((track)=>track.stop());\n            setMicrophonePermission('granted');\n            return true;\n        } catch (error) {\n            setMicrophonePermission('denied');\n            return false;\n        }\n    };\n    const initializeVoiceService = async ()=>{\n        try {\n            voiceServiceRef.current = new _lib_gemini_voice__WEBPACK_IMPORTED_MODULE_6__.GeminiVoiceService();\n            voiceServiceRef.current.enableVoiceActivityDetection(true);\n            voiceServiceRef.current.setInterruptCallback(()=>{\n                setIsSpeaking(false);\n            });\n            return true;\n        } catch (error) {\n            console.error('Failed to initialize voice service:', error);\n            return false;\n        }\n    };\n    const startInterview = async ()=>{\n        if (!sessionData) return;\n        try {\n            var _voiceServiceRef_current;\n            const hasPermission = await checkMicrophonePermission();\n            if (!hasPermission) {\n                setError('Microphone permission is required for the interview');\n                return;\n            }\n            const voiceInitialized = await initializeVoiceService();\n            if (!voiceInitialized) {\n                setError('Failed to initialize voice service');\n                return;\n            }\n            setIsInterviewStarted(true);\n            setStartTime(new Date());\n            (0,_shared_integration_config_js__WEBPACK_IMPORTED_MODULE_7__.logIntegrationEvent)('info', 'voice_agent', 'external_interview_started', 'External interview started', sessionId, {\n                userEmail: sessionData.userEmail,\n                jobTitle: sessionData.jobTitle\n            });\n            // Start with welcome message\n            const welcomeMessage = \"Hello \".concat(sessionData.userName, \", welcome to your interview for the \").concat(sessionData.jobTitle, \" position at \").concat(sessionData.companyName, \". I'm your AI interviewer, and I'll be asking you \").concat(sessionData.questions.length, \" questions today. Are you ready to begin?\");\n            await ((_voiceServiceRef_current = voiceServiceRef.current) === null || _voiceServiceRef_current === void 0 ? void 0 : _voiceServiceRef_current.speak(welcomeMessage));\n            setConversationHistory([\n                {\n                    speaker: 'ai',\n                    message: welcomeMessage,\n                    timestamp: new Date()\n                }\n            ]);\n            // Start listening for response\n            setTimeout(()=>{\n                startListening();\n            }, 1000);\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : 'Failed to start interview';\n            setError(errorMessage);\n            (0,_shared_integration_config_js__WEBPACK_IMPORTED_MODULE_7__.logIntegrationEvent)('error', 'voice_agent', 'interview_start_failed', 'Failed to start external interview', sessionId, {\n                error: errorMessage\n            });\n        }\n    };\n    const startListening = async ()=>{\n        if (!voiceServiceRef.current || isListening) return;\n        try {\n            setIsListening(true);\n            const response = await voiceServiceRef.current.startListening();\n            setTranscript(response);\n            setIsListening(false);\n            if (response.trim()) {\n                await processUserResponse(response);\n            }\n        } catch (error) {\n            console.error('Error listening:', error);\n            setIsListening(false);\n        }\n    };\n    const processUserResponse = async (response)=>{\n        if (!sessionData || !voiceServiceRef.current) return;\n        try {\n            var _sessionData_questions_currentQuestionIndex;\n            setIsSpeaking(true);\n            // Add user response to conversation\n            const userMessage = {\n                speaker: 'user',\n                message: response,\n                timestamp: new Date(),\n                questionId: (_sessionData_questions_currentQuestionIndex = sessionData.questions[currentQuestionIndex]) === null || _sessionData_questions_currentQuestionIndex === void 0 ? void 0 : _sessionData_questions_currentQuestionIndex.id\n            };\n            setConversationHistory((prev)=>[\n                    ...prev,\n                    userMessage\n                ]);\n            // Check if we should ask the next question or end the interview\n            if (currentQuestionIndex < sessionData.questions.length - 1) {\n                // Ask next question\n                const nextQuestionIndex = currentQuestionIndex + 1;\n                const nextQuestion = sessionData.questions[nextQuestionIndex];\n                setCurrentQuestionIndex(nextQuestionIndex);\n                setCurrentQuestion(nextQuestion.question);\n                await voiceServiceRef.current.speak(nextQuestion.question);\n                const aiMessage = {\n                    speaker: 'ai',\n                    message: nextQuestion.question,\n                    timestamp: new Date(),\n                    questionId: nextQuestion.id\n                };\n                setConversationHistory((prev)=>[\n                        ...prev,\n                        aiMessage\n                    ]);\n                setIsSpeaking(false);\n                // Continue listening\n                setTimeout(()=>{\n                    startListening();\n                }, 1000);\n            } else {\n                // End interview\n                await endInterview();\n            }\n        } catch (error) {\n            console.error('Error processing response:', error);\n            setIsSpeaking(false);\n        }\n    };\n    const endInterview = async ()=>{\n        if (!sessionData) return;\n        try {\n            setIsInterviewCompleted(true);\n            const endMessage = \"Thank you for completing the interview. Your responses have been recorded and will be reviewed. You can now close this window.\";\n            if (voiceServiceRef.current) {\n                await voiceServiceRef.current.speak(endMessage);\n            }\n            setConversationHistory((prev)=>[\n                    ...prev,\n                    {\n                        speaker: 'ai',\n                        message: endMessage,\n                        timestamp: new Date()\n                    }\n                ]);\n            // Submit feedback to main platform\n            await submitFeedback();\n            (0,_shared_integration_config_js__WEBPACK_IMPORTED_MODULE_7__.logIntegrationEvent)('info', 'voice_agent', 'external_interview_completed', 'External interview completed', sessionId, {\n                duration: elapsedTime,\n                questionsAnswered: currentQuestionIndex + 1\n            });\n        } catch (error) {\n            console.error('Error ending interview:', error);\n            (0,_shared_integration_config_js__WEBPACK_IMPORTED_MODULE_7__.logIntegrationEvent)('error', 'voice_agent', 'interview_end_failed', 'Failed to end external interview', sessionId, {\n                error: error.message\n            });\n        }\n    };\n    const submitFeedback = async ()=>{\n        if (!sessionData) return;\n        try {\n            // Calculate basic scores (in a real implementation, this would be more sophisticated)\n            const overallScore = Math.floor(Math.random() * 40) + 60 // 60-100 range\n            ;\n            const questionsAnswered = currentQuestionIndex + 1;\n            const feedbackData = {\n                sessionId: sessionData.sessionId,\n                voiceAgentInterviewId: sessionData.voiceAgentInterviewId,\n                userEmail: sessionData.userEmail,\n                userName: sessionData.userName,\n                overallScore,\n                technicalScore: overallScore + Math.floor(Math.random() * 10) - 5,\n                communicationScore: overallScore + Math.floor(Math.random() * 10) - 5,\n                problemSolvingScore: overallScore + Math.floor(Math.random() * 10) - 5,\n                recommendation: overallScore >= 80 ? 'hire' : overallScore >= 65 ? 'maybe' : 'reject',\n                strengths: [\n                    'Good communication',\n                    'Relevant experience'\n                ],\n                areasForImprovement: [\n                    'Could provide more specific examples'\n                ],\n                overallAssessment: 'Candidate showed good understanding of the role requirements.',\n                duration: Math.floor(elapsedTime / 60),\n                questionsAsked: sessionData.questions.length,\n                questionsAnswered,\n                conversationTranscript: conversationHistory,\n                completedAt: new Date().toISOString(),\n                interviewQuality: 'good',\n                callbackUrl: sessionData.callbackUrl,\n                webhookSecret: sessionData.webhookSecret\n            };\n            // Submit to voice agent's feedback endpoint which will forward to main platform\n            const response = await fetch('/api/integration/feedback', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json',\n                    [_shared_integration_config_js__WEBPACK_IMPORTED_MODULE_7__.INTEGRATION_CONFIG.API_KEY_HEADER]: process.env.NEXT_PUBLIC_INTEGRATION_API_KEY || 'voice-agent-api-key'\n                },\n                body: JSON.stringify(feedbackData)\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to submit feedback: \".concat(response.status));\n            }\n            (0,_shared_integration_config_js__WEBPACK_IMPORTED_MODULE_7__.logIntegrationEvent)('info', 'voice_agent', 'feedback_submitted', 'Feedback submitted successfully', sessionId, {\n                overallScore,\n                recommendation: feedbackData.recommendation\n            });\n        } catch (error) {\n            (0,_shared_integration_config_js__WEBPACK_IMPORTED_MODULE_7__.logIntegrationEvent)('error', 'voice_agent', 'feedback_submission_failed', 'Failed to submit feedback', sessionId, {\n                error: error.message\n            });\n        }\n    };\n    const formatTime = (seconds)=>{\n        const mins = Math.floor(seconds / 60);\n        const secs = seconds % 60;\n        return \"\".concat(mins, \":\").concat(secs.toString().padStart(2, '0'));\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                className: \"w-full max-w-md\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                    className: \"p-6 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\interview\\\\external\\\\page.tsx\",\n                            lineNumber: 509,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"Loading interview session...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\interview\\\\external\\\\page.tsx\",\n                            lineNumber: 510,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\interview\\\\external\\\\page.tsx\",\n                    lineNumber: 508,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\interview\\\\external\\\\page.tsx\",\n                lineNumber: 507,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\interview\\\\external\\\\page.tsx\",\n            lineNumber: 506,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-red-50 to-pink-100 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                className: \"w-full max-w-md\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                    className: \"p-6 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_CheckCircle_Clock_Mic_MicOff_User_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            className: \"h-12 w-12 text-red-500 mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\interview\\\\external\\\\page.tsx\",\n                            lineNumber: 522,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold text-red-700 mb-2\",\n                            children: \"Error\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\interview\\\\external\\\\page.tsx\",\n                            lineNumber: 523,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-red-600 mb-4\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\interview\\\\external\\\\page.tsx\",\n                            lineNumber: 524,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            onClick: ()=>router.push('/'),\n                            variant: \"outline\",\n                            children: \"Go Back\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\interview\\\\external\\\\page.tsx\",\n                            lineNumber: 525,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\interview\\\\external\\\\page.tsx\",\n                    lineNumber: 521,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\interview\\\\external\\\\page.tsx\",\n                lineNumber: 520,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\interview\\\\external\\\\page.tsx\",\n            lineNumber: 519,\n            columnNumber: 7\n        }, this);\n    }\n    if (!sessionData) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                className: \"w-full max-w-md\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                    className: \"p-6 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_CheckCircle_Clock_Mic_MicOff_User_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            className: \"h-12 w-12 text-gray-500 mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\interview\\\\external\\\\page.tsx\",\n                            lineNumber: 539,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"No session data available\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\interview\\\\external\\\\page.tsx\",\n                            lineNumber: 540,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\interview\\\\external\\\\page.tsx\",\n                    lineNumber: 538,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\interview\\\\external\\\\page.tsx\",\n                lineNumber: 537,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\interview\\\\external\\\\page.tsx\",\n            lineNumber: 536,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    className: \"mb-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_CheckCircle_Clock_Mic_MicOff_User_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\interview\\\\external\\\\page.tsx\",\n                                                    lineNumber: 556,\n                                                    columnNumber: 19\n                                                }, this),\n                                                sessionData.jobTitle,\n                                                \" Interview\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\interview\\\\external\\\\page.tsx\",\n                                            lineNumber: 555,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-muted-foreground\",\n                                            children: [\n                                                sessionData.companyName,\n                                                \" • \",\n                                                sessionData.experienceLevel,\n                                                \" Level\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\interview\\\\external\\\\page.tsx\",\n                                            lineNumber: 559,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\interview\\\\external\\\\page.tsx\",\n                                    lineNumber: 554,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-right\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2 text-sm text-muted-foreground\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_CheckCircle_Clock_Mic_MicOff_User_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\interview\\\\external\\\\page.tsx\",\n                                                    lineNumber: 565,\n                                                    columnNumber: 19\n                                                }, this),\n                                                sessionData.userName\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\interview\\\\external\\\\page.tsx\",\n                                            lineNumber: 564,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2 text-sm text-muted-foreground\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_CheckCircle_Clock_Mic_MicOff_User_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\interview\\\\external\\\\page.tsx\",\n                                                    lineNumber: 569,\n                                                    columnNumber: 19\n                                                }, this),\n                                                isInterviewStarted ? formatTime(elapsedTime) : \"\".concat(sessionData.duration, \" min\")\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\interview\\\\external\\\\page.tsx\",\n                                            lineNumber: 568,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\interview\\\\external\\\\page.tsx\",\n                                    lineNumber: 563,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\interview\\\\external\\\\page.tsx\",\n                            lineNumber: 553,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\interview\\\\external\\\\page.tsx\",\n                        lineNumber: 552,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\interview\\\\external\\\\page.tsx\",\n                    lineNumber: 551,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    className: \"mb-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        className: \"p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                variant: isInterviewCompleted ? \"default\" : isInterviewStarted ? \"secondary\" : \"outline\",\n                                                children: isInterviewCompleted ? \"Completed\" : isInterviewStarted ? \"In Progress\" : \"Ready to Start\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\interview\\\\external\\\\page.tsx\",\n                                                lineNumber: 582,\n                                                columnNumber: 17\n                                            }, this),\n                                            isInterviewStarted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-muted-foreground\",\n                                                children: [\n                                                    \"Question \",\n                                                    currentQuestionIndex + 1,\n                                                    \" of \",\n                                                    sessionData.questions.length\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\interview\\\\external\\\\page.tsx\",\n                                                lineNumber: 586,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\interview\\\\external\\\\page.tsx\",\n                                        lineNumber: 581,\n                                        columnNumber: 15\n                                    }, this),\n                                    isInterviewStarted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                variant: isListening ? \"default\" : \"outline\",\n                                                size: \"sm\",\n                                                onClick: startListening,\n                                                disabled: isSpeaking || isInterviewCompleted,\n                                                children: [\n                                                    isListening ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_CheckCircle_Clock_Mic_MicOff_User_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\interview\\\\external\\\\page.tsx\",\n                                                        lineNumber: 600,\n                                                        columnNumber: 36\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_CheckCircle_Clock_Mic_MicOff_User_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\interview\\\\external\\\\page.tsx\",\n                                                        lineNumber: 600,\n                                                        columnNumber: 66\n                                                    }, this),\n                                                    isListening ? \"Listening...\" : \"Start Speaking\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\interview\\\\external\\\\page.tsx\",\n                                                lineNumber: 594,\n                                                columnNumber: 19\n                                            }, this),\n                                            isSpeaking && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                variant: \"secondary\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_CheckCircle_Clock_Mic_MicOff_User_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"h-3 w-3 mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\interview\\\\external\\\\page.tsx\",\n                                                        lineNumber: 606,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    \"AI Speaking\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\interview\\\\external\\\\page.tsx\",\n                                                lineNumber: 605,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\interview\\\\external\\\\page.tsx\",\n                                        lineNumber: 593,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\interview\\\\external\\\\page.tsx\",\n                                lineNumber: 580,\n                                columnNumber: 13\n                            }, this),\n                            !isInterviewStarted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"mb-4\",\n                                        children: \"Ready to start your interview? Make sure you have a quiet environment and working microphone.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\interview\\\\external\\\\page.tsx\",\n                                        lineNumber: 616,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        onClick: startInterview,\n                                        size: \"lg\",\n                                        children: \"Start Interview\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\interview\\\\external\\\\page.tsx\",\n                                        lineNumber: 617,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\interview\\\\external\\\\page.tsx\",\n                                lineNumber: 615,\n                                columnNumber: 15\n                            }, this),\n                            isInterviewCompleted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_CheckCircle_Clock_Mic_MicOff_User_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"h-12 w-12 text-green-500 mx-auto mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\interview\\\\external\\\\page.tsx\",\n                                        lineNumber: 625,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold mb-2\",\n                                        children: \"Interview Completed!\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\interview\\\\external\\\\page.tsx\",\n                                        lineNumber: 626,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-muted-foreground\",\n                                        children: \"Thank you for your time. Your responses have been submitted for review.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\interview\\\\external\\\\page.tsx\",\n                                        lineNumber: 627,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\interview\\\\external\\\\page.tsx\",\n                                lineNumber: 624,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\interview\\\\external\\\\page.tsx\",\n                        lineNumber: 579,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\interview\\\\external\\\\page.tsx\",\n                    lineNumber: 578,\n                    columnNumber: 9\n                }, this),\n                conversationHistory.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                children: \"Interview Conversation\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\interview\\\\external\\\\page.tsx\",\n                                lineNumber: 639,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\interview\\\\external\\\\page.tsx\",\n                            lineNumber: 638,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4 max-h-96 overflow-y-auto\",\n                                children: conversationHistory.map((entry, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex \".concat(entry.speaker === 'user' ? 'justify-end' : 'justify-start'),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"max-w-[80%] p-3 rounded-lg \".concat(entry.speaker === 'user' ? 'bg-blue-500 text-white' : 'bg-gray-100 text-gray-900'),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm\",\n                                                    children: entry.message\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\interview\\\\external\\\\page.tsx\",\n                                                    lineNumber: 654,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs opacity-70 mt-1\",\n                                                    children: entry.timestamp.toLocaleTimeString()\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\interview\\\\external\\\\page.tsx\",\n                                                    lineNumber: 655,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\interview\\\\external\\\\page.tsx\",\n                                            lineNumber: 648,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, index, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\interview\\\\external\\\\page.tsx\",\n                                        lineNumber: 644,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\interview\\\\external\\\\page.tsx\",\n                                lineNumber: 642,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\interview\\\\external\\\\page.tsx\",\n                            lineNumber: 641,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\interview\\\\external\\\\page.tsx\",\n                    lineNumber: 637,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\interview\\\\external\\\\page.tsx\",\n            lineNumber: 549,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\interview\\\\external\\\\page.tsx\",\n        lineNumber: 548,\n        columnNumber: 5\n    }, this);\n}\n_s(ExternalInterviewPage, \"DY/X9P35/1iRPwo9f7biMD7qyrk=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = ExternalInterviewPage;\nvar _c;\n$RefreshReg$(_c, \"ExternalInterviewPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/interview/external/page.tsx\n"));

/***/ })

});