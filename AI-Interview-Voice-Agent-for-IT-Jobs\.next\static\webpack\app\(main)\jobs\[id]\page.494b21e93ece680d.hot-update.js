"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(main)/jobs/[id]/page",{

/***/ "(app-pages-browser)/./app/(main)/jobs/[id]/page.jsx":
/*!***************************************!*\
  !*** ./app/(main)/jobs/[id]/page.jsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _app_provider__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/app/provider */ \"(app-pages-browser)/./app/provider.jsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.jsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.jsx\");\n/* harmony import */ var _services_supabaseClient__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/services/supabaseClient */ \"(app-pages-browser)/./services/supabaseClient.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BriefcaseBusiness_Building2_Calendar_Clock_DollarSign_ExternalLink_MapPin_Sparkles_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BriefcaseBusiness,Building2,Calendar,Clock,DollarSign,ExternalLink,MapPin,Sparkles,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BriefcaseBusiness_Building2_Calendar_Clock_DollarSign_ExternalLink_MapPin_Sparkles_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BriefcaseBusiness,Building2,Calendar,Clock,DollarSign,ExternalLink,MapPin,Sparkles,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building-2.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BriefcaseBusiness_Building2_Calendar_Clock_DollarSign_ExternalLink_MapPin_Sparkles_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BriefcaseBusiness,Building2,Calendar,Clock,DollarSign,ExternalLink,MapPin,Sparkles,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/briefcase-business.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BriefcaseBusiness_Building2_Calendar_Clock_DollarSign_ExternalLink_MapPin_Sparkles_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BriefcaseBusiness,Building2,Calendar,Clock,DollarSign,ExternalLink,MapPin,Sparkles,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BriefcaseBusiness_Building2_Calendar_Clock_DollarSign_ExternalLink_MapPin_Sparkles_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BriefcaseBusiness,Building2,Calendar,Clock,DollarSign,ExternalLink,MapPin,Sparkles,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BriefcaseBusiness_Building2_Calendar_Clock_DollarSign_ExternalLink_MapPin_Sparkles_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BriefcaseBusiness,Building2,Calendar,Clock,DollarSign,ExternalLink,MapPin,Sparkles,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BriefcaseBusiness_Building2_Calendar_Clock_DollarSign_ExternalLink_MapPin_Sparkles_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BriefcaseBusiness,Building2,Calendar,Clock,DollarSign,ExternalLink,MapPin,Sparkles,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BriefcaseBusiness_Building2_Calendar_Clock_DollarSign_ExternalLink_MapPin_Sparkles_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BriefcaseBusiness,Building2,Calendar,Clock,DollarSign,ExternalLink,MapPin,Sparkles,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.jsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./components/ui/separator.jsx\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _lib_simple_integration_client__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/lib/simple-integration-client */ \"(app-pages-browser)/./lib/simple-integration-client.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nfunction JobDetailPage() {\n    var _job_Companies, _job_Companies1, _job_Companies2, _job_Companies3, _job_Companies4, _job_Companies5;\n    _s();\n    const [job, setJob] = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(true);\n    const [applying, setApplying] = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(false);\n    const { id } = (0,next_navigation__WEBPACK_IMPORTED_MODULE_6__.useParams)();\n    const { user } = (0,_app_provider__WEBPACK_IMPORTED_MODULE_1__.useUser)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_7__.useEffect)({\n        \"JobDetailPage.useEffect\": ()=>{\n            if (id) {\n                fetchJobDetails();\n            }\n        }\n    }[\"JobDetailPage.useEffect\"], [\n        id\n    ]);\n    const fetchJobDetails = async ()=>{\n        try {\n            setLoading(true);\n            // Try with specific foreign key first\n            let { data, error } = await _services_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.from('Jobs').select(\"\\n                    *,\\n                    Companies!Jobs_company_id_fkey(id, name, picture, industry_type, description, website)\\n                \").eq('id', id).single();\n            // If that fails, try alternative foreign key\n            if (error && (error.code === 'PGRST200' || error.code === 'PGRST201')) {\n                console.log(\"Trying alternative foreign key...\");\n                const { data: altData, error: altError } = await _services_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.from('Jobs').select(\"\\n                        *,\\n                        Companies!jobs_company_id_fkey(id, name, picture, industry_type, description, website)\\n                    \").eq('id', id).single();\n                if (!altError) {\n                    data = altData;\n                    error = null;\n                } else {\n                    console.log(\"Both foreign keys failed, fetching separately...\");\n                    // Fetch job and company separately\n                    const { data: jobData, error: jobError } = await _services_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.from('Jobs').select('*').eq('id', id).single();\n                    if (jobError) {\n                        console.error(\"Error fetching job:\", jobError);\n                        console.error(\"Full error details:\", JSON.stringify(jobError, null, 2));\n                        sonner__WEBPACK_IMPORTED_MODULE_10__.toast.error(\"Failed to load job details\");\n                        return;\n                    }\n                    if (jobData && jobData.company_id) {\n                        const { data: companyData, error: companyError } = await _services_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.from('Companies').select('id, name, picture, industry_type, description, website').eq('id', jobData.company_id).single();\n                        if (companyError) {\n                            console.error(\"Error fetching company:\", companyError);\n                            // Continue with job data only\n                            data = {\n                                ...jobData,\n                                Companies: null\n                            };\n                        } else {\n                            data = {\n                                ...jobData,\n                                Companies: companyData\n                            };\n                        }\n                        error = null;\n                    } else {\n                        data = jobData;\n                        error = null;\n                    }\n                }\n            }\n            if (error) {\n                console.error(\"Error fetching job details:\", error);\n                console.error(\"Full error details:\", JSON.stringify(error, null, 2));\n                sonner__WEBPACK_IMPORTED_MODULE_10__.toast.error(\"Failed to load job details\");\n            } else {\n                console.log(\"Fetched job details:\", data);\n                setJob(data);\n            }\n        } catch (error) {\n            console.error(\"Exception fetching job details:\", error);\n            console.error(\"Full exception details:\", JSON.stringify(error, null, 2));\n            sonner__WEBPACK_IMPORTED_MODULE_10__.toast.error(\"An error occurred while loading the job\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleApply = async ()=>{\n        if (!user) {\n            sonner__WEBPACK_IMPORTED_MODULE_10__.toast.error(\"Please sign in to apply for this job\");\n            router.push('/auth');\n            return;\n        }\n        // Check if user has credits for job application\n        try {\n            const { data: canApply, error: creditError } = await _services_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.rpc('can_apply_for_job', {\n                user_id_param: String(user.id)\n            });\n            if (creditError) {\n                console.error('Error checking credits:', creditError);\n                console.error('Full credit error:', JSON.stringify(creditError, null, 2));\n                // If function doesn't exist or has issues, continue without credit check\n                if (creditError.code === '42883' || creditError.code === 'PGRST203' || creditError.code === 'PGRST202') {\n                    console.log('Credit function not available or has issues, proceeding with application');\n                } else {\n                    console.log('Credit check failed, but proceeding with application anyway');\n                }\n            } else if (!canApply) {\n                sonner__WEBPACK_IMPORTED_MODULE_10__.toast.error('Insufficient credits for job application. Please purchase more credits to continue.');\n                router.push('/billing');\n                return;\n            }\n        } catch (error) {\n            console.error('Exception checking credits:', error);\n            // Continue without credit check if there's an error\n            console.log('Proceeding with application despite credit check error');\n        }\n        try {\n            var _job_Companies;\n            setApplying(true);\n            console.log(\"Starting integrated interview process...\");\n            // Prepare interview data for the integrated system\n            const interviewData = {\n                userEmail: user.email,\n                userName: user.name || 'Candidate',\n                jobTitle: job.job_title,\n                jobDescription: job.job_description,\n                companyName: ((_job_Companies = job.Companies) === null || _job_Companies === void 0 ? void 0 : _job_Companies.name) || 'Company',\n                interviewType: 'job_application',\n                duration: 30,\n                experienceLevel: job.experience_level || 'Mid-Level',\n                requiredSkills: job.required_skills || '',\n                companyCriteria: job.ai_criteria || '',\n                questionCount: job.question_count || 10,\n                jobId: job.id,\n                companyId: job.company_id\n            };\n            console.log(\"Interview data prepared:\", interviewData);\n            // Start the integrated interview process\n            const interviewSession = await _lib_simple_integration_client__WEBPACK_IMPORTED_MODULE_11__.simpleIntegrationClient.startInterview(interviewData);\n            console.log(\"Interview session created:\", interviewSession);\n            // Deduct credits for job application\n            try {\n                const { data: creditDeducted, error: creditDeductionError } = await _services_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.rpc('deduct_credits_for_application', {\n                    user_id_param: String(user.id),\n                    job_id_param: String(job.id)\n                });\n                if (creditDeductionError) {\n                    console.error(\"Error deducting credits:\", creditDeductionError);\n                    console.error(\"Full credit deduction error:\", JSON.stringify(creditDeductionError, null, 2));\n                    // If function doesn't exist or has issues, continue without credit deduction\n                    if (creditDeductionError.code === '42883' || creditDeductionError.code === 'PGRST203' || creditDeductionError.code === 'PGRST202') {\n                        console.log('Credit deduction function not available or has issues, proceeding with application');\n                    } else {\n                        console.log('Credit deduction failed, but proceeding with application anyway');\n                    }\n                } else if (!creditDeducted) {\n                    console.log(\"Credit deduction returned false, but proceeding with application anyway\");\n                }\n            } catch (error) {\n                console.error(\"Exception deducting credits:\", error);\n                // Continue without credit deduction if there's an error\n                console.log('Proceeding with application despite credit deduction error');\n            }\n            // Create a job submission record\n            const submissionData = {\n                job_id: job.id,\n                user_id: user.id,\n                user_name: user.name || 'Candidate',\n                user_email: user.email,\n                application_status: 'pending',\n                interview_completed: false\n            };\n            console.log(\"Creating job submission with data:\", submissionData);\n            const { data: submission, error: submissionError } = await _services_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.from('Job_Submissions').insert([\n                submissionData\n            ]).select();\n            if (submissionError) {\n                console.error(\"Error creating job submission:\", submissionError);\n                sonner__WEBPACK_IMPORTED_MODULE_10__.toast.warning(\"Application created but submission record failed\");\n            // Continue anyway since the interview session was created\n            } else {\n                console.log(\"Job submission created:\", submission);\n            }\n            sonner__WEBPACK_IMPORTED_MODULE_10__.toast.success(\"Application started! Redirecting to voice interview...\");\n            // Redirect to the voice agent interview\n            setTimeout(()=>{\n                window.open(interviewSession.voiceAgentUrl, '_blank');\n                // Also redirect to a status page where they can track the interview\n                router.push(\"/interview-status/\".concat(interviewSession.sessionId));\n            }, 1500);\n        } catch (error) {\n            console.error(\"Error applying for job:\", error);\n            sonner__WEBPACK_IMPORTED_MODULE_10__.toast.error(\"Failed to apply for this job: \".concat(error.message || \"Unknown error\"));\n            setApplying(false);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto py-12 flex justify-center items-center min-h-[60vh]\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                lineNumber: 246,\n                columnNumber: 17\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n            lineNumber: 245,\n            columnNumber: 13\n        }, this);\n    }\n    if (!job) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto py-12 text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-2xl font-bold mb-4\",\n                    children: \"Job Not Found\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                    lineNumber: 254,\n                    columnNumber: 17\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"mb-6\",\n                    children: \"The job you're looking for doesn't exist or has been removed.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                    lineNumber: 255,\n                    columnNumber: 17\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                    href: \"/jobs\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BriefcaseBusiness_Building2_Calendar_Clock_DollarSign_ExternalLink_MapPin_Sparkles_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                className: \"mr-2 h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                lineNumber: 258,\n                                columnNumber: 25\n                            }, this),\n                            \"Back to Jobs\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                        lineNumber: 257,\n                        columnNumber: 21\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                    lineNumber: 256,\n                    columnNumber: 17\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n            lineNumber: 253,\n            columnNumber: 13\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto py-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                href: \"/jobs\",\n                className: \"inline-flex items-center text-sm text-muted-foreground hover:text-foreground mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BriefcaseBusiness_Building2_Calendar_Clock_DollarSign_ExternalLink_MapPin_Sparkles_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        className: \"mr-1 h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                        lineNumber: 269,\n                        columnNumber: 17\n                    }, this),\n                    \"Back to all jobs\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                lineNumber: 268,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-start\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                        className: \"text-2xl\",\n                                                        children: job.job_title\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                        lineNumber: 279,\n                                                        columnNumber: 37\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                                        className: \"flex items-center mt-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BriefcaseBusiness_Building2_Calendar_Clock_DollarSign_ExternalLink_MapPin_Sparkles_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"h-4 w-4 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                                lineNumber: 281,\n                                                                columnNumber: 41\n                                                            }, this),\n                                                            ((_job_Companies = job.Companies) === null || _job_Companies === void 0 ? void 0 : _job_Companies.name) || \"Company\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                        lineNumber: 280,\n                                                        columnNumber: 37\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                lineNumber: 278,\n                                                columnNumber: 33\n                                            }, this),\n                                            ((_job_Companies1 = job.Companies) === null || _job_Companies1 === void 0 ? void 0 : _job_Companies1.picture) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-16 w-16 rounded-lg bg-gray-100 flex items-center justify-center overflow-hidden\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                    src: job.Companies.picture,\n                                                    alt: job.Companies.name,\n                                                    className: \"h-full w-full object-cover\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 287,\n                                                    columnNumber: 41\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                lineNumber: 286,\n                                                columnNumber: 37\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                        lineNumber: 277,\n                                        columnNumber: 29\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 276,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-wrap gap-2 mb-6\",\n                                            children: [\n                                                job.employment_type && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                                    variant: \"outline\",\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BriefcaseBusiness_Building2_Calendar_Clock_DollarSign_ExternalLink_MapPin_Sparkles_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                            className: \"h-3 w-3 mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 300,\n                                                            columnNumber: 41\n                                                        }, this),\n                                                        job.employment_type\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 299,\n                                                    columnNumber: 37\n                                                }, this),\n                                                job.location_type && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                                    variant: \"outline\",\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BriefcaseBusiness_Building2_Calendar_Clock_DollarSign_ExternalLink_MapPin_Sparkles_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            className: \"h-3 w-3 mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 306,\n                                                            columnNumber: 41\n                                                        }, this),\n                                                        job.location_type\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 305,\n                                                    columnNumber: 37\n                                                }, this),\n                                                job.experience_level && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                                    variant: \"outline\",\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BriefcaseBusiness_Building2_Calendar_Clock_DollarSign_ExternalLink_MapPin_Sparkles_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            className: \"h-3 w-3 mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 312,\n                                                            columnNumber: 41\n                                                        }, this),\n                                                        job.experience_level\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 311,\n                                                    columnNumber: 37\n                                                }, this),\n                                                job.salary_range && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                                    variant: \"outline\",\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BriefcaseBusiness_Building2_Calendar_Clock_DollarSign_ExternalLink_MapPin_Sparkles_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                            className: \"h-3 w-3 mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 318,\n                                                            columnNumber: 41\n                                                        }, this),\n                                                        job.salary_range\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 317,\n                                                    columnNumber: 37\n                                                }, this),\n                                                job.application_deadline && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                                    variant: \"outline\",\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BriefcaseBusiness_Building2_Calendar_Clock_DollarSign_ExternalLink_MapPin_Sparkles_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                            className: \"h-3 w-3 mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 324,\n                                                            columnNumber: 41\n                                                        }, this),\n                                                        \"Due: \",\n                                                        new Date(job.application_deadline).toLocaleDateString()\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 323,\n                                                    columnNumber: 37\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 297,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-lg font-semibold mb-2\",\n                                                            children: \"Job Description\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 332,\n                                                            columnNumber: 37\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"whitespace-pre-line\",\n                                                            children: job.job_description || \"No description provided.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 333,\n                                                            columnNumber: 37\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 331,\n                                                    columnNumber: 33\n                                                }, this),\n                                                job.required_skills && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-lg font-semibold mb-2\",\n                                                            children: \"Required Skills\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 338,\n                                                            columnNumber: 41\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"whitespace-pre-line\",\n                                                            children: job.required_skills\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 339,\n                                                            columnNumber: 41\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 337,\n                                                    columnNumber: 37\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 330,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 296,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardFooter, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        className: \"w-full\",\n                                        size: \"lg\",\n                                        onClick: handleApply,\n                                        disabled: applying,\n                                        children: applying ? \"Starting Application...\" : \"Apply with AI Interview\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                        lineNumber: 345,\n                                        columnNumber: 29\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 344,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                            lineNumber: 275,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                        lineNumber: 274,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            children: \"About the Company\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 360,\n                                            columnNumber: 29\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                        lineNumber: 359,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BriefcaseBusiness_Building2_Calendar_Clock_DollarSign_ExternalLink_MapPin_Sparkles_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            className: \"h-5 w-5 mr-2 text-muted-foreground\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 365,\n                                                            columnNumber: 37\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: (_job_Companies2 = job.Companies) === null || _job_Companies2 === void 0 ? void 0 : _job_Companies2.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 366,\n                                                            columnNumber: 37\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 364,\n                                                    columnNumber: 33\n                                                }, this),\n                                                ((_job_Companies3 = job.Companies) === null || _job_Companies3 === void 0 ? void 0 : _job_Companies3.industry_type) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BriefcaseBusiness_Building2_Calendar_Clock_DollarSign_ExternalLink_MapPin_Sparkles_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                            className: \"h-5 w-5 mr-2 text-muted-foreground\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 370,\n                                                            columnNumber: 41\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: job.Companies.industry_type\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 371,\n                                                            columnNumber: 41\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 369,\n                                                    columnNumber: 37\n                                                }, this),\n                                                ((_job_Companies4 = job.Companies) === null || _job_Companies4 === void 0 ? void 0 : _job_Companies4.website) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: job.Companies.website,\n                                                        target: \"_blank\",\n                                                        rel: \"noopener noreferrer\",\n                                                        className: \"text-primary hover:underline\",\n                                                        children: \"Visit Website\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                        lineNumber: 376,\n                                                        columnNumber: 41\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 375,\n                                                    columnNumber: 37\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_9__.Separator, {}, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 386,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-muted-foreground\",\n                                                        children: ((_job_Companies5 = job.Companies) === null || _job_Companies5 === void 0 ? void 0 : _job_Companies5.description) || \"No company description available.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                        lineNumber: 388,\n                                                        columnNumber: 37\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 387,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 363,\n                                            columnNumber: 29\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                        lineNumber: 362,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                lineNumber: 358,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                className: \"mt-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BriefcaseBusiness_Building2_Calendar_Clock_DollarSign_ExternalLink_MapPin_Sparkles_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                    className: \"h-5 w-5 mr-2 text-yellow-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 399,\n                                                    columnNumber: 33\n                                                }, this),\n                                                \"AI Interview Process\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 398,\n                                            columnNumber: 29\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                        lineNumber: 397,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-muted-foreground mb-4\",\n                                                children: \"This job uses AI-powered interviews to assess candidates. Here's how it works:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                lineNumber: 404,\n                                                columnNumber: 29\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                                className: \"space-y-2 list-decimal list-inside text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: 'Click \"Apply with AI Interview\" to start'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                        lineNumber: 408,\n                                                        columnNumber: 33\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            \"Complete a \",\n                                                            job.question_count || 10,\n                                                            \"-question interview\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                        lineNumber: 409,\n                                                        columnNumber: 33\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Your responses will be analyzed by AI\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                        lineNumber: 410,\n                                                        columnNumber: 33\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"The company will review your results\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                        lineNumber: 411,\n                                                        columnNumber: 33\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"You'll be contacted if selected for next steps\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                        lineNumber: 412,\n                                                        columnNumber: 33\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                lineNumber: 407,\n                                                columnNumber: 29\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                        lineNumber: 403,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                lineNumber: 396,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                        lineNumber: 357,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                lineNumber: 273,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n        lineNumber: 267,\n        columnNumber: 9\n    }, this);\n}\n_s(JobDetailPage, \"Rlqc4I/yV/2ErfcAy4635RRVPMM=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_6__.useParams,\n        _app_provider__WEBPACK_IMPORTED_MODULE_1__.useUser,\n        next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter\n    ];\n});\n_c = JobDetailPage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (JobDetailPage);\nvar _c;\n$RefreshReg$(_c, \"JobDetailPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/(main)/jobs/[id]/page.jsx\n"));

/***/ })

});