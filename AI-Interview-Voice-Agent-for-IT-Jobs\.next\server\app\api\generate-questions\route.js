/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/generate-questions/route";
exports.ids = ["app/api/generate-questions/route"];
exports.modules = {

/***/ "(rsc)/./app/api/generate-questions/route.js":
/*!*********************************************!*\
  !*** ./app/api/generate-questions/route.js ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var openai__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! openai */ \"(rsc)/./node_modules/openai/index.mjs\");\n\n\n// Initialize OpenAI with your API key\nlet openai;\ntry {\n    if (!process.env.OPENROUTER_API_KEY) {\n        console.error('OPENROUTER_API_KEY is not defined in environment variables');\n    } else {\n        console.log('Initializing OpenAI with API key:', process.env.OPENROUTER_API_KEY.substring(0, 10) + '...');\n    }\n    openai = new openai__WEBPACK_IMPORTED_MODULE_1__[\"default\"]({\n        apiKey: process.env.OPENROUTER_API_KEY || 'dummy-key-for-initialization',\n        baseURL: 'https://openrouter.ai/api/v1',\n        defaultHeaders: {\n            'HTTP-Referer': 'https://interview-voice-agent.vercel.app',\n            'X-Title': 'AI Interview Scheduler'\n        }\n    });\n} catch (error) {\n    console.error('Error initializing OpenAI client:', error);\n}\nasync function POST(request) {\n    // Default questions to use as fallback\n    const getDefaultQuestions = (jobTitle)=>[\n            `Tell me about your experience related to ${jobTitle || 'this role'}.`,\n            `What specific skills do you have that make you a good fit for this ${jobTitle || 'position'}?`,\n            `How do you stay updated with the latest trends and technologies in this field?`,\n            `Describe a challenging project you worked on and how you overcame obstacles.`,\n            `How do you handle tight deadlines and pressure?`,\n            `Give an example of how you've used problem-solving skills in a previous role.`,\n            `How do you collaborate with team members who have different working styles?`,\n            `What interests you most about this position?`,\n            `Where do you see yourself professionally in 5 years?`,\n            `Do you have any questions about the role or company?`\n        ];\n    try {\n        // Check if OpenAI client is initialized\n        if (!openai) {\n            console.error('OpenAI client is not initialized');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                questions: getDefaultQuestions('this role'),\n                warning: 'Using default questions because OpenAI client is not initialized'\n            });\n        }\n        // Parse request body\n        let jobTitle, jobDescription, requiredSkills, experienceLevel, questionCount;\n        try {\n            const body = await request.json();\n            jobTitle = body.jobTitle;\n            jobDescription = body.jobDescription;\n            requiredSkills = body.requiredSkills;\n            experienceLevel = body.experienceLevel;\n            questionCount = body.questionCount || 10;\n            console.log('Received request to generate questions for:', jobTitle);\n        } catch (parseError) {\n            console.error('Error parsing request body:', parseError);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                questions: getDefaultQuestions('this role'),\n                error: 'Invalid request format'\n            }, {\n                status: 400\n            });\n        }\n        // Validate required fields\n        if (!jobTitle) {\n            console.warn('Job title is missing, using default questions');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                questions: getDefaultQuestions('this role'),\n                warning: 'Job title is required for customized questions'\n            });\n        }\n        // Create the prompt for generating interview questions\n        const prompt = `\nGenerate ${questionCount} interview questions for a ${jobTitle} position.\n\nJob Description: ${jobDescription || 'Not provided'}\n\nRequired Skills: ${requiredSkills || 'Not provided'}\n\nExperience Level: ${experienceLevel || 'Mid-Level'}\n\nPlease generate a JSON array of ${questionCount} questions that would be appropriate for this position.\nThe questions should assess the candidate's skills, experience, and fit for the role.\nEach question should be clear, concise, and directly related to the job requirements.\n\nFormat the response as a valid JSON array of strings, with each string being a question.\nExample format:\n[\n  \"Question 1?\",\n  \"Question 2?\",\n  \"Question 3?\"\n]\n`;\n        try {\n            console.log('Calling OpenAI to generate questions...');\n            // Call OpenAI to generate questions\n            const response = await openai.chat.completions.create({\n                model: 'openai/gpt-4',\n                messages: [\n                    {\n                        role: 'system',\n                        content: 'You are an expert HR professional who specializes in creating interview questions for technical positions.'\n                    },\n                    {\n                        role: 'user',\n                        content: prompt\n                    }\n                ],\n                temperature: 0.7,\n                max_tokens: 2000\n            });\n            console.log('OpenAI response received');\n            // Extract the generated questions\n            if (!response.choices || response.choices.length === 0 || !response.choices[0].message) {\n                console.error('Invalid response from OpenAI:', response);\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    questions: getDefaultQuestions(jobTitle),\n                    warning: 'Invalid response from AI service'\n                });\n            }\n            const content = response.choices[0].message.content;\n            console.log('Raw content from OpenAI:', content.substring(0, 100) + '...');\n            // Parse the JSON response\n            let questions;\n            try {\n                // Try to extract JSON if it's wrapped in markdown code blocks\n                const jsonMatch = content.match(/```(?:json)?\\s*([\\s\\S]*?)\\s*```/) || content.match(/\\[([\\s\\S]*?)\\]/);\n                const jsonString = jsonMatch ? jsonMatch[0] : content;\n                questions = JSON.parse(jsonString);\n                console.log('Successfully parsed questions:', questions.length);\n            } catch (parseError) {\n                console.error('Error parsing questions JSON:', parseError);\n                // Fallback to default questions if parsing fails\n                questions = getDefaultQuestions(jobTitle);\n                console.log('Using default questions due to parsing error');\n            }\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                questions\n            });\n        } catch (aiError) {\n            console.error('Error calling OpenAI API:', aiError);\n            // Return default questions if AI call fails\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                questions: getDefaultQuestions(jobTitle),\n                warning: 'Error generating custom questions, using defaults'\n            });\n        }\n    } catch (error) {\n        console.error('Unhandled error in generate-questions API:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            questions: getDefaultQuestions('this role'),\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/generate-questions/route.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-questions%2Froute&page=%2Fapi%2Fgenerate-questions%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-questions%2Froute.js&appDir=C%3A%5CUsers%5Chp%5CDesktop%5CVoice-Agent-for-ITjobs%5CAI-Interview-Voice-Agent-for-IT-Jobs%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Chp%5CDesktop%5CVoice-Agent-for-ITjobs%5CAI-Interview-Voice-Agent-for-IT-Jobs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-questions%2Froute&page=%2Fapi%2Fgenerate-questions%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-questions%2Froute.js&appDir=C%3A%5CUsers%5Chp%5CDesktop%5CVoice-Agent-for-ITjobs%5CAI-Interview-Voice-Agent-for-IT-Jobs%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Chp%5CDesktop%5CVoice-Agent-for-ITjobs%5CAI-Interview-Voice-Agent-for-IT-Jobs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_hp_Desktop_Voice_Agent_for_ITjobs_AI_Interview_Voice_Agent_for_IT_Jobs_app_api_generate_questions_route_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/generate-questions/route.js */ \"(rsc)/./app/api/generate-questions/route.js\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/generate-questions/route\",\n        pathname: \"/api/generate-questions\",\n        filename: \"route\",\n        bundlePath: \"app/api/generate-questions/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\api\\\\generate-questions\\\\route.js\",\n    nextConfigOutput,\n    userland: C_Users_hp_Desktop_Voice_Agent_for_ITjobs_AI_Interview_Voice_Agent_for_IT_Jobs_app_api_generate_questions_route_js__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-questions%2Froute&page=%2Fapi%2Fgenerate-questions%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-questions%2Froute.js&appDir=C%3A%5CUsers%5Chp%5CDesktop%5CVoice-Agent-for-ITjobs%5CAI-Interview-Voice-Agent-for-IT-Jobs%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Chp%5CDesktop%5CVoice-Agent-for-ITjobs%5CAI-Interview-Voice-Agent-for-IT-Jobs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "node:fs":
/*!**************************!*\
  !*** external "node:fs" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:fs");

/***/ }),

/***/ "node:stream":
/*!******************************!*\
  !*** external "node:stream" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:stream");

/***/ }),

/***/ "node:stream/web":
/*!**********************************!*\
  !*** external "node:stream/web" ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:stream/web");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "worker_threads":
/*!*********************************!*\
  !*** external "worker_threads" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("worker_threads");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/tr46","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions","vendor-chunks/formdata-node","vendor-chunks/openai","vendor-chunks/form-data-encoder","vendor-chunks/agentkeepalive","vendor-chunks/web-streams-polyfill","vendor-chunks/node-fetch","vendor-chunks/ms","vendor-chunks/humanize-ms","vendor-chunks/event-target-shim","vendor-chunks/abort-controller"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-questions%2Froute&page=%2Fapi%2Fgenerate-questions%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-questions%2Froute.js&appDir=C%3A%5CUsers%5Chp%5CDesktop%5CVoice-Agent-for-ITjobs%5CAI-Interview-Voice-Agent-for-IT-Jobs%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Chp%5CDesktop%5CVoice-Agent-for-ITjobs%5CAI-Interview-Voice-Agent-for-IT-Jobs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();