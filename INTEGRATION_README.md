# Voice Agent Integration for IT Jobs Platform

This document describes the integration between the main job platform (`AI-Interview-Voice-Agent-for-IT-Jobs`) and the voice interview agent (`kamikaz-interview-ai`).

## 🏗️ Architecture Overview

The integration creates a seamless flow where:

1. **Job Platform** - Companies post jobs, candidates apply
2. **Voice Agent** - Conducts AI-powered voice interviews
3. **Feedback Loop** - Results flow back to companies for hiring decisions

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Job Platform  │───▶│  Voice Agent    │───▶│   Feedback      │
│                 │    │                 │    │                 │
│ • Job Postings  │    │ • Voice AI      │    │ • Scores        │
│ • Applications  │    │ • Questions     │    │ • Analysis      │
│ • Company View  │    │ • Conversation  │    │ • Hiring Rec.   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🚀 Quick Setup

### 1. Database Setup

Run the integration schema:
```sql
-- In your Supabase SQL editor
\i integration_schema.sql
\i setup_integration.sql
```

### 2. Environment Variables

**Main Platform (.env.local):**
```env
# Integration
INTEGRATION_API_KEY=main-platform-api-key
VOICE_AGENT_URL=http://localhost:3001
INTEGRATION_SHARED_SECRET=your-secure-shared-secret

# Existing variables
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
OPENROUTER_API_KEY=your_openrouter_key
```

**Voice Agent (.env.local):**
```env
# Integration
INTEGRATION_API_KEY=voice-agent-api-key
MAIN_PLATFORM_URL=http://localhost:3000
NEXT_PUBLIC_INTEGRATION_API_KEY=voice-agent-api-key

# Existing variables
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
GOOGLE_GEMINI_API_KEY=your_gemini_api_key
```

### 3. Start Both Systems

```bash
# Terminal 1 - Main Platform
cd AI-Interview-Voice-Agent-for-IT-Jobs
npm run dev

# Terminal 2 - Voice Agent
cd kamikaz-interview-ai\ \(1\)
npm run dev
```

### 4. Test Integration

```bash
# Run integration tests
node test_integration.js
```

## 📋 Integration Flow

### Step 1: Job Application
1. User applies for job on main platform
2. System generates interview questions
3. Creates interview session in database
4. Redirects user to voice agent

### Step 2: Voice Interview
1. Voice agent receives session data
2. Conducts AI-powered interview
3. Records conversation and responses
4. Generates performance scores

### Step 3: Feedback Processing
1. Voice agent sends results to main platform
2. Main platform processes and stores feedback
3. Company receives hiring recommendation
4. User can view their interview results

## 🔧 API Endpoints

### Main Platform APIs

| Endpoint | Method | Purpose |
|----------|--------|---------|
| `/api/integration/health` | GET | Health check |
| `/api/integration/interview/create` | POST | Create interview session |
| `/api/integration/interview/feedback` | POST | Receive feedback |

### Voice Agent APIs

| Endpoint | Method | Purpose |
|----------|--------|---------|
| `/api/integration/health` | GET | Health check |
| `/api/integration/interview/start` | POST | Start interview |
| `/api/integration/interview/feedback` | POST | Submit feedback |

## 📊 Database Schema

### Key Tables

- **Interview_Sessions** - Links both systems
- **Interview_Feedback_Bridge** - Stores feedback data
- **Integration_Logs** - Debugging and monitoring
- **Integration_API_Keys** - Authentication

## 🔍 Debugging

### Health Checks
```bash
# Check main platform
curl http://localhost:3000/api/integration/health

# Check voice agent
curl http://localhost:3001/api/integration/health
```

### View Logs
```sql
-- Recent integration logs
SELECT * FROM "Integration_Logs" 
ORDER BY created_at DESC 
LIMIT 20;

-- Session status
SELECT * FROM "Interview_Sessions" 
WHERE created_at > NOW() - INTERVAL '1 day';
```

### Common Issues

1. **API Key Errors**
   - Check environment variables
   - Verify API keys in database
   - Ensure proper headers

2. **Connection Timeouts**
   - Check if both services are running
   - Verify URLs in environment variables
   - Check firewall/network settings

3. **Database Errors**
   - Run setup scripts
   - Check Supabase connection
   - Verify table permissions

## 🔐 Security

### API Authentication
- Uses hashed API keys
- Rate limiting (1000 requests/hour)
- Request validation and sanitization

### Data Protection
- Row Level Security (RLS) enabled
- Encrypted sensitive data
- Audit logging for all operations

## 📈 Monitoring

### Key Metrics
- Interview completion rate
- Average interview duration
- Feedback processing time
- System health status

### Alerts
- Failed interview sessions
- API authentication failures
- Database connection issues
- High error rates

## 🧪 Testing

### Manual Testing
1. Apply for a job on main platform
2. Complete voice interview
3. Check feedback in company dashboard
4. Verify data in database

### Automated Testing
```bash
# Run full integration test suite
npm run test:integration

# Test specific components
npm run test:health
npm run test:session-creation
npm run test:feedback-flow
```

## 🚀 Deployment

### Production Checklist
- [ ] Update environment variables
- [ ] Configure proper API keys
- [ ] Set up SSL certificates
- [ ] Configure rate limiting
- [ ] Set up monitoring
- [ ] Test backup/recovery

### Environment URLs
```env
# Production
MAIN_PLATFORM_URL=https://your-main-platform.com
VOICE_AGENT_URL=https://your-voice-agent.com

# Staging
MAIN_PLATFORM_URL=https://staging-main-platform.com
VOICE_AGENT_URL=https://staging-voice-agent.com
```

## 📞 Support

### Troubleshooting Steps
1. Check health endpoints
2. Review integration logs
3. Verify environment variables
4. Test API connectivity
5. Check database permissions

### Contact
- Technical Issues: Check integration logs
- Feature Requests: Create GitHub issue
- Security Concerns: Contact admin

## 🔄 Updates

### Version 1.0 Features
- ✅ Basic integration flow
- ✅ Voice interview sessions
- ✅ Feedback processing
- ✅ Health monitoring
- ✅ Error handling

### Planned Features
- 🔄 Real-time status updates
- 🔄 Advanced analytics
- 🔄 Multi-language support
- 🔄 Video interview option
- 🔄 Custom question templates

## 📝 Contributing

### Development Setup
1. Fork both repositories
2. Set up local environment
3. Run integration tests
4. Make changes
5. Test thoroughly
6. Submit pull request

### Code Standards
- Use TypeScript for new features
- Add comprehensive error handling
- Include integration tests
- Update documentation
- Follow existing patterns
