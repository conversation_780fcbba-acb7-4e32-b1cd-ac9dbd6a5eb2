"use client"

import { useState, useEffect, useRef } from "react"
import { useSearchPara<PERSON>, useRouter } from "next/navigation"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Mic, MicOff, Volume2, VolumeX, AlertCircle, CheckCircle, Clock, User, Building } from "lucide-react"
import { GeminiVoiceService } from "@/lib/gemini-voice"
import { supabase } from "@/lib/supabase"
import {
  INTEGRATION_CONFIG,
  logIntegrationEvent
} from '../../../../shared/integration-config.js'

interface ExternalSessionData {
  sessionId: string;
  mainPlatformSession: boolean;
  userEmail: string;
  userName: string;
  jobTitle: string;
  jobDescription?: string;
  companyName?: string;
  interviewType: 'job_application' | 'practice';
  duration: number;
  experienceLevel: string;
  requiredSkills?: string;
  companyCriteria?: string;
  questions: Array<{
    id: string;
    question: string;
    type: string;
    difficulty: string;
    expectedDuration: number;
    followUpAllowed: boolean;
    metadata: any;
  }>;
  questionCount: number;
  callbackUrl: string;
  webhookSecret: string;
  voiceAgentUserId: string;
  voiceAgentInterviewId: string;
  createdAt: string;
}

export default function ExternalInterviewPage() {
  const searchParams = useSearchParams()
  const router = useRouter()
  const sessionId = searchParams.get('sessionId')
  const encodedData = searchParams.get('data')

  // Session and interview state
  const [sessionData, setSessionData] = useState<ExternalSessionData | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Interview state
  const [isInterviewStarted, setIsInterviewStarted] = useState(false)
  const [isInterviewCompleted, setIsInterviewCompleted] = useState(false)
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0)
  const [currentQuestion, setCurrentQuestion] = useState<string>("")
  const [isListening, setIsListening] = useState(false)
  const [isSpeaking, setIsSpeaking] = useState(false)
  const [transcript, setTranscript] = useState("")
  const [conversationHistory, setConversationHistory] = useState<Array<{
    speaker: 'ai' | 'user';
    message: string;
    timestamp: Date;
    questionId?: string;
  }>>([])

  // Voice service
  const voiceServiceRef = useRef<GeminiVoiceService | null>(null)
  const [microphonePermission, setMicrophonePermission] = useState<'granted' | 'denied' | 'prompt'>('prompt')

  // Interview metrics
  const [startTime, setStartTime] = useState<Date | null>(null)
  const [elapsedTime, setElapsedTime] = useState(0)

  // Load session data on component mount
  useEffect(() => {
    if (!sessionId && !encodedData) {
      setError('No session ID or data provided')
      setIsLoading(false)
      return
    }

    if (encodedData) {
      // Handle direct data passing from main platform
      loadDirectSessionData()
    } else if (sessionId) {
      // Handle traditional sessionId approach
      loadSessionData()
    }
  }, [sessionId, encodedData])

  // Timer for elapsed time
  useEffect(() => {
    let interval: NodeJS.Timeout | null = null

    if (isInterviewStarted && startTime && !isInterviewCompleted) {
      interval = setInterval(() => {
        setElapsedTime(Math.floor((Date.now() - startTime.getTime()) / 1000))
      }, 1000)
    }

    return () => {
      if (interval) clearInterval(interval)
    }
  }, [isInterviewStarted, startTime, isInterviewCompleted])

  const loadDirectSessionData = async () => {
    try {
      setIsLoading(true)

      if (!encodedData) {
        throw new Error('No encoded data provided')
      }

      logIntegrationEvent('info', 'voice_agent', 'loading_direct_session_data',
        'Loading interview session from encoded data', 'direct_data')

      // Decode the data passed from main platform
      // First decode from base64, then decode URI component, then parse JSON
      const decodedString = decodeURIComponent(atob(encodedData))
      const interviewData = JSON.parse(decodedString)

      console.log('📥 Received interview data:', interviewData)

      // Transform the data to match our ExternalSessionData interface
      const transformedSessionData: ExternalSessionData = {
        sessionId: interviewData.sessionId,
        mainPlatformSession: true,
        userEmail: interviewData.userEmail,
        userName: interviewData.userName,
        jobTitle: interviewData.jobTitle,
        jobDescription: interviewData.jobDescription,
        companyName: interviewData.companyName,
        interviewType: interviewData.interviewType as 'job_application' | 'practice',
        duration: interviewData.duration,
        experienceLevel: interviewData.experienceLevel,
        requiredSkills: interviewData.requiredSkills,
        companyCriteria: interviewData.companyCriteria,
        questions: interviewData.questions || [],
        questionCount: interviewData.questionCount,
        callbackUrl: `${INTEGRATION_CONFIG.MAIN_PLATFORM_URL}/api/integration/feedback`,
        webhookSecret: 'webhook-secret',
        voiceAgentUserId: 'external_user',
        voiceAgentInterviewId: interviewData.sessionId,
        createdAt: new Date().toISOString()
      }

      setSessionData(transformedSessionData)
      setIsLoading(false)

      logIntegrationEvent('info', 'voice_agent', 'session_data_loaded',
        'Successfully loaded session data from encoded data', transformedSessionData.sessionId)

    } catch (error) {
      console.error('❌ Failed to load direct session data:', error)
      setError(`Failed to load interview data: ${error.message}`)
      setIsLoading(false)

      logIntegrationEvent('error', 'voice_agent', 'session_loading_failed',
        'Failed to load session data from encoded data', 'direct_data', { error: error.message })
    }
  }

  const loadSessionData = async () => {
    try {
      setIsLoading(true)

      logIntegrationEvent('info', 'voice_agent', 'loading_external_session',
        'Loading external interview session', sessionId)

      // In a real implementation, you would fetch this from your backend
      // For now, we'll check if there's session data in localStorage or make an API call

      // Try to get session data from the main platform
      const response = await fetch(`${INTEGRATION_CONFIG.MAIN_PLATFORM_URL}/api/integration/interview/create?sessionId=${sessionId}`, {
        headers: {
          [INTEGRATION_CONFIG.API_KEY_HEADER]: process.env.NEXT_PUBLIC_INTEGRATION_API_KEY || 'voice-agent-api-key'
        }
      })

      if (!response.ok) {
        throw new Error(`Failed to load session: ${response.status}`)
      }

      const result = await response.json()
      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to load session data')
      }

      // For demo purposes, create mock session data
      // In production, this would come from the API response
      const mockSessionData: ExternalSessionData = {
        sessionId: sessionId,
        mainPlatformSession: true,
        userEmail: '<EMAIL>',
        userName: 'Interview Candidate',
        jobTitle: 'Software Engineer',
        jobDescription: 'Full-stack development role',
        companyName: 'Tech Company',
        interviewType: 'job_application',
        duration: 30,
        experienceLevel: 'Mid-Level',
        requiredSkills: 'JavaScript, React, Node.js',
        companyCriteria: 'Strong problem-solving skills',
        questions: result.data?.questions || [
          {
            id: 'q1',
            question: 'Tell me about yourself and your experience with software development.',
            type: 'behavioral',
            difficulty: 'easy',
            expectedDuration: 120,
            followUpAllowed: true,
            metadata: {}
          },
          {
            id: 'q2',
            question: 'Describe a challenging technical problem you solved recently.',
            type: 'technical',
            difficulty: 'medium',
            expectedDuration: 180,
            followUpAllowed: true,
            metadata: {}
          }
        ],
        questionCount: 2,
        callbackUrl: `${INTEGRATION_CONFIG.MAIN_PLATFORM_URL}/api/integration/interview/feedback`,
        webhookSecret: 'webhook-secret',
        voiceAgentUserId: 'user-123',
        voiceAgentInterviewId: 'interview-456',
        createdAt: new Date().toISOString()
      }

      setSessionData(mockSessionData)

      logIntegrationEvent('info', 'voice_agent', 'external_session_loaded',
        'External interview session loaded successfully', sessionId, {
        jobTitle: mockSessionData.jobTitle,
        questionCount: mockSessionData.questions.length
      })

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load session'
      setError(errorMessage)

      logIntegrationEvent('error', 'voice_agent', 'session_load_failed',
        'Failed to load external session', sessionId, { error: errorMessage })
    } finally {
      setIsLoading(false)
    }
  }

  const checkMicrophonePermission = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true })
      stream.getTracks().forEach(track => track.stop())
      setMicrophonePermission('granted')
      return true
    } catch (error) {
      setMicrophonePermission('denied')
      return false
    }
  }

  const initializeVoiceService = async () => {
    try {
      voiceServiceRef.current = new GeminiVoiceService()
      voiceServiceRef.current.enableVoiceActivityDetection(true)
      voiceServiceRef.current.setInterruptCallback(() => {
        setIsSpeaking(false)
      })

      return true
    } catch (error) {
      console.error('Failed to initialize voice service:', error)
      return false
    }
  }

  const startInterview = async () => {
    if (!sessionData) return

    try {
      const hasPermission = await checkMicrophonePermission()
      if (!hasPermission) {
        setError('Microphone permission is required for the interview')
        return
      }

      const voiceInitialized = await initializeVoiceService()
      if (!voiceInitialized) {
        setError('Failed to initialize voice service')
        return
      }

      setIsInterviewStarted(true)
      setStartTime(new Date())

      logIntegrationEvent('info', 'voice_agent', 'external_interview_started',
        'External interview started', sessionId, {
        userEmail: sessionData.userEmail,
        jobTitle: sessionData.jobTitle
      })

      // Start with welcome message
      const welcomeMessage = `Hello ${sessionData.userName}, welcome to your interview for the ${sessionData.jobTitle} position at ${sessionData.companyName}. I'm your AI interviewer, and I'll be asking you ${sessionData.questions.length} questions today. Are you ready to begin?`

      await voiceServiceRef.current?.speak(welcomeMessage)

      setConversationHistory([{
        speaker: 'ai',
        message: welcomeMessage,
        timestamp: new Date()
      }])

      // Start listening for response
      setTimeout(() => {
        startListening()
      }, 1000)

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to start interview'
      setError(errorMessage)

      logIntegrationEvent('error', 'voice_agent', 'interview_start_failed',
        'Failed to start external interview', sessionId, { error: errorMessage })
    }
  }

  const startListening = async () => {
    if (!voiceServiceRef.current || isListening) return

    try {
      setIsListening(true)
      const response = await voiceServiceRef.current.startListening()
      setTranscript(response)
      setIsListening(false)

      if (response.trim()) {
        await processUserResponse(response)
      }
    } catch (error) {
      console.error('Error listening:', error)
      setIsListening(false)
    }
  }

  const processUserResponse = async (response: string) => {
    if (!sessionData || !voiceServiceRef.current) return

    try {
      setIsSpeaking(true)

      // Add user response to conversation
      const userMessage = {
        speaker: 'user' as const,
        message: response,
        timestamp: new Date(),
        questionId: sessionData.questions[currentQuestionIndex]?.id
      }

      setConversationHistory(prev => [...prev, userMessage])

      // Check if we should ask the next question or end the interview
      if (currentQuestionIndex < sessionData.questions.length - 1) {
        // Ask next question
        const nextQuestionIndex = currentQuestionIndex + 1
        const nextQuestion = sessionData.questions[nextQuestionIndex]

        setCurrentQuestionIndex(nextQuestionIndex)
        setCurrentQuestion(nextQuestion.question)

        await voiceServiceRef.current.speak(nextQuestion.question)

        const aiMessage = {
          speaker: 'ai' as const,
          message: nextQuestion.question,
          timestamp: new Date(),
          questionId: nextQuestion.id
        }

        setConversationHistory(prev => [...prev, aiMessage])

        setIsSpeaking(false)

        // Continue listening
        setTimeout(() => {
          startListening()
        }, 1000)

      } else {
        // End interview
        await endInterview()
      }

    } catch (error) {
      console.error('Error processing response:', error)
      setIsSpeaking(false)
    }
  }

  const endInterview = async () => {
    if (!sessionData) return

    try {
      setIsInterviewCompleted(true)

      const endMessage = "Thank you for completing the interview. Your responses have been recorded and will be reviewed. You can now close this window."

      if (voiceServiceRef.current) {
        await voiceServiceRef.current.speak(endMessage)
      }

      setConversationHistory(prev => [...prev, {
        speaker: 'ai',
        message: endMessage,
        timestamp: new Date()
      }])

      // Submit feedback to main platform
      await submitFeedback()

      logIntegrationEvent('info', 'voice_agent', 'external_interview_completed',
        'External interview completed', sessionId, {
        duration: elapsedTime,
        questionsAnswered: currentQuestionIndex + 1
      })

    } catch (error) {
      console.error('Error ending interview:', error)

      logIntegrationEvent('error', 'voice_agent', 'interview_end_failed',
        'Failed to end external interview', sessionId, { error: error.message })
    }
  }

  const submitFeedback = async () => {
    if (!sessionData) return

    try {
      // Calculate basic scores (in a real implementation, this would be more sophisticated)
      const overallScore = Math.floor(Math.random() * 40) + 60 // 60-100 range
      const questionsAnswered = currentQuestionIndex + 1

      const feedbackData = {
        sessionId: sessionData.sessionId,
        voiceAgentInterviewId: sessionData.voiceAgentInterviewId,
        userEmail: sessionData.userEmail,
        userName: sessionData.userName,
        overallScore,
        technicalScore: overallScore + Math.floor(Math.random() * 10) - 5,
        communicationScore: overallScore + Math.floor(Math.random() * 10) - 5,
        problemSolvingScore: overallScore + Math.floor(Math.random() * 10) - 5,
        recommendation: overallScore >= 80 ? 'hire' : overallScore >= 65 ? 'maybe' : 'reject',
        strengths: ['Good communication', 'Relevant experience'],
        areasForImprovement: ['Could provide more specific examples'],
        overallAssessment: 'Candidate showed good understanding of the role requirements.',
        duration: Math.floor(elapsedTime / 60), // Convert to minutes
        questionsAsked: sessionData.questions.length,
        questionsAnswered,
        conversationTranscript: conversationHistory,
        completedAt: new Date().toISOString(),
        interviewQuality: 'good',
        callbackUrl: sessionData.callbackUrl,
        webhookSecret: sessionData.webhookSecret
      }

      // Submit to voice agent's feedback endpoint which will forward to main platform
      const response = await fetch('/api/integration/feedback', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          [INTEGRATION_CONFIG.API_KEY_HEADER]: process.env.NEXT_PUBLIC_INTEGRATION_API_KEY || 'voice-agent-api-key'
        },
        body: JSON.stringify(feedbackData)
      })

      if (!response.ok) {
        throw new Error(`Failed to submit feedback: ${response.status}`)
      }

      logIntegrationEvent('info', 'voice_agent', 'feedback_submitted',
        'Feedback submitted successfully', sessionId, {
        overallScore,
        recommendation: feedbackData.recommendation
      })

    } catch (error) {
      logIntegrationEvent('error', 'voice_agent', 'feedback_submission_failed',
        'Failed to submit feedback', sessionId, { error: error.message })
    }
  }

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardContent className="p-6 text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p>Loading interview session...</p>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-red-50 to-pink-100 flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardContent className="p-6 text-center">
            <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h2 className="text-xl font-semibold text-red-700 mb-2">Error</h2>
            <p className="text-red-600 mb-4">{error}</p>
            <Button onClick={() => router.push('/')} variant="outline">
              Go Back
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (!sessionData) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardContent className="p-6 text-center">
            <AlertCircle className="h-12 w-12 text-gray-500 mx-auto mb-4" />
            <p>No session data available</p>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-4">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <Card className="mb-6">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center gap-2">
                  <Building className="h-5 w-5" />
                  {sessionData.jobTitle} Interview
                </CardTitle>
                <p className="text-muted-foreground">
                  {sessionData.companyName} • {sessionData.experienceLevel} Level
                </p>
              </div>
              <div className="text-right">
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <User className="h-4 w-4" />
                  {sessionData.userName}
                </div>
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <Clock className="h-4 w-4" />
                  {isInterviewStarted ? formatTime(elapsedTime) : `${sessionData.duration} min`}
                </div>
              </div>
            </div>
          </CardHeader>
        </Card>

        {/* Interview Status */}
        <Card className="mb-6">
          <CardContent className="p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-2">
                <Badge variant={isInterviewCompleted ? "default" : isInterviewStarted ? "secondary" : "outline"}>
                  {isInterviewCompleted ? "Completed" : isInterviewStarted ? "In Progress" : "Ready to Start"}
                </Badge>
                {isInterviewStarted && (
                  <span className="text-sm text-muted-foreground">
                    Question {currentQuestionIndex + 1} of {sessionData.questions.length}
                  </span>
                )}
              </div>

              {isInterviewStarted && (
                <div className="flex items-center gap-2">
                  <Button
                    variant={isListening ? "default" : "outline"}
                    size="sm"
                    onClick={startListening}
                    disabled={isSpeaking || isInterviewCompleted}
                  >
                    {isListening ? <Mic className="h-4 w-4" /> : <MicOff className="h-4 w-4" />}
                    {isListening ? "Listening..." : "Start Speaking"}
                  </Button>

                  {isSpeaking && (
                    <Badge variant="secondary">
                      <Volume2 className="h-3 w-3 mr-1" />
                      AI Speaking
                    </Badge>
                  )}
                </div>
              )}
            </div>

            {!isInterviewStarted && (
              <div className="text-center">
                <p className="mb-4">Ready to start your interview? Make sure you have a quiet environment and working microphone.</p>
                <Button onClick={startInterview} size="lg">
                  Start Interview
                </Button>
              </div>
            )}

            {isInterviewCompleted && (
              <div className="text-center">
                <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">Interview Completed!</h3>
                <p className="text-muted-foreground">
                  Thank you for your time. Your responses have been submitted for review.
                </p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Conversation History */}
        {conversationHistory.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle>Interview Conversation</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4 max-h-96 overflow-y-auto">
                {conversationHistory.map((entry, index) => (
                  <div
                    key={index}
                    className={`flex ${entry.speaker === 'user' ? 'justify-end' : 'justify-start'}`}
                  >
                    <div
                      className={`max-w-[80%] p-3 rounded-lg ${entry.speaker === 'user'
                        ? 'bg-blue-500 text-white'
                        : 'bg-gray-100 text-gray-900'
                        }`}
                    >
                      <p className="text-sm">{entry.message}</p>
                      <p className="text-xs opacity-70 mt-1">
                        {entry.timestamp.toLocaleTimeString()}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
}
