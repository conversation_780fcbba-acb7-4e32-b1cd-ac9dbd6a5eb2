-- =====================================================
-- SIMPLE INTERVIEW SESSIONS TABLE SETUP
-- This version doesn't depend on other tables existing
-- =====================================================

-- Create the Interview_Sessions table if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public'
        AND table_name = 'Interview_Sessions'
    ) THEN
        CREATE TABLE public.Interview_Sessions (
            id SERIAL PRIMARY KEY,
            main_interview_id VARCHAR(255) UNIQUE NOT NULL,
            job_id INTEGER,
            company_id INTEGER,
            user_email VARCHAR(255) NOT NULL,
            user_name VARCHAR(255),
            job_title VARCHAR(255),
            company_name VARCHAR(255),
            interview_type VARCHAR(50) DEFAULT 'job_application',
            status VARCHAR(50) DEFAULT 'pending',
            session_data JSONB,
            voice_agent_session_id VARCHAR(255),
            feedback_data JSONB,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            completed_at TIMESTAMP WITH TIME ZONE
        );
        
        RAISE NOTICE 'Created Interview_Sessions table';
    ELSE
        RAISE NOTICE 'Interview_Sessions table already exists';
    END IF;
END $$;

-- Create indexes for better performance
DO $$
BEGIN
    -- Drop existing indexes if they exist
    DROP INDEX IF EXISTS idx_interview_sessions_main_id;
    DROP INDEX IF EXISTS idx_interview_sessions_user_email;
    DROP INDEX IF EXISTS idx_interview_sessions_job_id;
    DROP INDEX IF EXISTS idx_interview_sessions_company_id;
    DROP INDEX IF EXISTS idx_interview_sessions_status;
    
    -- Create indexes
    CREATE INDEX idx_interview_sessions_main_id ON public.Interview_Sessions(main_interview_id);
    CREATE INDEX idx_interview_sessions_user_email ON public.Interview_Sessions(user_email);
    CREATE INDEX idx_interview_sessions_job_id ON public.Interview_Sessions(job_id);
    CREATE INDEX idx_interview_sessions_company_id ON public.Interview_Sessions(company_id);
    CREATE INDEX idx_interview_sessions_status ON public.Interview_Sessions(status);
    
    RAISE NOTICE 'Created indexes for Interview_Sessions table';
END $$;

-- Enable RLS (Row Level Security)
ALTER TABLE public.Interview_Sessions ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Users can view their own interview sessions" ON public.Interview_Sessions;
DROP POLICY IF EXISTS "Companies can view sessions for their jobs" ON public.Interview_Sessions;
DROP POLICY IF EXISTS "Service can insert interview sessions" ON public.Interview_Sessions;
DROP POLICY IF EXISTS "Service can update interview sessions" ON public.Interview_Sessions;
DROP POLICY IF EXISTS "Allow all operations for authenticated users" ON public.Interview_Sessions;

-- Create simple, permissive policies
-- Allow authenticated users to see all interview sessions (for now)
CREATE POLICY "Allow all operations for authenticated users" ON public.Interview_Sessions
    FOR ALL TO authenticated
    USING (true)
    WITH CHECK (true);

-- Allow anon users to insert and update (for the integration)
CREATE POLICY "Allow anon operations" ON public.Interview_Sessions
    FOR ALL TO anon
    USING (true)
    WITH CHECK (true);

-- Grant necessary permissions
DO $$
BEGIN
    -- Grant table permissions
    GRANT SELECT, INSERT, UPDATE ON public.Interview_Sessions TO anon;
    GRANT SELECT, INSERT, UPDATE ON public.Interview_Sessions TO authenticated;
    
    -- Grant sequence permissions
    GRANT USAGE, SELECT ON SEQUENCE Interview_Sessions_id_seq TO anon;
    GRANT USAGE, SELECT ON SEQUENCE Interview_Sessions_id_seq TO authenticated;
    
    RAISE NOTICE 'Granted permissions on Interview_Sessions table and sequence';
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE 'Could not grant some permissions: %', SQLERRM;
END $$;

-- Final verification
DO $$
BEGIN
    RAISE NOTICE 'Simple Interview_Sessions table setup completed successfully!';
    RAISE NOTICE 'This version uses permissive policies that work without other tables';
    RAISE NOTICE 'You can tighten security later once all tables are properly set up';
END $$;
