"use client"

import { useTheme } from "next-themes"
import { Toaster as Son<PERSON> } from "sonner";
import { useIsClient } from "@/hooks/useHydrationFix";

const Toaster = ({
  ...props
}) => {
  const { theme = "system" } = useTheme()
  const isClient = useIsClient();

  // Don't render on server to prevent hydration mismatch
  if (!isClient) {
    return null;
  }

  return (
    (<Sonner
      theme={theme}
      className="toaster group"
      style={
        {
          "--normal-bg": "var(--popover)",
          "--normal-text": "var(--popover-foreground)",
          "--normal-border": "var(--border)"
        }
      }
      {...props} />)
  );
}

export { Toaster }
