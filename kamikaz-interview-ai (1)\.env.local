# Supabase Configuration (Required)
NEXT_PUBLIC_SUPABASE_URL=https://bdoyiznptgiumezxnank.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJkb3lpem5wdGdpdW1lenhuYW5rIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE2OTM4MTIsImV4cCI6MjA2NzI2OTgxMn0.qMzIp7wpO_Dcuq6rJMPJ5wRx1hiVmpS3V4UPFOvm-80

# Google Gemini AI (Required for AI features)
GOOGLE_GEMINI_API_KEY=AIzaSyAgK32ThjpPUNT_-RM5XiXFKPtgAT1vpZI

# Database Configuration (Optional - for direct database access)
POSTGRES_URL=postgres://postgres.bdoyiznptgiumezxnank:<EMAIL>:6543/postgres?sslmode=require&supa=base-pooler.x
POSTGRES_PRISMA_URL=postgres://postgres.bdoyiznptgiumezxnank:<EMAIL>:6543/postgres?sslmode=require&pgbouncer=true
POSTGRES_URL_NON_POOLING=postgres://postgres.bdoyiznptgiumezxnank:<EMAIL>:5432/postgres?sslmode=require

# Supabase Service Keys (Optional - for admin operations)
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJkb3lpem5wdGdpdW1lenhuYW5rIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTY5MzgxMiwiZXhwIjoyMDY3MjY5ODEyfQ.muuR7qBB9PhEUePBVRiSANaBaHS8WF5WlZ0x88otufU
SUPABASE_JWT_SECRET=qBTBp6Zy71llSzY7staC8kqBD+7HlxnQKxWNHfnU8ugUCLLiINoMTpU44Fm5nVfRVDrFsn2R/Cb99eksFxmsXg==

# AI API Keys (Optional - for enhanced AI features)
XAI_API_KEY=************************************************************************************
GROQ_API_KEY=********************************************************

# Database Credentials (Optional)
POSTGRES_USER=postgres
POSTGRES_PASSWORD=bLd5qFit7Y7P9jkQ
POSTGRES_DATABASE=postgres
POSTGRES_HOST=db.bdoyiznptgiumezxnank.supabase.co

# Production Environment
NODE_ENV=production
