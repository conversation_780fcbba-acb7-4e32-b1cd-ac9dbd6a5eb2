/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/ai-model/route";
exports.ids = ["app/api/ai-model/route"];
exports.modules = {

/***/ "(rsc)/./app/api/ai-model/route.jsx":
/*!************************************!*\
  !*** ./app/api/ai-model/route.jsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var _services_Constants__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/services/Constants */ \"(rsc)/./services/Constants.jsx\");\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var openai__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! openai */ \"(rsc)/./node_modules/openai/index.mjs\");\n\n\n\nasync function POST(req) {\n    try {\n        const { jobPosition, jobDescription, duration, type, experienceLevel = '', requiredSkills = '', companyCriteria = '', questionCount = '10' } = await req.json();\n        // Replace all placeholders in the prompt\n        const FINAL_PROMPT = _services_Constants__WEBPACK_IMPORTED_MODULE_0__.QUESTIONS_PROMPT.replace('{{jobTitle}}', jobPosition).replace('{{jobDescription}}', jobDescription).replace('{{duration}}', duration).replace('{{type}}', type).replace('{{experienceLevel}}', experienceLevel).replace('{{requiredSkills}}', requiredSkills).replace('{{companyCriteria}}', companyCriteria).replace('{{questionCount}}', questionCount);\n        console.log(\"Sending prompt to AI model for question generation...\");\n        const openai = new openai__WEBPACK_IMPORTED_MODULE_2__[\"default\"]({\n            baseURL: \"https://openrouter.ai/api/v1\",\n            apiKey: process.env.OPENROUTER_API_KEY\n        });\n        const completion = await openai.chat.completions.create({\n            model: \"google/gemini-flash-1.5\",\n            messages: [\n                {\n                    role: \"user\",\n                    content: FINAL_PROMPT\n                }\n            ]\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json(completion.choices[0].message);\n    } catch (error) {\n        console.error(\"Error in AI question generation:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n            error: \"Failed to generate questions\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/ai-model/route.jsx\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fai-model%2Froute&page=%2Fapi%2Fai-model%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fai-model%2Froute.jsx&appDir=C%3A%5CUsers%5Chp%5CDesktop%5CVoice-Agent-for-ITjobs%5CAI-Interview-Voice-Agent-for-IT-Jobs%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Chp%5CDesktop%5CVoice-Agent-for-ITjobs%5CAI-Interview-Voice-Agent-for-IT-Jobs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fai-model%2Froute&page=%2Fapi%2Fai-model%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fai-model%2Froute.jsx&appDir=C%3A%5CUsers%5Chp%5CDesktop%5CVoice-Agent-for-ITjobs%5CAI-Interview-Voice-Agent-for-IT-Jobs%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Chp%5CDesktop%5CVoice-Agent-for-ITjobs%5CAI-Interview-Voice-Agent-for-IT-Jobs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_hp_Desktop_Voice_Agent_for_ITjobs_AI_Interview_Voice_Agent_for_IT_Jobs_app_api_ai_model_route_jsx__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/ai-model/route.jsx */ \"(rsc)/./app/api/ai-model/route.jsx\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/ai-model/route\",\n        pathname: \"/api/ai-model\",\n        filename: \"route\",\n        bundlePath: \"app/api/ai-model/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\api\\\\ai-model\\\\route.jsx\",\n    nextConfigOutput,\n    userland: C_Users_hp_Desktop_Voice_Agent_for_ITjobs_AI_Interview_Voice_Agent_for_IT_Jobs_app_api_ai_model_route_jsx__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fai-model%2Froute&page=%2Fapi%2Fai-model%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fai-model%2Froute.jsx&appDir=C%3A%5CUsers%5Chp%5CDesktop%5CVoice-Agent-for-ITjobs%5CAI-Interview-Voice-Agent-for-IT-Jobs%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Chp%5CDesktop%5CVoice-Agent-for-ITjobs%5CAI-Interview-Voice-Agent-for-IT-Jobs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./services/Constants.jsx":
/*!********************************!*\
  !*** ./services/Constants.jsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CompanySideBarOptions: () => (/* binding */ CompanySideBarOptions),\n/* harmony export */   EmploymentTypes: () => (/* binding */ EmploymentTypes),\n/* harmony export */   ExperienceLevels: () => (/* binding */ ExperienceLevels),\n/* harmony export */   FEEDBACK_PROMPT: () => (/* binding */ FEEDBACK_PROMPT),\n/* harmony export */   InterviewType: () => (/* binding */ InterviewType),\n/* harmony export */   LocationTypes: () => (/* binding */ LocationTypes),\n/* harmony export */   QUESTIONS_PROMPT: () => (/* binding */ QUESTIONS_PROMPT),\n/* harmony export */   SideBarOptions: () => (/* binding */ SideBarOptions)\n/* harmony export */ });\n/* harmony import */ var _barrel_optimize_names_BriefcaseBusinessIcon_Building2_Calendar_ClipboardList_Code2Icon_Component_FileText_LayoutDashboard_List_Puzzle_Settings_User_User2Icon_Users_WalletCards_lucide_react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! __barrel_optimize__?names=BriefcaseBusinessIcon,Building2,Calendar,ClipboardList,Code2Icon,Component,FileText,LayoutDashboard,List,Puzzle,Settings,User,User2Icon,Users,WalletCards!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/layout-dashboard.js\");\n/* harmony import */ var _barrel_optimize_names_BriefcaseBusinessIcon_Building2_Calendar_ClipboardList_Code2Icon_Component_FileText_LayoutDashboard_List_Puzzle_Settings_User_User2Icon_Users_WalletCards_lucide_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=BriefcaseBusinessIcon,Building2,Calendar,ClipboardList,Code2Icon,Component,FileText,LayoutDashboard,List,Puzzle,Settings,User,User2Icon,Users,WalletCards!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/briefcase-business.js\");\n/* harmony import */ var _barrel_optimize_names_BriefcaseBusinessIcon_Building2_Calendar_ClipboardList_Code2Icon_Component_FileText_LayoutDashboard_List_Puzzle_Settings_User_User2Icon_Users_WalletCards_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=BriefcaseBusinessIcon,Building2,Calendar,ClipboardList,Code2Icon,Component,FileText,LayoutDashboard,List,Puzzle,Settings,User,User2Icon,Users,WalletCards!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_BriefcaseBusinessIcon_Building2_Calendar_ClipboardList_Code2Icon_Component_FileText_LayoutDashboard_List_Puzzle_Settings_User_User2Icon_Users_WalletCards_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=BriefcaseBusinessIcon,Building2,Calendar,ClipboardList,Code2Icon,Component,FileText,LayoutDashboard,List,Puzzle,Settings,User,User2Icon,Users,WalletCards!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_BriefcaseBusinessIcon_Building2_Calendar_ClipboardList_Code2Icon_Component_FileText_LayoutDashboard_List_Puzzle_Settings_User_User2Icon_Users_WalletCards_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BriefcaseBusinessIcon,Building2,Calendar,ClipboardList,Code2Icon,Component,FileText,LayoutDashboard,List,Puzzle,Settings,User,User2Icon,Users,WalletCards!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/wallet-cards.js\");\n/* harmony import */ var _barrel_optimize_names_BriefcaseBusinessIcon_Building2_Calendar_ClipboardList_Code2Icon_Component_FileText_LayoutDashboard_List_Puzzle_Settings_User_User2Icon_Users_WalletCards_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BriefcaseBusinessIcon,Building2,Calendar,ClipboardList,Code2Icon,Component,FileText,LayoutDashboard,List,Puzzle,Settings,User,User2Icon,Users,WalletCards!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_BriefcaseBusinessIcon_Building2_Calendar_ClipboardList_Code2Icon_Component_FileText_LayoutDashboard_List_Puzzle_Settings_User_User2Icon_Users_WalletCards_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BriefcaseBusinessIcon,Building2,Calendar,ClipboardList,Code2Icon,Component,FileText,LayoutDashboard,List,Puzzle,Settings,User,User2Icon,Users,WalletCards!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/clipboard-list.js\");\n/* harmony import */ var _barrel_optimize_names_BriefcaseBusinessIcon_Building2_Calendar_ClipboardList_Code2Icon_Component_FileText_LayoutDashboard_List_Puzzle_Settings_User_User2Icon_Users_WalletCards_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BriefcaseBusinessIcon,Building2,Calendar,ClipboardList,Code2Icon,Component,FileText,LayoutDashboard,List,Puzzle,Settings,User,User2Icon,Users,WalletCards!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_BriefcaseBusinessIcon_Building2_Calendar_ClipboardList_Code2Icon_Component_FileText_LayoutDashboard_List_Puzzle_Settings_User_User2Icon_Users_WalletCards_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BriefcaseBusinessIcon,Building2,Calendar,ClipboardList,Code2Icon,Component,FileText,LayoutDashboard,List,Puzzle,Settings,User,User2Icon,Users,WalletCards!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/building-2.js\");\n/* harmony import */ var _barrel_optimize_names_BriefcaseBusinessIcon_Building2_Calendar_ClipboardList_Code2Icon_Component_FileText_LayoutDashboard_List_Puzzle_Settings_User_User2Icon_Users_WalletCards_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BriefcaseBusinessIcon,Building2,Calendar,ClipboardList,Code2Icon,Component,FileText,LayoutDashboard,List,Puzzle,Settings,User,User2Icon,Users,WalletCards!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/code-xml.js\");\n/* harmony import */ var _barrel_optimize_names_BriefcaseBusinessIcon_Building2_Calendar_ClipboardList_Code2Icon_Component_FileText_LayoutDashboard_List_Puzzle_Settings_User_User2Icon_Users_WalletCards_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BriefcaseBusinessIcon,Building2,Calendar,ClipboardList,Code2Icon,Component,FileText,LayoutDashboard,List,Puzzle,Settings,User,User2Icon,Users,WalletCards!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/user-round.js\");\n/* harmony import */ var _barrel_optimize_names_BriefcaseBusinessIcon_Building2_Calendar_ClipboardList_Code2Icon_Component_FileText_LayoutDashboard_List_Puzzle_Settings_User_User2Icon_Users_WalletCards_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BriefcaseBusinessIcon,Building2,Calendar,ClipboardList,Code2Icon,Component,FileText,LayoutDashboard,List,Puzzle,Settings,User,User2Icon,Users,WalletCards!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/puzzle.js\");\n/* harmony import */ var _barrel_optimize_names_BriefcaseBusinessIcon_Building2_Calendar_ClipboardList_Code2Icon_Component_FileText_LayoutDashboard_List_Puzzle_Settings_User_User2Icon_Users_WalletCards_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BriefcaseBusinessIcon,Building2,Calendar,ClipboardList,Code2Icon,Component,FileText,LayoutDashboard,List,Puzzle,Settings,User,User2Icon,Users,WalletCards!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/component.js\");\n\nconst SideBarOptions = [\n    {\n        name: 'Dashboard',\n        icon: _barrel_optimize_names_BriefcaseBusinessIcon_Building2_Calendar_ClipboardList_Code2Icon_Component_FileText_LayoutDashboard_List_Puzzle_Settings_User_User2Icon_Users_WalletCards_lucide_react__WEBPACK_IMPORTED_MODULE_0__[\"default\"],\n        path: '/dashboard'\n    },\n    {\n        name: 'Find Jobs',\n        icon: _barrel_optimize_names_BriefcaseBusinessIcon_Building2_Calendar_ClipboardList_Code2Icon_Component_FileText_LayoutDashboard_List_Puzzle_Settings_User_User2Icon_Users_WalletCards_lucide_react__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n        path: '/jobs'\n    },\n    {\n        name: 'My Applications',\n        icon: _barrel_optimize_names_BriefcaseBusinessIcon_Building2_Calendar_ClipboardList_Code2Icon_Component_FileText_LayoutDashboard_List_Puzzle_Settings_User_User2Icon_Users_WalletCards_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n        path: '/dashboard/applications'\n    },\n    {\n        name: 'My Profile',\n        icon: _barrel_optimize_names_BriefcaseBusinessIcon_Building2_Calendar_ClipboardList_Code2Icon_Component_FileText_LayoutDashboard_List_Puzzle_Settings_User_User2Icon_Users_WalletCards_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        path: '/dashboard/profile'\n    },\n    {\n        name: 'Billing',\n        icon: _barrel_optimize_names_BriefcaseBusinessIcon_Building2_Calendar_ClipboardList_Code2Icon_Component_FileText_LayoutDashboard_List_Puzzle_Settings_User_User2Icon_Users_WalletCards_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        path: '/billing'\n    },\n    {\n        name: 'Settings',\n        icon: _barrel_optimize_names_BriefcaseBusinessIcon_Building2_Calendar_ClipboardList_Code2Icon_Component_FileText_LayoutDashboard_List_Puzzle_Settings_User_User2Icon_Users_WalletCards_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        path: '/settings'\n    }\n];\nconst CompanySideBarOptions = [\n    {\n        name: 'Dashboard',\n        icon: _barrel_optimize_names_BriefcaseBusinessIcon_Building2_Calendar_ClipboardList_Code2Icon_Component_FileText_LayoutDashboard_List_Puzzle_Settings_User_User2Icon_Users_WalletCards_lucide_react__WEBPACK_IMPORTED_MODULE_0__[\"default\"],\n        path: '/company/dashboard'\n    },\n    {\n        name: 'Jobs',\n        icon: _barrel_optimize_names_BriefcaseBusinessIcon_Building2_Calendar_ClipboardList_Code2Icon_Component_FileText_LayoutDashboard_List_Puzzle_Settings_User_User2Icon_Users_WalletCards_lucide_react__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n        path: '/company/jobs'\n    },\n    {\n        name: 'Submissions',\n        icon: _barrel_optimize_names_BriefcaseBusinessIcon_Building2_Calendar_ClipboardList_Code2Icon_Component_FileText_LayoutDashboard_List_Puzzle_Settings_User_User2Icon_Users_WalletCards_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        path: '/company/submissions'\n    },\n    {\n        name: 'Candidates',\n        icon: _barrel_optimize_names_BriefcaseBusinessIcon_Building2_Calendar_ClipboardList_Code2Icon_Component_FileText_LayoutDashboard_List_Puzzle_Settings_User_User2Icon_Users_WalletCards_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        path: '/company/candidates'\n    },\n    {\n        name: 'Company Profile',\n        icon: _barrel_optimize_names_BriefcaseBusinessIcon_Building2_Calendar_ClipboardList_Code2Icon_Component_FileText_LayoutDashboard_List_Puzzle_Settings_User_User2Icon_Users_WalletCards_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        path: '/company/profile'\n    },\n    {\n        name: 'Settings',\n        icon: _barrel_optimize_names_BriefcaseBusinessIcon_Building2_Calendar_ClipboardList_Code2Icon_Component_FileText_LayoutDashboard_List_Puzzle_Settings_User_User2Icon_Users_WalletCards_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        path: '/company/settings'\n    }\n];\nconst InterviewType = [\n    {\n        title: 'Technical',\n        icon: _barrel_optimize_names_BriefcaseBusinessIcon_Building2_Calendar_ClipboardList_Code2Icon_Component_FileText_LayoutDashboard_List_Puzzle_Settings_User_User2Icon_Users_WalletCards_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n    },\n    {\n        title: 'Behavioral',\n        icon: _barrel_optimize_names_BriefcaseBusinessIcon_Building2_Calendar_ClipboardList_Code2Icon_Component_FileText_LayoutDashboard_List_Puzzle_Settings_User_User2Icon_Users_WalletCards_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n    },\n    {\n        title: 'Experience',\n        icon: _barrel_optimize_names_BriefcaseBusinessIcon_Building2_Calendar_ClipboardList_Code2Icon_Component_FileText_LayoutDashboard_List_Puzzle_Settings_User_User2Icon_Users_WalletCards_lucide_react__WEBPACK_IMPORTED_MODULE_1__[\"default\"]\n    },\n    {\n        title: 'Problem Solving',\n        icon: _barrel_optimize_names_BriefcaseBusinessIcon_Building2_Calendar_ClipboardList_Code2Icon_Component_FileText_LayoutDashboard_List_Puzzle_Settings_User_User2Icon_Users_WalletCards_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n    },\n    {\n        title: 'Leadership',\n        icon: _barrel_optimize_names_BriefcaseBusinessIcon_Building2_Calendar_ClipboardList_Code2Icon_Component_FileText_LayoutDashboard_List_Puzzle_Settings_User_User2Icon_Users_WalletCards_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n    }\n];\nconst EmploymentTypes = [\n    \"Full-time\",\n    \"Part-time\",\n    \"Contract\",\n    \"Temporary\",\n    \"Internship\",\n    \"Freelance\"\n];\nconst LocationTypes = [\n    \"Remote\",\n    \"On-site\",\n    \"Hybrid\"\n];\nconst ExperienceLevels = [\n    \"Entry Level\",\n    \"Junior\",\n    \"Mid-Level\",\n    \"Senior\",\n    \"Lead\",\n    \"Manager\",\n    \"Director\",\n    \"Executive\"\n];\nconst QUESTIONS_PROMPT = `You are an expert technical interviewer.\nBased on the following inputs, generate a well-structured list of high-quality interview questions:\n\nJob Title: {{jobTitle}}\n\nJob Description: {{jobDescription}}\n\nInterview Duration: {{duration}}\n\nInterview Type: {{type}}\n\nExperience Level: {{experienceLevel}}\n\nRequired Skills: {{requiredSkills}}\n\nCompany Criteria: {{companyCriteria}}\n\nNumber of Questions: {{questionCount}}\n\n📝 Your task:\n\nAnalyze the job description to identify key responsibilities, required skills, and expected experience.\n\nGenerate a list of interview questions based on the interview duration and requested number of questions.\n\nAdjust the number and depth of questions to match the interview duration.\n\nEnsure the questions match the tone and structure of a real-life {{type}} interview.\n\nPay special attention to the company's specific criteria for the ideal candidate.\n\n🧩 Format your response in JSON format with array list of questions.\nformat: interviewQuestions=[\n{\n question:'',\n type:'Technical/Behavioral/Experience/Problem Solving/Leadership'\n},{\n...\n}]\n\n🎯 The goal is to create a structured, relevant, and time-optimized interview plan for a {{jobTitle}} role.`;\nconst FEEDBACK_PROMPT = `You are an expert HR interviewer and career coach. Analyze the following interview conversation and provide comprehensive feedback.\n\nINTERVIEW CONVERSATION:\n{{conversation}}\n\nJOB DETAILS:\n- Job Title: {{jobTitle}}\n- Job Description: {{jobDescription}}\n- Required Skills: {{requiredSkills}}\n- Company Criteria: {{companyCriteria}}\n\nANALYSIS INSTRUCTIONS:\n1. Evaluate the candidate's performance across multiple dimensions\n2. Provide specific examples from the conversation\n3. Give actionable improvement suggestions\n4. Rate each area on a scale of 1-10\n5. Calculate an overall match score based on job requirements\n\nProvide your response in the following JSON format:\n{\n    \"feedback\": {\n        \"overallScore\": <number 1-100>,\n        \"recommendation\": <true/false>,\n        \"recommendationMessage\": \"<one line recommendation>\",\n        \"ratings\": {\n            \"technicalSkills\": <number 1-10>,\n            \"communication\": <number 1-10>,\n            \"problemSolving\": <number 1-10>,\n            \"experience\": <number 1-10>,\n            \"culturalFit\": <number 1-10>,\n            \"enthusiasm\": <number 1-10>\n        },\n        \"strengths\": [\n            \"<specific strength with example>\",\n            \"<specific strength with example>\",\n            \"<specific strength with example>\"\n        ],\n        \"areasForImprovement\": [\n            {\n                \"area\": \"<area name>\",\n                \"issue\": \"<specific issue>\",\n                \"suggestion\": \"<actionable improvement suggestion>\"\n            },\n            {\n                \"area\": \"<area name>\",\n                \"issue\": \"<specific issue>\",\n                \"suggestion\": \"<actionable improvement suggestion>\"\n            }\n        ],\n        \"detailedAnalysis\": {\n            \"technicalCompetency\": \"<detailed analysis of technical skills>\",\n            \"communicationStyle\": \"<analysis of communication effectiveness>\",\n            \"problemSolvingApproach\": \"<analysis of problem-solving methodology>\",\n            \"experienceRelevance\": \"<how well experience matches the role>\"\n        },\n        \"interviewPerformance\": {\n            \"questionHandling\": \"<how well they answered questions>\",\n            \"clarityOfResponses\": \"<clarity and structure of answers>\",\n            \"examplesProvided\": \"<quality of examples given>\",\n            \"professionalDemeanor\": \"<professional presentation>\"\n        },\n        \"careerAdvice\": [\n            \"<specific career development suggestion>\",\n            \"<specific skill to develop>\",\n            \"<specific resource or action to take>\"\n        ],\n        \"matchAnalysis\": {\n            \"jobRequirementMatch\": <percentage 0-100>,\n            \"skillGaps\": [\"<skill gap 1>\", \"<skill gap 2>\"],\n            \"strongMatches\": [\"<strong match 1>\", \"<strong match 2>\"],\n            \"growthPotential\": \"<assessment of growth potential>\"\n        },\n        \"nextSteps\": [\n            \"<immediate action item>\",\n            \"<short-term goal>\",\n            \"<long-term development area>\"\n        ]\n    }\n}\n`;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./services/Constants.jsx\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "node:fs":
/*!**************************!*\
  !*** external "node:fs" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:fs");

/***/ }),

/***/ "node:stream":
/*!******************************!*\
  !*** external "node:stream" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:stream");

/***/ }),

/***/ "node:stream/web":
/*!**********************************!*\
  !*** external "node:stream/web" ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:stream/web");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "worker_threads":
/*!*********************************!*\
  !*** external "worker_threads" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("worker_threads");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/tr46","vendor-chunks/whatwg-url","vendor-chunks/lucide-react","vendor-chunks/webidl-conversions","vendor-chunks/formdata-node","vendor-chunks/openai","vendor-chunks/form-data-encoder","vendor-chunks/agentkeepalive","vendor-chunks/web-streams-polyfill","vendor-chunks/node-fetch","vendor-chunks/ms","vendor-chunks/humanize-ms","vendor-chunks/event-target-shim","vendor-chunks/abort-controller"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fai-model%2Froute&page=%2Fapi%2Fai-model%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fai-model%2Froute.jsx&appDir=C%3A%5CUsers%5Chp%5CDesktop%5CVoice-Agent-for-ITjobs%5CAI-Interview-Voice-Agent-for-IT-Jobs%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Chp%5CDesktop%5CVoice-Agent-for-ITjobs%5CAI-Interview-Voice-Agent-for-IT-Jobs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();