"use client"

import { useIsClient } from '@/hooks/useHydrationFix';

/**
 * NoSSR Component - Prevents server-side rendering for components that cause hydration mismatches
 * Use this wrapper for components that:
 * - Access browser APIs (localStorage, sessionStorage, window, document)
 * - Use dynamic content that differs between server and client
 * - Have third-party libraries that cause hydration issues
 */
export default function NoSSR({ children, fallback = null }) {
  const isClient = useIsClient();
  
  if (!isClient) {
    return fallback;
  }
  
  return children;
}

/**
 * Higher-order component version of NoSSR
 * Usage: export default withNoSSR(YourComponent);
 */
export function withNoSSR(Component, fallback = null) {
  const WrappedComponent = (props) => (
    <NoSSR fallback={fallback}>
      <Component {...props} />
    </NoSSR>
  );
  
  WrappedComponent.displayName = `withNoSSR(${Component.displayName || Component.name})`;
  
  return WrappedComponent;
}
