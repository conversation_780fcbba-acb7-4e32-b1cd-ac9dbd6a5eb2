// Simple Integration Client - URL-based communication
// This approach avoids database sharing and uses URL parameters to pass data

import {
  INTEGRATION_CONFIG,
  ERROR_CODES,
  createApiResponse,
  logIntegrationEvent
} from '../../shared/integration-config.js';

export class SimpleIntegrationClient {
  constructor() {
    this.baseUrl = INTEGRATION_CONFIG.VOICE_AGENT_URL;
  }

  // Generate interview questions using AI
  async generateQuestions({
    jobTitle,
    jobDescription = '',
    requiredSkills = '',
    experienceLevel = 'Mid-Level',
    questionCount = 5
  }) {
    try {
      console.log('Generating questions for:', { jobTitle, experienceLevel, questionCount });

      // Simple fallback questions based on job title and experience level
      const baseQuestions = [
        {
          id: 1,
          question: `Tell me about your experience with ${jobTitle} roles.`,
          category: 'experience',
          difficulty: experienceLevel.toLowerCase(),
          expectedDuration: 120
        },
        {
          id: 2,
          question: `What interests you most about this ${jobTitle} position?`,
          category: 'motivation',
          difficulty: 'basic',
          expectedDuration: 90
        },
        {
          id: 3,
          question: `How do you handle challenging situations in your work?`,
          category: 'problem_solving',
          difficulty: 'intermediate',
          expectedDuration: 120
        },
        {
          id: 4,
          question: `Where do you see yourself in your career in the next few years?`,
          category: 'career_goals',
          difficulty: 'basic',
          expectedDuration: 90
        },
        {
          id: 5,
          question: `Do you have any questions about the role or our company?`,
          category: 'engagement',
          difficulty: 'basic',
          expectedDuration: 60
        }
      ];

      // Add skill-specific questions if skills are provided
      if (requiredSkills) {
        const skillsArray = requiredSkills.split(',').map(s => s.trim());
        if (skillsArray.length > 0) {
          baseQuestions.splice(2, 0, {
            id: 6,
            question: `Can you describe your experience with ${skillsArray[0]}?`,
            category: 'technical_skills',
            difficulty: experienceLevel.toLowerCase(),
            expectedDuration: 150
          });
        }
      }

      return baseQuestions.slice(0, questionCount);

    } catch (error) {
      console.error('Error generating questions:', error);
      // Return fallback questions
      return [
        {
          id: 1,
          question: "Tell me about yourself and your background.",
          category: 'general',
          difficulty: 'basic',
          expectedDuration: 120
        },
        {
          id: 2,
          question: "Why are you interested in this position?",
          category: 'motivation',
          difficulty: 'basic',
          expectedDuration: 90
        },
        {
          id: 3,
          question: "What are your strengths and weaknesses?",
          category: 'self_assessment',
          difficulty: 'intermediate',
          expectedDuration: 120
        }
      ];
    }
  }

  // Start interview process - Simple URL-based approach
  async startInterview(interviewData) {
    try {
      logIntegrationEvent('info', 'main_platform', 'interview_start_requested',
        'Starting simple interview process', null, interviewData);

      // Generate unique session ID
      const sessionId = `main_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

      // Validate required data
      if (!interviewData.userEmail || !interviewData.jobTitle) {
        throw new Error('Missing required interview data: userEmail and jobTitle are required');
      }

      // Generate questions if not provided
      let questions = interviewData.questions;
      if (!questions || questions.length === 0) {
        questions = await this.generateQuestions({
          jobTitle: interviewData.jobTitle,
          jobDescription: interviewData.jobDescription,
          requiredSkills: interviewData.requiredSkills,
          experienceLevel: interviewData.experienceLevel,
          questionCount: interviewData.questionCount || 5
        });
      }

      // Prepare interview data to pass via URL parameters
      const interviewParams = {
        sessionId: sessionId,
        userEmail: interviewData.userEmail,
        userName: interviewData.userName || 'User',
        jobTitle: interviewData.jobTitle,
        jobDescription: interviewData.jobDescription || '',
        companyName: interviewData.companyName || 'Company',
        interviewType: interviewData.interviewType || 'job_application',
        duration: interviewData.duration || 30,
        experienceLevel: interviewData.experienceLevel || 'Mid-Level',
        requiredSkills: interviewData.requiredSkills || '',
        companyCriteria: interviewData.companyCriteria || '',
        questionCount: questions.length,
        jobId: interviewData.jobId || '',
        companyId: interviewData.companyId || '',
        questions: questions
      };

      // Encode the interview data as base64 to pass safely in URL
      const encodedData = btoa(JSON.stringify(interviewParams));

      // Create the voice agent URL with encoded data
      const voiceAgentUrl = `${INTEGRATION_CONFIG.VOICE_AGENT_URL}/interview/external?data=${encodedData}`;
      
      logIntegrationEvent('info', 'main_platform', 'interview_process_ready',
        'Interview process ready, redirecting to voice agent', sessionId, {
        voiceAgentUrl: voiceAgentUrl
      });

      console.log('✅ Interview session created successfully:', {
        sessionId,
        userEmail: interviewData.userEmail,
        jobTitle: interviewData.jobTitle,
        questionCount: questions.length
      });

      return {
        sessionId,
        voiceAgentUrl: voiceAgentUrl,
        estimatedDuration: interviewParams.duration,
        questionCount: questions.length,
        success: true
      };

    } catch (error) {
      console.error('❌ Failed to start interview:', error);
      logIntegrationEvent('error', 'main_platform', 'interview_start_failed',
        'Failed to start interview process', null, { error: error.message });
      throw error;
    }
  }

  // Handle feedback from voice agent (via webhook or API call)
  async handleInterviewFeedback(feedbackData) {
    try {
      logIntegrationEvent('info', 'main_platform', 'feedback_received',
        'Received interview feedback from voice agent', feedbackData.sessionId, feedbackData);

      // Process the feedback data
      // This could involve saving to database, sending emails, etc.
      console.log('📝 Interview feedback received:', {
        sessionId: feedbackData.sessionId,
        score: feedbackData.overallScore,
        status: feedbackData.status
      });

      return {
        success: true,
        message: 'Feedback processed successfully'
      };

    } catch (error) {
      console.error('❌ Failed to process feedback:', error);
      logIntegrationEvent('error', 'main_platform', 'feedback_processing_failed',
        'Failed to process interview feedback', feedbackData?.sessionId, { error: error.message });
      throw error;
    }
  }

  // Check if voice agent is available
  async checkVoiceAgentHealth() {
    try {
      const response = await fetch(`${INTEGRATION_CONFIG.VOICE_AGENT_URL}/api/health`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      return {
        available: response.ok,
        status: response.status,
        url: INTEGRATION_CONFIG.VOICE_AGENT_URL
      };

    } catch (error) {
      console.warn('Voice agent health check failed:', error.message);
      return {
        available: false,
        error: error.message,
        url: INTEGRATION_CONFIG.VOICE_AGENT_URL
      };
    }
  }
}

// Export singleton instance
export const simpleIntegrationClient = new SimpleIntegrationClient();
