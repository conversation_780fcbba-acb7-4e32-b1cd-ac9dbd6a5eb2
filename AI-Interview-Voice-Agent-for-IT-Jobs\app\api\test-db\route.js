import { supabase } from '@/services/supabaseClient';
import { NextResponse } from 'next/server';

export async function GET() {
    try {
        console.log('Testing database connection...');

        // Test basic connection
        const { data, error } = await supabase
            .from('Interviews')
            .select('count(*)')
            .limit(1);

        if (error) {
            console.error('Database connection error:', error);
            return NextResponse.json({
                success: false,
                error: error.message,
                details: error
            }, { status: 500 });
        }

        return NextResponse.json({
            success: true,
            message: 'Database connection successful',
            data: data
        });

    } catch (err) {
        console.error('Exception testing database:', err);
        return NextResponse.json({
            success: false,
            error: err.message,
            type: 'exception'
        }, { status: 500 });
    }
}

export async function POST() {
    try {
        console.log('Testing guest interview creation...');

        // Test creating a simple guest interview
        const testData = {
            interview_id: 'test-' + Date.now(),
            userEmail: '<EMAIL>',
            userName: 'Test Guest',
            jobPosition: 'Test Position',
            jobDescription: 'Test Description',
            type: 'Practice',
            duration: 15,
            experienceLevel: 'Mid-Level',
            requiredSkills: '',
            companyCriteria: '',
            questionList: [{ question: 'Test question?', type: 'Technical' }],
            companyId: null,
            jobId: null,
            is_guest: true
        };

        const { data, error } = await supabase
            .from('Interviews')
            .insert([testData])
            .select();

        if (error) {
            console.error('Test insert error:', error);
            return NextResponse.json({
                success: false,
                error: error.message,
                details: error,
                testData: testData
            }, { status: 500 });
        }

        return NextResponse.json({
            success: true,
            message: 'Test interview created successfully',
            data: data
        });

    } catch (err) {
        console.error('Exception testing insert:', err);
        return NextResponse.json({
            success: false,
            error: err.message,
            type: 'exception'
        }, { status: 500 });
    }
}
