"use client"
import React from 'react'
import { Card, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { 
    Brain, 
    MessageCircle, 
    Lightbulb, 
    User, 
    Award, 
    TrendingUp,
    BookOpen,
    Target,
    CheckCircle,
    AlertCircle
} from 'lucide-react'

function DetailedFeedback({ feedback, jobDetails }) {
    if (!feedback) return null;

    const { 
        detailedAnalysis,
        interviewPerformance,
        careerAdvice,
        matchAnalysis,
        nextSteps
    } = feedback;

    return (
        <div className="space-y-6">
            {/* Detailed Analysis */}
            <Card>
                <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                        <Brain className="w-5 h-5" />
                        Detailed Performance Analysis
                    </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                    <div>
                        <h4 className="font-semibold text-blue-600 mb-2 flex items-center gap-2">
                            <Brain className="w-4 h-4" />
                            Technical Competency
                        </h4>
                        <p className="text-sm text-gray-700">{detailedAnalysis?.technicalCompetency}</p>
                    </div>
                    
                    <Separator />
                    
                    <div>
                        <h4 className="font-semibold text-green-600 mb-2 flex items-center gap-2">
                            <MessageCircle className="w-4 h-4" />
                            Communication Style
                        </h4>
                        <p className="text-sm text-gray-700">{detailedAnalysis?.communicationStyle}</p>
                    </div>
                    
                    <Separator />
                    
                    <div>
                        <h4 className="font-semibold text-yellow-600 mb-2 flex items-center gap-2">
                            <Lightbulb className="w-4 h-4" />
                            Problem-Solving Approach
                        </h4>
                        <p className="text-sm text-gray-700">{detailedAnalysis?.problemSolvingApproach}</p>
                    </div>
                    
                    <Separator />
                    
                    <div>
                        <h4 className="font-semibold text-purple-600 mb-2 flex items-center gap-2">
                            <Award className="w-4 h-4" />
                            Experience Relevance
                        </h4>
                        <p className="text-sm text-gray-700">{detailedAnalysis?.experienceRelevance}</p>
                    </div>
                </CardContent>
            </Card>

            {/* Interview Performance */}
            <Card>
                <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                        <User className="w-5 h-5" />
                        Interview Performance Breakdown
                    </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                    <div className="grid md:grid-cols-2 gap-4">
                        <div>
                            <h4 className="font-semibold text-sm mb-2">Question Handling</h4>
                            <p className="text-xs text-gray-600">{interviewPerformance?.questionHandling}</p>
                        </div>
                        <div>
                            <h4 className="font-semibold text-sm mb-2">Clarity of Responses</h4>
                            <p className="text-xs text-gray-600">{interviewPerformance?.clarityOfResponses}</p>
                        </div>
                        <div>
                            <h4 className="font-semibold text-sm mb-2">Examples Provided</h4>
                            <p className="text-xs text-gray-600">{interviewPerformance?.examplesProvided}</p>
                        </div>
                        <div>
                            <h4 className="font-semibold text-sm mb-2">Professional Demeanor</h4>
                            <p className="text-xs text-gray-600">{interviewPerformance?.professionalDemeanor}</p>
                        </div>
                    </div>
                </CardContent>
            </Card>

            {/* Job Match Analysis */}
            <Card>
                <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                        <Target className="w-5 h-5" />
                        Job Match Analysis
                    </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                    <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">Job Requirement Match</span>
                        <Badge variant="outline" className="text-lg px-3 py-1">
                            {matchAnalysis?.jobRequirementMatch}%
                        </Badge>
                    </div>
                    
                    <div className="grid md:grid-cols-2 gap-4">
                        <div>
                            <h4 className="font-semibold text-green-600 mb-2 flex items-center gap-2">
                                <CheckCircle className="w-4 h-4" />
                                Strong Matches
                            </h4>
                            <ul className="space-y-1">
                                {matchAnalysis?.strongMatches?.map((match, index) => (
                                    <li key={index} className="text-sm text-gray-700 flex items-center gap-2">
                                        <CheckCircle className="w-3 h-3 text-green-500" />
                                        {match}
                                    </li>
                                ))}
                            </ul>
                        </div>
                        
                        <div>
                            <h4 className="font-semibold text-orange-600 mb-2 flex items-center gap-2">
                                <AlertCircle className="w-4 h-4" />
                                Skill Gaps
                            </h4>
                            <ul className="space-y-1">
                                {matchAnalysis?.skillGaps?.map((gap, index) => (
                                    <li key={index} className="text-sm text-gray-700 flex items-center gap-2">
                                        <AlertCircle className="w-3 h-3 text-orange-500" />
                                        {gap}
                                    </li>
                                ))}
                            </ul>
                        </div>
                    </div>
                    
                    <div>
                        <h4 className="font-semibold text-blue-600 mb-2">Growth Potential</h4>
                        <p className="text-sm text-gray-700">{matchAnalysis?.growthPotential}</p>
                    </div>
                </CardContent>
            </Card>

            {/* Career Advice */}
            <Card>
                <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                        <BookOpen className="w-5 h-5" />
                        Career Development Advice
                    </CardTitle>
                </CardHeader>
                <CardContent>
                    <div className="space-y-3">
                        {careerAdvice?.map((advice, index) => (
                            <div key={index} className="flex items-start gap-2">
                                <TrendingUp className="w-4 h-4 text-blue-500 mt-0.5 flex-shrink-0" />
                                <p className="text-sm text-gray-700">{advice}</p>
                            </div>
                        ))}
                    </div>
                </CardContent>
            </Card>

            {/* Next Steps */}
            <Card>
                <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                        <Target className="w-5 h-5" />
                        Recommended Next Steps
                    </CardTitle>
                </CardHeader>
                <CardContent>
                    <div className="space-y-3">
                        {nextSteps?.map((step, index) => (
                            <div key={index} className="flex items-start gap-3">
                                <div className="w-6 h-6 rounded-full bg-blue-100 text-blue-600 flex items-center justify-center text-xs font-bold">
                                    {index + 1}
                                </div>
                                <p className="text-sm text-gray-700">{step}</p>
                            </div>
                        ))}
                    </div>
                </CardContent>
            </Card>
        </div>
    );
}

export default DetailedFeedback;
