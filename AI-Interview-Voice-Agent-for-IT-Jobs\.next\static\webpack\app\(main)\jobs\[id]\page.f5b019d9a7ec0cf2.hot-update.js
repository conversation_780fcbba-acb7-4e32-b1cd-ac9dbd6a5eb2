"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(main)/jobs/[id]/page",{

/***/ "(app-pages-browser)/./app/(main)/jobs/[id]/page.jsx":
/*!***************************************!*\
  !*** ./app/(main)/jobs/[id]/page.jsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _app_provider__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/app/provider */ \"(app-pages-browser)/./app/provider.jsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.jsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.jsx\");\n/* harmony import */ var _services_supabaseClient__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/services/supabaseClient */ \"(app-pages-browser)/./services/supabaseClient.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BriefcaseBusiness_Building2_Calendar_Clock_DollarSign_ExternalLink_MapPin_Sparkles_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BriefcaseBusiness,Building2,Calendar,Clock,DollarSign,ExternalLink,MapPin,Sparkles,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BriefcaseBusiness_Building2_Calendar_Clock_DollarSign_ExternalLink_MapPin_Sparkles_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BriefcaseBusiness,Building2,Calendar,Clock,DollarSign,ExternalLink,MapPin,Sparkles,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building-2.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BriefcaseBusiness_Building2_Calendar_Clock_DollarSign_ExternalLink_MapPin_Sparkles_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BriefcaseBusiness,Building2,Calendar,Clock,DollarSign,ExternalLink,MapPin,Sparkles,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/briefcase-business.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BriefcaseBusiness_Building2_Calendar_Clock_DollarSign_ExternalLink_MapPin_Sparkles_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BriefcaseBusiness,Building2,Calendar,Clock,DollarSign,ExternalLink,MapPin,Sparkles,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BriefcaseBusiness_Building2_Calendar_Clock_DollarSign_ExternalLink_MapPin_Sparkles_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BriefcaseBusiness,Building2,Calendar,Clock,DollarSign,ExternalLink,MapPin,Sparkles,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BriefcaseBusiness_Building2_Calendar_Clock_DollarSign_ExternalLink_MapPin_Sparkles_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BriefcaseBusiness,Building2,Calendar,Clock,DollarSign,ExternalLink,MapPin,Sparkles,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BriefcaseBusiness_Building2_Calendar_Clock_DollarSign_ExternalLink_MapPin_Sparkles_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BriefcaseBusiness,Building2,Calendar,Clock,DollarSign,ExternalLink,MapPin,Sparkles,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BriefcaseBusiness_Building2_Calendar_Clock_DollarSign_ExternalLink_MapPin_Sparkles_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BriefcaseBusiness,Building2,Calendar,Clock,DollarSign,ExternalLink,MapPin,Sparkles,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.jsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./components/ui/separator.jsx\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _lib_simple_integration_client__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/lib/simple-integration-client */ \"(app-pages-browser)/./lib/simple-integration-client.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nfunction JobDetailPage() {\n    var _job_Companies, _job_Companies1, _job_Companies2, _job_Companies3, _job_Companies4, _job_Companies5;\n    _s();\n    const [job, setJob] = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(true);\n    const [applying, setApplying] = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(false);\n    const { id } = (0,next_navigation__WEBPACK_IMPORTED_MODULE_6__.useParams)();\n    const { user } = (0,_app_provider__WEBPACK_IMPORTED_MODULE_1__.useUser)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_7__.useEffect)({\n        \"JobDetailPage.useEffect\": ()=>{\n            if (id) {\n                fetchJobDetails();\n            }\n        }\n    }[\"JobDetailPage.useEffect\"], [\n        id\n    ]);\n    const fetchJobDetails = async ()=>{\n        try {\n            setLoading(true);\n            // Try with specific foreign key first\n            let { data, error } = await _services_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.from('Jobs').select(\"\\n                    *,\\n                    Companies!Jobs_company_id_fkey(id, name, picture, industry_type, description, website)\\n                \").eq('id', id).single();\n            // If that fails, try alternative foreign key\n            if (error && (error.code === 'PGRST200' || error.code === 'PGRST201')) {\n                console.log(\"Trying alternative foreign key...\");\n                const { data: altData, error: altError } = await _services_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.from('Jobs').select(\"\\n                        *,\\n                        Companies!jobs_company_id_fkey(id, name, picture, industry_type, description, website)\\n                    \").eq('id', id).single();\n                if (!altError) {\n                    data = altData;\n                    error = null;\n                } else {\n                    console.log(\"Both foreign keys failed, fetching separately...\");\n                    // Fetch job and company separately\n                    const { data: jobData, error: jobError } = await _services_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.from('Jobs').select('*').eq('id', id).single();\n                    if (jobError) {\n                        console.error(\"Error fetching job:\", jobError);\n                        console.error(\"Full error details:\", JSON.stringify(jobError, null, 2));\n                        sonner__WEBPACK_IMPORTED_MODULE_10__.toast.error(\"Failed to load job details\");\n                        return;\n                    }\n                    if (jobData && jobData.company_id) {\n                        const { data: companyData, error: companyError } = await _services_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.from('Companies').select('id, name, picture, industry_type, description, website').eq('id', jobData.company_id).single();\n                        if (companyError) {\n                            console.error(\"Error fetching company:\", companyError);\n                            // Continue with job data only\n                            data = {\n                                ...jobData,\n                                Companies: null\n                            };\n                        } else {\n                            data = {\n                                ...jobData,\n                                Companies: companyData\n                            };\n                        }\n                        error = null;\n                    } else {\n                        data = jobData;\n                        error = null;\n                    }\n                }\n            }\n            if (error) {\n                console.error(\"Error fetching job details:\", error);\n                console.error(\"Full error details:\", JSON.stringify(error, null, 2));\n                sonner__WEBPACK_IMPORTED_MODULE_10__.toast.error(\"Failed to load job details\");\n            } else {\n                console.log(\"Fetched job details:\", data);\n                setJob(data);\n            }\n        } catch (error) {\n            console.error(\"Exception fetching job details:\", error);\n            console.error(\"Full exception details:\", JSON.stringify(error, null, 2));\n            sonner__WEBPACK_IMPORTED_MODULE_10__.toast.error(\"An error occurred while loading the job\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleApply = async ()=>{\n        if (!user) {\n            sonner__WEBPACK_IMPORTED_MODULE_10__.toast.error(\"Please sign in to apply for this job\");\n            router.push('/auth');\n            return;\n        }\n        // Check if user has credits for job application\n        try {\n            const { data: canApply, error: creditError } = await _services_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.rpc('can_apply_for_job', {\n                user_id_param: String(user.id)\n            });\n            if (creditError) {\n                console.error('Error checking credits:', creditError);\n                console.error('Full credit error:', JSON.stringify(creditError, null, 2));\n                // If function doesn't exist or has issues, continue without credit check\n                if (creditError.code === '42883' || creditError.code === 'PGRST203' || creditError.code === 'PGRST202') {\n                    console.log('Credit function not available or has issues, proceeding with application');\n                } else {\n                    console.log('Credit check failed, but proceeding with application anyway');\n                }\n            } else if (!canApply) {\n                sonner__WEBPACK_IMPORTED_MODULE_10__.toast.error('Insufficient credits for job application. Please purchase more credits to continue.');\n                router.push('/billing');\n                return;\n            }\n        } catch (error) {\n            console.error('Exception checking credits:', error);\n            // Continue without credit check if there's an error\n            console.log('Proceeding with application despite credit check error');\n        }\n        try {\n            var _job_Companies;\n            setApplying(true);\n            console.log(\"Starting integrated interview process...\");\n            // Prepare interview data for the integrated system\n            const interviewData = {\n                userEmail: user.email,\n                userName: user.name || 'Candidate',\n                jobTitle: job.job_title,\n                jobDescription: job.job_description,\n                companyName: ((_job_Companies = job.Companies) === null || _job_Companies === void 0 ? void 0 : _job_Companies.name) || 'Company',\n                interviewType: 'job_application',\n                duration: 30,\n                experienceLevel: job.experience_level || 'Mid-Level',\n                requiredSkills: job.required_skills || '',\n                companyCriteria: job.ai_criteria || '',\n                questionCount: job.question_count || 10,\n                jobId: job.id,\n                companyId: job.company_id\n            };\n            console.log(\"Interview data prepared:\", interviewData);\n            // Start the integrated interview process\n            const interviewSession = await integrationClient.startInterview(interviewData);\n            console.log(\"Interview session created:\", interviewSession);\n            // Deduct credits for job application\n            try {\n                const { data: creditDeducted, error: creditDeductionError } = await _services_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.rpc('deduct_credits_for_application', {\n                    user_id_param: String(user.id),\n                    job_id_param: String(job.id)\n                });\n                if (creditDeductionError) {\n                    console.error(\"Error deducting credits:\", creditDeductionError);\n                    console.error(\"Full credit deduction error:\", JSON.stringify(creditDeductionError, null, 2));\n                    // If function doesn't exist or has issues, continue without credit deduction\n                    if (creditDeductionError.code === '42883' || creditDeductionError.code === 'PGRST203' || creditDeductionError.code === 'PGRST202') {\n                        console.log('Credit deduction function not available or has issues, proceeding with application');\n                    } else {\n                        console.log('Credit deduction failed, but proceeding with application anyway');\n                    }\n                } else if (!creditDeducted) {\n                    console.log(\"Credit deduction returned false, but proceeding with application anyway\");\n                }\n            } catch (error) {\n                console.error(\"Exception deducting credits:\", error);\n                // Continue without credit deduction if there's an error\n                console.log('Proceeding with application despite credit deduction error');\n            }\n            // Create a job submission record\n            const submissionData = {\n                job_id: job.id,\n                user_id: user.id,\n                user_name: user.name || 'Candidate',\n                user_email: user.email,\n                application_status: 'pending',\n                interview_completed: false\n            };\n            console.log(\"Creating job submission with data:\", submissionData);\n            const { data: submission, error: submissionError } = await _services_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.from('Job_Submissions').insert([\n                submissionData\n            ]).select();\n            if (submissionError) {\n                console.error(\"Error creating job submission:\", submissionError);\n                sonner__WEBPACK_IMPORTED_MODULE_10__.toast.warning(\"Application created but submission record failed\");\n            // Continue anyway since the interview session was created\n            } else {\n                console.log(\"Job submission created:\", submission);\n            }\n            sonner__WEBPACK_IMPORTED_MODULE_10__.toast.success(\"Application started! Redirecting to voice interview...\");\n            // Redirect to the voice agent interview\n            setTimeout(()=>{\n                window.open(interviewSession.voiceAgentUrl, '_blank');\n                // Also redirect to a status page where they can track the interview\n                router.push(\"/interview-status/\".concat(interviewSession.sessionId));\n            }, 1500);\n        } catch (error) {\n            console.error(\"Error applying for job:\", error);\n            sonner__WEBPACK_IMPORTED_MODULE_10__.toast.error(\"Failed to apply for this job: \".concat(error.message || \"Unknown error\"));\n            setApplying(false);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto py-12 flex justify-center items-center min-h-[60vh]\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                lineNumber: 246,\n                columnNumber: 17\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n            lineNumber: 245,\n            columnNumber: 13\n        }, this);\n    }\n    if (!job) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto py-12 text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-2xl font-bold mb-4\",\n                    children: \"Job Not Found\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                    lineNumber: 254,\n                    columnNumber: 17\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"mb-6\",\n                    children: \"The job you're looking for doesn't exist or has been removed.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                    lineNumber: 255,\n                    columnNumber: 17\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                    href: \"/jobs\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BriefcaseBusiness_Building2_Calendar_Clock_DollarSign_ExternalLink_MapPin_Sparkles_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                className: \"mr-2 h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                lineNumber: 258,\n                                columnNumber: 25\n                            }, this),\n                            \"Back to Jobs\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                        lineNumber: 257,\n                        columnNumber: 21\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                    lineNumber: 256,\n                    columnNumber: 17\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n            lineNumber: 253,\n            columnNumber: 13\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto py-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                href: \"/jobs\",\n                className: \"inline-flex items-center text-sm text-muted-foreground hover:text-foreground mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BriefcaseBusiness_Building2_Calendar_Clock_DollarSign_ExternalLink_MapPin_Sparkles_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        className: \"mr-1 h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                        lineNumber: 269,\n                        columnNumber: 17\n                    }, this),\n                    \"Back to all jobs\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                lineNumber: 268,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-start\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                        className: \"text-2xl\",\n                                                        children: job.job_title\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                        lineNumber: 279,\n                                                        columnNumber: 37\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                                        className: \"flex items-center mt-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BriefcaseBusiness_Building2_Calendar_Clock_DollarSign_ExternalLink_MapPin_Sparkles_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"h-4 w-4 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                                lineNumber: 281,\n                                                                columnNumber: 41\n                                                            }, this),\n                                                            ((_job_Companies = job.Companies) === null || _job_Companies === void 0 ? void 0 : _job_Companies.name) || \"Company\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                        lineNumber: 280,\n                                                        columnNumber: 37\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                lineNumber: 278,\n                                                columnNumber: 33\n                                            }, this),\n                                            ((_job_Companies1 = job.Companies) === null || _job_Companies1 === void 0 ? void 0 : _job_Companies1.picture) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-16 w-16 rounded-lg bg-gray-100 flex items-center justify-center overflow-hidden\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                    src: job.Companies.picture,\n                                                    alt: job.Companies.name,\n                                                    className: \"h-full w-full object-cover\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 287,\n                                                    columnNumber: 41\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                lineNumber: 286,\n                                                columnNumber: 37\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                        lineNumber: 277,\n                                        columnNumber: 29\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 276,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-wrap gap-2 mb-6\",\n                                            children: [\n                                                job.employment_type && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                                    variant: \"outline\",\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BriefcaseBusiness_Building2_Calendar_Clock_DollarSign_ExternalLink_MapPin_Sparkles_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                            className: \"h-3 w-3 mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 300,\n                                                            columnNumber: 41\n                                                        }, this),\n                                                        job.employment_type\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 299,\n                                                    columnNumber: 37\n                                                }, this),\n                                                job.location_type && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                                    variant: \"outline\",\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BriefcaseBusiness_Building2_Calendar_Clock_DollarSign_ExternalLink_MapPin_Sparkles_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            className: \"h-3 w-3 mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 306,\n                                                            columnNumber: 41\n                                                        }, this),\n                                                        job.location_type\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 305,\n                                                    columnNumber: 37\n                                                }, this),\n                                                job.experience_level && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                                    variant: \"outline\",\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BriefcaseBusiness_Building2_Calendar_Clock_DollarSign_ExternalLink_MapPin_Sparkles_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            className: \"h-3 w-3 mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 312,\n                                                            columnNumber: 41\n                                                        }, this),\n                                                        job.experience_level\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 311,\n                                                    columnNumber: 37\n                                                }, this),\n                                                job.salary_range && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                                    variant: \"outline\",\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BriefcaseBusiness_Building2_Calendar_Clock_DollarSign_ExternalLink_MapPin_Sparkles_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                            className: \"h-3 w-3 mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 318,\n                                                            columnNumber: 41\n                                                        }, this),\n                                                        job.salary_range\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 317,\n                                                    columnNumber: 37\n                                                }, this),\n                                                job.application_deadline && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                                    variant: \"outline\",\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BriefcaseBusiness_Building2_Calendar_Clock_DollarSign_ExternalLink_MapPin_Sparkles_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                            className: \"h-3 w-3 mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 324,\n                                                            columnNumber: 41\n                                                        }, this),\n                                                        \"Due: \",\n                                                        new Date(job.application_deadline).toLocaleDateString()\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 323,\n                                                    columnNumber: 37\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 297,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-lg font-semibold mb-2\",\n                                                            children: \"Job Description\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 332,\n                                                            columnNumber: 37\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"whitespace-pre-line\",\n                                                            children: job.job_description || \"No description provided.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 333,\n                                                            columnNumber: 37\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 331,\n                                                    columnNumber: 33\n                                                }, this),\n                                                job.required_skills && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-lg font-semibold mb-2\",\n                                                            children: \"Required Skills\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 338,\n                                                            columnNumber: 41\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"whitespace-pre-line\",\n                                                            children: job.required_skills\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 339,\n                                                            columnNumber: 41\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 337,\n                                                    columnNumber: 37\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 330,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 296,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardFooter, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        className: \"w-full\",\n                                        size: \"lg\",\n                                        onClick: handleApply,\n                                        disabled: applying,\n                                        children: applying ? \"Starting Application...\" : \"Apply with AI Interview\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                        lineNumber: 345,\n                                        columnNumber: 29\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 344,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                            lineNumber: 275,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                        lineNumber: 274,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            children: \"About the Company\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 360,\n                                            columnNumber: 29\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                        lineNumber: 359,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BriefcaseBusiness_Building2_Calendar_Clock_DollarSign_ExternalLink_MapPin_Sparkles_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            className: \"h-5 w-5 mr-2 text-muted-foreground\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 365,\n                                                            columnNumber: 37\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: (_job_Companies2 = job.Companies) === null || _job_Companies2 === void 0 ? void 0 : _job_Companies2.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 366,\n                                                            columnNumber: 37\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 364,\n                                                    columnNumber: 33\n                                                }, this),\n                                                ((_job_Companies3 = job.Companies) === null || _job_Companies3 === void 0 ? void 0 : _job_Companies3.industry_type) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BriefcaseBusiness_Building2_Calendar_Clock_DollarSign_ExternalLink_MapPin_Sparkles_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                            className: \"h-5 w-5 mr-2 text-muted-foreground\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 370,\n                                                            columnNumber: 41\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: job.Companies.industry_type\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 371,\n                                                            columnNumber: 41\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 369,\n                                                    columnNumber: 37\n                                                }, this),\n                                                ((_job_Companies4 = job.Companies) === null || _job_Companies4 === void 0 ? void 0 : _job_Companies4.website) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: job.Companies.website,\n                                                        target: \"_blank\",\n                                                        rel: \"noopener noreferrer\",\n                                                        className: \"text-primary hover:underline\",\n                                                        children: \"Visit Website\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                        lineNumber: 376,\n                                                        columnNumber: 41\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 375,\n                                                    columnNumber: 37\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_9__.Separator, {}, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 386,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-muted-foreground\",\n                                                        children: ((_job_Companies5 = job.Companies) === null || _job_Companies5 === void 0 ? void 0 : _job_Companies5.description) || \"No company description available.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                        lineNumber: 388,\n                                                        columnNumber: 37\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 387,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 363,\n                                            columnNumber: 29\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                        lineNumber: 362,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                lineNumber: 358,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                className: \"mt-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BriefcaseBusiness_Building2_Calendar_Clock_DollarSign_ExternalLink_MapPin_Sparkles_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                    className: \"h-5 w-5 mr-2 text-yellow-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 399,\n                                                    columnNumber: 33\n                                                }, this),\n                                                \"AI Interview Process\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 398,\n                                            columnNumber: 29\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                        lineNumber: 397,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-muted-foreground mb-4\",\n                                                children: \"This job uses AI-powered interviews to assess candidates. Here's how it works:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                lineNumber: 404,\n                                                columnNumber: 29\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                                className: \"space-y-2 list-decimal list-inside text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: 'Click \"Apply with AI Interview\" to start'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                        lineNumber: 408,\n                                                        columnNumber: 33\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            \"Complete a \",\n                                                            job.question_count || 10,\n                                                            \"-question interview\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                        lineNumber: 409,\n                                                        columnNumber: 33\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Your responses will be analyzed by AI\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                        lineNumber: 410,\n                                                        columnNumber: 33\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"The company will review your results\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                        lineNumber: 411,\n                                                        columnNumber: 33\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"You'll be contacted if selected for next steps\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                        lineNumber: 412,\n                                                        columnNumber: 33\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                                lineNumber: 407,\n                                                columnNumber: 29\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                        lineNumber: 403,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                                lineNumber: 396,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                        lineNumber: 357,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n                lineNumber: 273,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\AI-Interview-Voice-Agent-for-IT-Jobs\\\\app\\\\(main)\\\\jobs\\\\[id]\\\\page.jsx\",\n        lineNumber: 267,\n        columnNumber: 9\n    }, this);\n}\n_s(JobDetailPage, \"Rlqc4I/yV/2ErfcAy4635RRVPMM=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_6__.useParams,\n        _app_provider__WEBPACK_IMPORTED_MODULE_1__.useUser,\n        next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter\n    ];\n});\n_c = JobDetailPage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (JobDetailPage);\nvar _c;\n$RefreshReg$(_c, \"JobDetailPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/(main)/jobs/[id]/page.jsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./lib/simple-integration-client.js":
/*!******************************************!*\
  !*** ./lib/simple-integration-client.js ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SimpleIntegrationClient: () => (/* binding */ SimpleIntegrationClient),\n/* harmony export */   simpleIntegrationClient: () => (/* binding */ simpleIntegrationClient)\n/* harmony export */ });\n/* harmony import */ var _shared_integration_config_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../shared/integration-config.js */ \"(app-pages-browser)/../shared/integration-config.js\");\n// Simple Integration Client - URL-based communication\n// This approach avoids database sharing and uses URL parameters to pass data\n\nclass SimpleIntegrationClient {\n    // Generate interview questions using AI\n    async generateQuestions(param) {\n        let { jobTitle, jobDescription = '', requiredSkills = '', experienceLevel = 'Mid-Level', questionCount = 5 } = param;\n        try {\n            console.log('Generating questions for:', {\n                jobTitle,\n                experienceLevel,\n                questionCount\n            });\n            // Simple fallback questions based on job title and experience level\n            const baseQuestions = [\n                {\n                    id: 1,\n                    question: \"Tell me about your experience with \".concat(jobTitle, \" roles.\"),\n                    category: 'experience',\n                    difficulty: experienceLevel.toLowerCase(),\n                    expectedDuration: 120\n                },\n                {\n                    id: 2,\n                    question: \"What interests you most about this \".concat(jobTitle, \" position?\"),\n                    category: 'motivation',\n                    difficulty: 'basic',\n                    expectedDuration: 90\n                },\n                {\n                    id: 3,\n                    question: \"How do you handle challenging situations in your work?\",\n                    category: 'problem_solving',\n                    difficulty: 'intermediate',\n                    expectedDuration: 120\n                },\n                {\n                    id: 4,\n                    question: \"Where do you see yourself in your career in the next few years?\",\n                    category: 'career_goals',\n                    difficulty: 'basic',\n                    expectedDuration: 90\n                },\n                {\n                    id: 5,\n                    question: \"Do you have any questions about the role or our company?\",\n                    category: 'engagement',\n                    difficulty: 'basic',\n                    expectedDuration: 60\n                }\n            ];\n            // Add skill-specific questions if skills are provided\n            if (requiredSkills) {\n                const skillsArray = requiredSkills.split(',').map((s)=>s.trim());\n                if (skillsArray.length > 0) {\n                    baseQuestions.splice(2, 0, {\n                        id: 6,\n                        question: \"Can you describe your experience with \".concat(skillsArray[0], \"?\"),\n                        category: 'technical_skills',\n                        difficulty: experienceLevel.toLowerCase(),\n                        expectedDuration: 150\n                    });\n                }\n            }\n            return baseQuestions.slice(0, questionCount);\n        } catch (error) {\n            console.error('Error generating questions:', error);\n            // Return fallback questions\n            return [\n                {\n                    id: 1,\n                    question: \"Tell me about yourself and your background.\",\n                    category: 'general',\n                    difficulty: 'basic',\n                    expectedDuration: 120\n                },\n                {\n                    id: 2,\n                    question: \"Why are you interested in this position?\",\n                    category: 'motivation',\n                    difficulty: 'basic',\n                    expectedDuration: 90\n                },\n                {\n                    id: 3,\n                    question: \"What are your strengths and weaknesses?\",\n                    category: 'self_assessment',\n                    difficulty: 'intermediate',\n                    expectedDuration: 120\n                }\n            ];\n        }\n    }\n    // Start interview process - Simple URL-based approach\n    async startInterview(interviewData) {\n        try {\n            (0,_shared_integration_config_js__WEBPACK_IMPORTED_MODULE_0__.logIntegrationEvent)('info', 'main_platform', 'interview_start_requested', 'Starting simple interview process', null, interviewData);\n            // Generate unique session ID\n            const sessionId = \"main_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substr(2, 9));\n            // Validate required data\n            if (!interviewData.userEmail || !interviewData.jobTitle) {\n                throw new Error('Missing required interview data: userEmail and jobTitle are required');\n            }\n            // Generate questions if not provided\n            let questions = interviewData.questions;\n            if (!questions || questions.length === 0) {\n                questions = await this.generateQuestions({\n                    jobTitle: interviewData.jobTitle,\n                    jobDescription: interviewData.jobDescription,\n                    requiredSkills: interviewData.requiredSkills,\n                    experienceLevel: interviewData.experienceLevel,\n                    questionCount: interviewData.questionCount || 5\n                });\n            }\n            // Prepare interview data to pass via URL parameters\n            const interviewParams = {\n                sessionId: sessionId,\n                userEmail: interviewData.userEmail,\n                userName: interviewData.userName || 'User',\n                jobTitle: interviewData.jobTitle,\n                jobDescription: interviewData.jobDescription || '',\n                companyName: interviewData.companyName || 'Company',\n                interviewType: interviewData.interviewType || 'job_application',\n                duration: interviewData.duration || 30,\n                experienceLevel: interviewData.experienceLevel || 'Mid-Level',\n                requiredSkills: interviewData.requiredSkills || '',\n                companyCriteria: interviewData.companyCriteria || '',\n                questionCount: questions.length,\n                jobId: interviewData.jobId || '',\n                companyId: interviewData.companyId || '',\n                questions: questions\n            };\n            // Encode the interview data as base64 to pass safely in URL\n            const encodedData = btoa(JSON.stringify(interviewParams));\n            // Create the voice agent URL with encoded data\n            const voiceAgentUrl = \"\".concat(_shared_integration_config_js__WEBPACK_IMPORTED_MODULE_0__.INTEGRATION_CONFIG.VOICE_AGENT_URL, \"/interview/external?data=\").concat(encodedData);\n            (0,_shared_integration_config_js__WEBPACK_IMPORTED_MODULE_0__.logIntegrationEvent)('info', 'main_platform', 'interview_process_ready', 'Interview process ready, redirecting to voice agent', sessionId, {\n                voiceAgentUrl: voiceAgentUrl\n            });\n            console.log('✅ Interview session created successfully:', {\n                sessionId,\n                userEmail: interviewData.userEmail,\n                jobTitle: interviewData.jobTitle,\n                questionCount: questions.length\n            });\n            return {\n                sessionId,\n                voiceAgentUrl: voiceAgentUrl,\n                estimatedDuration: interviewParams.duration,\n                questionCount: questions.length,\n                success: true\n            };\n        } catch (error) {\n            console.error('❌ Failed to start interview:', error);\n            (0,_shared_integration_config_js__WEBPACK_IMPORTED_MODULE_0__.logIntegrationEvent)('error', 'main_platform', 'interview_start_failed', 'Failed to start interview process', null, {\n                error: error.message\n            });\n            throw error;\n        }\n    }\n    // Handle feedback from voice agent (via webhook or API call)\n    async handleInterviewFeedback(feedbackData) {\n        try {\n            (0,_shared_integration_config_js__WEBPACK_IMPORTED_MODULE_0__.logIntegrationEvent)('info', 'main_platform', 'feedback_received', 'Received interview feedback from voice agent', feedbackData.sessionId, feedbackData);\n            // Process the feedback data\n            // This could involve saving to database, sending emails, etc.\n            console.log('📝 Interview feedback received:', {\n                sessionId: feedbackData.sessionId,\n                score: feedbackData.overallScore,\n                status: feedbackData.status\n            });\n            return {\n                success: true,\n                message: 'Feedback processed successfully'\n            };\n        } catch (error) {\n            console.error('❌ Failed to process feedback:', error);\n            (0,_shared_integration_config_js__WEBPACK_IMPORTED_MODULE_0__.logIntegrationEvent)('error', 'main_platform', 'feedback_processing_failed', 'Failed to process interview feedback', feedbackData === null || feedbackData === void 0 ? void 0 : feedbackData.sessionId, {\n                error: error.message\n            });\n            throw error;\n        }\n    }\n    // Check if voice agent is available\n    async checkVoiceAgentHealth() {\n        try {\n            const response = await fetch(\"\".concat(_shared_integration_config_js__WEBPACK_IMPORTED_MODULE_0__.INTEGRATION_CONFIG.VOICE_AGENT_URL, \"/api/health\"), {\n                method: 'GET',\n                headers: {\n                    'Content-Type': 'application/json'\n                }\n            });\n            return {\n                available: response.ok,\n                status: response.status,\n                url: _shared_integration_config_js__WEBPACK_IMPORTED_MODULE_0__.INTEGRATION_CONFIG.VOICE_AGENT_URL\n            };\n        } catch (error) {\n            console.warn('Voice agent health check failed:', error.message);\n            return {\n                available: false,\n                error: error.message,\n                url: _shared_integration_config_js__WEBPACK_IMPORTED_MODULE_0__.INTEGRATION_CONFIG.VOICE_AGENT_URL\n            };\n        }\n    }\n    constructor(){\n        this.baseUrl = _shared_integration_config_js__WEBPACK_IMPORTED_MODULE_0__.INTEGRATION_CONFIG.VOICE_AGENT_URL;\n    }\n}\n// Export singleton instance\nconst simpleIntegrationClient = new SimpleIntegrationClient();\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/simple-integration-client.js\n"));

/***/ })

});