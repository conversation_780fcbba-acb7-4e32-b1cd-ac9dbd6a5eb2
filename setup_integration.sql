-- Setup Integration API Keys and Test Data
-- Run this script to set up the integration between the two systems

-- =====================================================
-- CREATE INTEGRATION API KEYS
-- =====================================================

-- Insert API keys for system communication
-- Note: In production, use proper hashed keys and secure generation
INSERT INTO "Integration_API_Keys" (key_name, api_key_hash, system_name, permissions, rate_limit, is_active) VALUES
(
  'main_platform_key',
  encode(sha256('main-platform-api-key'::bytea), 'hex'),
  'main_platform',
  '["create_session", "get_status", "receive_feedback"]'::jsonb,
  1000,
  true
),
(
  'voice_agent_key', 
  encode(sha256('voice-agent-api-key'::bytea), 'hex'),
  'voice_agent',
  '["start_interview", "submit_feedback", "get_status"]'::jsonb,
  1000,
  true
) ON CONFLICT (key_name) DO UPDATE SET
  api_key_hash = EXCLUDED.api_key_hash,
  updated_at = NOW();

-- =====================================================
-- CREATE TEST INTERVIEW SESSION
-- =====================================================

-- Create a test interview session for debugging
INSERT INTO "Interview_Sessions" (
  main_interview_id,
  job_id,
  company_id,
  user_email,
  user_name,
  job_title,
  job_description,
  required_skills,
  experience_level,
  company_criteria,
  interview_type,
  duration,
  question_count,
  questions,
  status,
  voice_agent_url,
  integration_version,
  debug_info
) VALUES (
  'test_session_' || extract(epoch from now())::text,
  null,
  null,
  '<EMAIL>',
  'Test Candidate',
  'Software Engineer',
  'Full-stack development position requiring strong technical skills',
  'JavaScript, React, Node.js, Python',
  'Mid-Level',
  'Strong problem-solving skills and team collaboration',
  'job_application',
  30,
  5,
  '[
    {
      "id": "q1",
      "question": "Tell me about your experience with JavaScript and React.",
      "type": "technical",
      "difficulty": "medium",
      "expectedDuration": 120,
      "followUpAllowed": true,
      "metadata": {"category": "frontend"}
    },
    {
      "id": "q2", 
      "question": "Describe a challenging project you worked on recently.",
      "type": "behavioral",
      "difficulty": "medium",
      "expectedDuration": 180,
      "followUpAllowed": true,
      "metadata": {"category": "experience"}
    },
    {
      "id": "q3",
      "question": "How do you handle debugging complex issues?",
      "type": "technical",
      "difficulty": "medium", 
      "expectedDuration": 150,
      "followUpAllowed": true,
      "metadata": {"category": "problem_solving"}
    },
    {
      "id": "q4",
      "question": "What interests you about this position?",
      "type": "behavioral",
      "difficulty": "easy",
      "expectedDuration": 90,
      "followUpAllowed": true,
      "metadata": {"category": "motivation"}
    },
    {
      "id": "q5",
      "question": "Do you have any questions for us?",
      "type": "behavioral", 
      "difficulty": "easy",
      "expectedDuration": 60,
      "followUpAllowed": true,
      "metadata": {"category": "questions"}
    }
  ]'::jsonb,
  'pending',
  'http://localhost:3001/interview/external?sessionId=test_session_' || extract(epoch from now())::text,
  '1.0',
  '{"created_by": "setup_script", "purpose": "testing"}'::jsonb
) ON CONFLICT (main_interview_id) DO NOTHING;

-- =====================================================
-- CREATE SAMPLE FEEDBACK DATA
-- =====================================================

-- Create sample feedback for testing
INSERT INTO "Interview_Feedback_Bridge" (
  session_id,
  main_interview_id,
  voice_agent_interview_id,
  user_email,
  user_name,
  voice_agent_feedback,
  conversation_transcript,
  processed_feedback,
  overall_score,
  recommendation,
  strengths,
  areas_for_improvement,
  technical_assessment,
  communication_score,
  problem_solving_score,
  feedback_status
) 
SELECT 
  s.id,
  s.main_interview_id,
  'voice_agent_interview_' || extract(epoch from now())::text,
  s.user_email,
  s.user_name,
  '{
    "overallScore": 78,
    "technicalScore": 80,
    "communicationScore": 75,
    "problemSolvingScore": 82,
    "recommendation": "hire",
    "strengths": ["Strong technical knowledge", "Good problem-solving approach", "Clear communication"],
    "areasForImprovement": ["Could provide more specific examples", "Time management during responses"],
    "overallAssessment": "Candidate demonstrates solid technical skills and good communication. Recommended for hire.",
    "duration": 28,
    "questionsAsked": 5,
    "questionsAnswered": 5,
    "interviewQuality": "good",
    "completedAt": "' || now()::text || '"
  }'::jsonb,
  '[
    {
      "speaker": "ai",
      "message": "Hello! Welcome to your interview. Let''s start with the first question.",
      "timestamp": "' || (now() - interval '30 minutes')::text || '"
    },
    {
      "speaker": "user", 
      "message": "Thank you, I''m excited to be here.",
      "timestamp": "' || (now() - interval '29 minutes')::text || '"
    }
  ]'::jsonb,
  '{
    "feedback": {
      "strengths": ["Strong technical knowledge", "Good problem-solving approach", "Clear communication"],
      "areas_for_improvement": ["Could provide more specific examples", "Time management during responses"],
      "overall_assessment": "Candidate demonstrates solid technical skills and good communication. Recommended for hire.",
      "matchScore": 78,
      "technical_score": 80,
      "communication_score": 75,
      "problem_solving_score": 82,
      "recommendation": "hire",
      "interview_duration": 28,
      "questions_asked": 5,
      "questions_answered": 5,
      "interview_quality": "good"
    }
  }'::jsonb,
  78,
  'hire',
  ARRAY['Strong technical knowledge', 'Good problem-solving approach', 'Clear communication'],
  ARRAY['Could provide more specific examples', 'Time management during responses'],
  '{
    "technical_score": 80,
    "communication_score": 75, 
    "problem_solving_score": 82
  }'::jsonb,
  75,
  82,
  'processed'
FROM "Interview_Sessions" s 
WHERE s.main_interview_id LIKE 'test_session_%'
AND NOT EXISTS (
  SELECT 1 FROM "Interview_Feedback_Bridge" f 
  WHERE f.session_id = s.id
)
LIMIT 1;

-- =====================================================
-- CREATE INTEGRATION LOG ENTRIES
-- =====================================================

-- Add some sample log entries for testing
INSERT INTO "Integration_Logs" (
  session_id,
  log_level,
  component,
  action,
  message,
  request_data,
  response_data,
  user_email
)
SELECT 
  s.id,
  'info',
  'main_platform',
  'session_created',
  'Test interview session created successfully',
  '{"jobTitle": "Software Engineer", "userEmail": "<EMAIL>"}'::jsonb,
  '{"sessionId": "' || s.main_interview_id || '", "status": "created"}'::jsonb,
  '<EMAIL>'
FROM "Interview_Sessions" s 
WHERE s.main_interview_id LIKE 'test_session_%'
LIMIT 1;

-- =====================================================
-- VERIFICATION QUERIES
-- =====================================================

-- Check that everything was created correctly
DO $$
DECLARE
    api_key_count INTEGER;
    session_count INTEGER;
    feedback_count INTEGER;
    log_count INTEGER;
BEGIN
    -- Count API keys
    SELECT COUNT(*) INTO api_key_count FROM "Integration_API_Keys" WHERE is_active = true;
    
    -- Count test sessions
    SELECT COUNT(*) INTO session_count FROM "Interview_Sessions" WHERE main_interview_id LIKE 'test_session_%';
    
    -- Count feedback entries
    SELECT COUNT(*) INTO feedback_count FROM "Interview_Feedback_Bridge" WHERE feedback_status = 'processed';
    
    -- Count log entries
    SELECT COUNT(*) INTO log_count FROM "Integration_Logs" WHERE component = 'main_platform';
    
    RAISE NOTICE 'Integration setup completed:';
    RAISE NOTICE '- API Keys created: %', api_key_count;
    RAISE NOTICE '- Test sessions: %', session_count;
    RAISE NOTICE '- Feedback entries: %', feedback_count;
    RAISE NOTICE '- Log entries: %', log_count;
    
    IF api_key_count >= 2 AND session_count >= 1 THEN
        RAISE NOTICE 'Integration setup successful!';
    ELSE
        RAISE WARNING 'Integration setup may be incomplete. Please check the data.';
    END IF;
END $$;

-- =====================================================
-- ENVIRONMENT VARIABLES REMINDER
-- =====================================================

-- Display required environment variables
DO $$
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '=== REQUIRED ENVIRONMENT VARIABLES ===';
    RAISE NOTICE '';
    RAISE NOTICE 'Main Platform (.env.local):';
    RAISE NOTICE 'INTEGRATION_API_KEY=main-platform-api-key';
    RAISE NOTICE 'VOICE_AGENT_URL=http://localhost:3001';
    RAISE NOTICE 'INTEGRATION_SHARED_SECRET=your-secure-shared-secret';
    RAISE NOTICE '';
    RAISE NOTICE 'Voice Agent (.env.local):';
    RAISE NOTICE 'INTEGRATION_API_KEY=voice-agent-api-key';
    RAISE NOTICE 'MAIN_PLATFORM_URL=http://localhost:3000';
    RAISE NOTICE 'NEXT_PUBLIC_INTEGRATION_API_KEY=voice-agent-api-key';
    RAISE NOTICE '';
    RAISE NOTICE 'Both systems need:';
    RAISE NOTICE 'NEXT_PUBLIC_SUPABASE_URL=your_supabase_url';
    RAISE NOTICE 'SUPABASE_SERVICE_ROLE_KEY=your_service_role_key';
    RAISE NOTICE '';
    RAISE NOTICE '=== TESTING URLS ===';
    RAISE NOTICE '';
    RAISE NOTICE 'Main Platform Health: http://localhost:3000/api/integration/health';
    RAISE NOTICE 'Voice Agent Health: http://localhost:3001/api/integration/health';
    RAISE NOTICE '';
END $$;
