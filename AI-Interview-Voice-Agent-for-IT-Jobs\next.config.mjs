/** @type {import('next').NextConfig} */
const nextConfig = {
    reactStrictMode: false,
    images: {
        domains: ['lh3.googleusercontent.com']
    },
    // Experimental features to help with hydration
    experimental: {
        // Suppress hydration warnings in development
        suppressHydrationWarning: true,
    },
    // Compiler options
    compiler: {
        // Remove console logs in production
        removeConsole: process.env.NODE_ENV === 'production' ? {
            exclude: ['error', 'warn']
        } : false,
    },
    // Custom webpack configuration
    webpack: (config, { dev, isServer }) => {
        // Suppress specific warnings in development
        if (dev && !isServer) {
            config.infrastructureLogging = {
                level: 'error',
            };
        }
        return config;
    },
};

export default nextConfig;
