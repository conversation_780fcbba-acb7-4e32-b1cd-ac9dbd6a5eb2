"use client"

import { useState, useEffect } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import {
  Clock,
  CheckCircle,
  AlertCircle,
  ExternalLink,
  RefreshCw,
  User,
  Building,
  MessageSquare,
  Star,
  ArrowLeft
} from 'lucide-react';
import Link from 'next/link';
import { toast } from 'sonner';
// Removed integration client import - using direct database queries
import { supabase } from '@/services/supabaseClient';

export default function InterviewStatusPage() {
  const { sessionId } = useParams();
  const router = useRouter();

  const [sessionData, setSessionData] = useState(null);
  const [feedbackData, setFeedbackData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    if (sessionId) {
      loadSessionStatus();
      // Set up polling for status updates
      const interval = setInterval(loadSessionStatus, 10000); // Poll every 10 seconds
      return () => clearInterval(interval);
    }
  }, [sessionId]);

  const loadSessionStatus = async () => {
    try {
      setRefreshing(true);

      // Get interview data from main platform Interviews table
      const { data: interview, error: interviewError } = await supabase
        .from('Interviews')
        .select(`
          *,
          Companies (
            name,
            email
          )
        `)
        .eq('interview_id', sessionId)
        .single();

      if (interviewError) {
        throw new Error(`Interview not found: ${interviewError.message}`);
      }

      setSessionData(interview);

      // Check for feedback
      if (interview.status === 'completed') {
        const { data: feedback, error: feedbackError } = await supabase
          .from('interview-feedback')
          .select('*')
          .eq('interview_id', sessionId)
          .single();

        if (!feedbackError && feedback) {
          setFeedbackData(feedback);
        }
      }

      setError(null);
    } catch (err) {
      console.error('Error loading session status:', err);
      setError(err.message);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const openVoiceAgent = () => {
    if (sessionData) {
      // Prepare data for voice agent
      const voiceAgentData = {
        sessionId: `main_${sessionData.interview_id}`,
        userEmail: sessionData.userEmail,
        userName: sessionData.userName,
        jobTitle: sessionData.jobPosition,
        jobDescription: sessionData.jobDescription,
        companyName: 'Company',
        interviewType: 'job_application',
        duration: sessionData.duration || 30,
        experienceLevel: sessionData.experienceLevel,
        requiredSkills: sessionData.requiredSkills,
        companyCriteria: sessionData.companyCriteria,
        questionCount: sessionData.questionList?.length || 0,
        jobId: sessionData.jobId,
        companyId: sessionData.companyId,
        questions: (sessionData.questionList || []).map((q, index) => ({
          id: index + 1,
          question: q.question,
          type: q.type || 'general',
          difficulty: q.difficulty || 'medium',
          expectedDuration: q.expectedDuration || 120,
          followUpAllowed: true,
          metadata: q
        }))
      };

      // Encode the data for voice agent
      const jsonString = JSON.stringify(voiceAgentData);
      const encodedData = btoa(encodeURIComponent(jsonString));
      const voiceAgentUrl = `http://localhost:3001/interview/external?data=${encodedData}`;

      window.open(voiceAgentUrl, '_blank');
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed': return 'default';
      case 'in_progress': return 'secondary';
      case 'ready': return 'outline';
      case 'pending': return 'outline';
      default: return 'destructive';
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'completed': return 'Interview Completed';
      case 'in_progress': return 'Interview In Progress';
      case 'ready': return 'Ready to Start';
      case 'pending': return 'Setting Up Interview';
      default: return 'Unknown Status';
    }
  };

  const getProgressValue = (status) => {
    switch (status) {
      case 'completed': return 100;
      case 'in_progress': return 60;
      case 'ready': return 30;
      case 'pending': return 10;
      default: return 0;
    }
  };

  const formatScore = (score) => {
    if (typeof score !== 'number') return 'N/A';
    return `${Math.round(score)}%`;
  };

  const getRecommendationColor = (recommendation) => {
    switch (recommendation) {
      case 'hire': return 'text-green-600';
      case 'maybe': return 'text-yellow-600';
      case 'reject': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  const getRecommendationText = (recommendation) => {
    switch (recommendation) {
      case 'hire': return 'Recommended for Hire';
      case 'maybe': return 'Under Review';
      case 'reject': return 'Not Recommended';
      default: return 'Pending Review';
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto py-12 flex justify-center items-center min-h-[60vh]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary mx-auto mb-4"></div>
          <p>Loading interview status...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto py-12">
        <Card className="max-w-md mx-auto">
          <CardContent className="p-6 text-center">
            <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h2 className="text-xl font-semibold text-red-700 mb-2">Error</h2>
            <p className="text-red-600 mb-4">{error}</p>
            <Button onClick={() => router.push('/jobs')} variant="outline">
              Back to Jobs
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!sessionData) {
    return (
      <div className="container mx-auto py-12">
        <Card className="max-w-md mx-auto">
          <CardContent className="p-6 text-center">
            <AlertCircle className="h-12 w-12 text-gray-500 mx-auto mb-4" />
            <p>Interview session not found</p>
            <Button onClick={() => router.push('/jobs')} variant="outline" className="mt-4">
              Back to Jobs
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6">
      <Link href="/jobs" className="inline-flex items-center text-sm text-muted-foreground hover:text-foreground mb-6">
        <ArrowLeft className="mr-1 h-4 w-4" />
        Back to jobs
      </Link>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Status Card */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <Building className="h-5 w-5" />
                    {sessionData.job_title}
                  </CardTitle>
                  <CardDescription className="flex items-center gap-2 mt-1">
                    <User className="h-4 w-4" />
                    {sessionData.user_name} • {sessionData.user_email}
                  </CardDescription>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant={getStatusColor(sessionData.status)}>
                    {getStatusText(sessionData.status)}
                  </Badge>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={loadSessionStatus}
                    disabled={refreshing}
                  >
                    <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {/* Progress */}
                <div>
                  <div className="flex justify-between text-sm mb-2">
                    <span>Interview Progress</span>
                    <span>{getProgressValue(sessionData.status)}%</span>
                  </div>
                  <Progress value={getProgressValue(sessionData.status)} className="h-2" />
                </div>

                {/* Interview Details */}
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-muted-foreground">Duration:</span>
                    <p className="font-medium">{sessionData.duration} minutes</p>
                  </div>
                  <div>
                    <span className="text-muted-foreground">Questions:</span>
                    <p className="font-medium">{sessionData.question_count} questions</p>
                  </div>
                  <div>
                    <span className="text-muted-foreground">Type:</span>
                    <p className="font-medium">{sessionData.interview_type}</p>
                  </div>
                  <div>
                    <span className="text-muted-foreground">Experience Level:</span>
                    <p className="font-medium">{sessionData.experience_level}</p>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex gap-2">
                  {sessionData.status === 'ready' && (
                    <Button onClick={openVoiceAgent} className="flex-1">
                      <ExternalLink className="mr-2 h-4 w-4" />
                      Start Interview
                    </Button>
                  )}
                  {sessionData.status === 'in_progress' && (
                    <Button onClick={openVoiceAgent} variant="outline" className="flex-1">
                      <ExternalLink className="mr-2 h-4 w-4" />
                      Continue Interview
                    </Button>
                  )}
                  {sessionData.status === 'completed' && feedbackData && (
                    <Button onClick={() => router.push(`/interview/${sessionData.main_interview_id}/feedback`)} className="flex-1">
                      <MessageSquare className="mr-2 h-4 w-4" />
                      View Feedback
                    </Button>
                  )}
                </div>

                {/* Timestamps */}
                <div className="text-xs text-muted-foreground space-y-1">
                  <div className="flex items-center gap-2">
                    <Clock className="h-3 w-3" />
                    Created: {new Date(sessionData.created_at).toLocaleString()}
                  </div>
                  {sessionData.started_at && (
                    <div className="flex items-center gap-2">
                      <Clock className="h-3 w-3" />
                      Started: {new Date(sessionData.started_at).toLocaleString()}
                    </div>
                  )}
                  {sessionData.completed_at && (
                    <div className="flex items-center gap-2">
                      <CheckCircle className="h-3 w-3" />
                      Completed: {new Date(sessionData.completed_at).toLocaleString()}
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Feedback Card */}
        <div>
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Star className="h-5 w-5" />
                Interview Results
              </CardTitle>
            </CardHeader>
            <CardContent>
              {feedbackData ? (
                <div className="space-y-4">
                  <div className="text-center">
                    <div className="text-3xl font-bold mb-1">
                      {formatScore(feedbackData.overall_score)}
                    </div>
                    <p className="text-sm text-muted-foreground">Overall Score</p>
                  </div>

                  <div className={`text-center font-medium ${getRecommendationColor(feedbackData.recommendation)}`}>
                    {getRecommendationText(feedbackData.recommendation)}
                  </div>

                  {feedbackData.processed_feedback?.feedback && (
                    <div className="space-y-3">
                      <div>
                        <h4 className="font-medium text-sm mb-1">Strengths</h4>
                        <ul className="text-xs text-muted-foreground space-y-1">
                          {feedbackData.processed_feedback.feedback.strengths?.map((strength, index) => (
                            <li key={index}>• {strength}</li>
                          ))}
                        </ul>
                      </div>

                      <div>
                        <h4 className="font-medium text-sm mb-1">Areas for Improvement</h4>
                        <ul className="text-xs text-muted-foreground space-y-1">
                          {feedbackData.processed_feedback.feedback.areas_for_improvement?.map((area, index) => (
                            <li key={index}>• {area}</li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  )}

                  <Button
                    variant="outline"
                    size="sm"
                    className="w-full"
                    onClick={() => router.push(`/interview/${sessionData.main_interview_id}/feedback`)}
                  >
                    View Detailed Feedback
                  </Button>
                </div>
              ) : (
                <div className="text-center text-muted-foreground">
                  <MessageSquare className="h-8 w-8 mx-auto mb-2 opacity-50" />
                  <p className="text-sm">
                    {sessionData.status === 'completed'
                      ? 'Processing feedback...'
                      : 'Complete the interview to see results'
                    }
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
