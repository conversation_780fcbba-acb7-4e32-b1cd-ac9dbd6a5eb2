// Test Supabase connection
const { createClient } = require('@supabase/supabase-js');

// Hardcode the values for testing
const supabaseUrl = 'https://aimmcbxnlckivomgsdnt.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFpbW1jYnhubGNraXZvbWdzZG50Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc1NjkzNDQsImV4cCI6MjA2MzE0NTM0NH0.wemJlkzSpml8KjSmZY5DvVHD1hLSSTw2ij_tJ10bk3c';

console.log('Testing Supabase connection...');
console.log('URL:', supabaseUrl);
console.log('Key exists:', !!supabaseAnonKey);

if (!supabaseUrl || !supabaseAnonKey) {
    console.error('❌ Missing Supabase environment variables');
    console.log('NEXT_PUBLIC_SUPABASE_URL:', supabaseUrl);
    console.log('NEXT_PUBLIC_SUPABASE_ANON_KEY:', supabaseAnonKey ? 'Set' : 'Not set');
    process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function testConnection() {
    try {
        // Test basic connection
        const { data, error } = await supabase
            .from('Interviews')
            .select('count(*)')
            .limit(1);
        
        if (error) {
            console.error('❌ Supabase connection failed:', error.message);
            return false;
        }
        
        console.log('✅ Supabase connection successful');
        return true;
    } catch (err) {
        console.error('❌ Exception testing connection:', err.message);
        return false;
    }
}

testConnection().then(success => {
    if (success) {
        console.log('🎉 All tests passed!');
    } else {
        console.log('💥 Tests failed!');
    }
    process.exit(success ? 0 : 1);
});
