"use client"

import { useState, useEffect } from 'react';
import { useIsClient } from './useHydrationFix';

/**
 * Safe localStorage hook that prevents hydration mismatches
 * @param {string} key - The localStorage key
 * @param {any} initialValue - The initial value if no stored value exists
 * @returns {[any, function]} - [storedValue, setValue]
 */
export function useLocalStorage(key, initialValue) {
  const isClient = useIsClient();
  
  // State to store our value
  const [storedValue, setStoredValue] = useState(initialValue);

  // Return a wrapped version of useState's setter function that persists the new value to localStorage
  const setValue = (value) => {
    try {
      // Allow value to be a function so we have the same API as useState
      const valueToStore = value instanceof Function ? value(storedValue) : value;
      
      // Save state
      setStoredValue(valueToStore);
      
      // Save to local storage only on client
      if (isClient && typeof window !== 'undefined') {
        window.localStorage.setItem(key, JSON.stringify(valueToStore));
      }
    } catch (error) {
      // A more advanced implementation would handle the error case
      console.warn(`Error setting localStorage key "${key}":`, error);
    }
  };

  // Load value from localStorage on client mount
  useEffect(() => {
    if (!isClient) return;
    
    try {
      const item = window.localStorage.getItem(key);
      if (item) {
        setStoredValue(JSON.parse(item));
      }
    } catch (error) {
      console.warn(`Error reading localStorage key "${key}":`, error);
      setStoredValue(initialValue);
    }
  }, [key, initialValue, isClient]);

  return [storedValue, setValue];
}

/**
 * Safe sessionStorage hook that prevents hydration mismatches
 * @param {string} key - The sessionStorage key
 * @param {any} initialValue - The initial value if no stored value exists
 * @returns {[any, function]} - [storedValue, setValue]
 */
export function useSessionStorage(key, initialValue) {
  const isClient = useIsClient();
  
  const [storedValue, setStoredValue] = useState(initialValue);

  const setValue = (value) => {
    try {
      const valueToStore = value instanceof Function ? value(storedValue) : value;
      setStoredValue(valueToStore);
      
      if (isClient && typeof window !== 'undefined') {
        window.sessionStorage.setItem(key, JSON.stringify(valueToStore));
      }
    } catch (error) {
      console.warn(`Error setting sessionStorage key "${key}":`, error);
    }
  };

  useEffect(() => {
    if (!isClient) return;
    
    try {
      const item = window.sessionStorage.getItem(key);
      if (item) {
        setStoredValue(JSON.parse(item));
      }
    } catch (error) {
      console.warn(`Error reading sessionStorage key "${key}":`, error);
      setStoredValue(initialValue);
    }
  }, [key, initialValue, isClient]);

  return [storedValue, setValue];
}

/**
 * Hook to safely access window object
 * @returns {Window | null} - Window object or null if not on client
 */
export function useWindow() {
  const isClient = useIsClient();
  return isClient ? window : null;
}

/**
 * Hook to safely access document object
 * @returns {Document | null} - Document object or null if not on client
 */
export function useDocument() {
  const isClient = useIsClient();
  return isClient ? document : null;
}
