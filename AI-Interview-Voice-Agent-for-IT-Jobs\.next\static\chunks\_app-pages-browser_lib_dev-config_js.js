/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_lib_dev-config_js"],{

/***/ "(app-pages-browser)/./lib/dev-config.js":
/*!***************************!*\
  !*** ./lib/dev-config.js ***!
  \***************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("// Development Configuration\n// This file contains development-specific configurations to improve the development experience\nif (true) {\n    // Suppress specific console warnings in development\n    const originalConsoleError = console.error;\n    const originalConsoleWarn = console.warn;\n    console.error = function() {\n        for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){\n            args[_key] = arguments[_key];\n        }\n        // Suppress hydration mismatch warnings caused by browser extensions\n        const message = args[0];\n        if (typeof message === 'string') {\n            // Suppress hydration warnings\n            if (message.includes('Hydration failed') || message.includes('hydrated but some attributes') || message.includes('cz-shortcut-listen') || message.includes('data-gr-') || message.includes('data-gramm') || message.includes('browser extension')) {\n                return;\n            }\n            // Suppress React DevTools warnings about browser extensions\n            if (message.includes('Warning: Extra attributes from the server') || message.includes('Warning: Prop') && message.includes('did not match')) {\n                return;\n            }\n        }\n        // Call original console.error for other messages\n        originalConsoleError.apply(console, args);\n    };\n    console.warn = function() {\n        for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){\n            args[_key] = arguments[_key];\n        }\n        const message = args[0];\n        if (typeof message === 'string') {\n            // Suppress specific development warnings\n            if (message.includes('useLayoutEffect does nothing on the server') || message.includes('Warning: validateDOMNesting') || message.includes('browser extension')) {\n                return;\n            }\n        }\n        // Call original console.warn for other messages\n        originalConsoleWarn.apply(console, args);\n    };\n    // Add a global error handler for unhandled hydration errors\n    window.addEventListener('error', (event)=>{\n        if (event.message && (event.message.includes('Hydration') || event.message.includes('hydrated but some attributes') || event.message.includes('cz-shortcut-listen'))) {\n            event.preventDefault();\n            event.stopPropagation();\n            return false;\n        }\n    });\n    // Add a global unhandled rejection handler\n    window.addEventListener('unhandledrejection', (event)=>{\n        if (event.reason && event.reason.message && (event.reason.message.includes('Hydration') || event.reason.message.includes('hydrated but some attributes'))) {\n            event.preventDefault();\n            return false;\n        }\n    });\n    console.log('🔧 Development mode: Hydration warnings suppressed for browser extensions');\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/dev-config.js\n"));

/***/ })

}]);