import { NextRequest, NextResponse } from 'next/server'
import {
  INTEGRATION_CONFIG,
  logIntegrationEvent
} from '../../../../../shared/integration-config.js'

// Send feedback to main platform
export async function POST(request: NextRequest) {
  try {
    const feedbackData = await request.json()
    
    console.log('📝 Sending feedback to main platform:', feedbackData)
    
    logIntegrationEvent('info', 'voice_agent', 'sending_feedback',
      'Sending interview feedback to main platform', feedbackData.sessionId, feedbackData)

    // Send feedback to main platform
    const response = await fetch(`${INTEGRATION_CONFIG.MAIN_PLATFORM_URL}/api/integration/feedback`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        [INTEGRATION_CONFIG.API_KEY_HEADER]: process.env.INTEGRATION_API_KEY || 'voice-agent-api-key'
      },
      body: JSON.stringify(feedbackData)
    })

    if (!response.ok) {
      throw new Error(`Failed to send feedback: ${response.status}`)
    }

    const result = await response.json()
    
    logIntegrationEvent('info', 'voice_agent', 'feedback_sent',
      'Successfully sent feedback to main platform', feedbackData.sessionId, result)

    return NextResponse.json({
      success: true,
      message: 'Feedback sent to main platform successfully',
      result
    })

  } catch (error) {
    console.error('❌ Error sending feedback:', error)
    
    logIntegrationEvent('error', 'voice_agent', 'feedback_send_failed',
      'Failed to send feedback to main platform', null, { error: error.message })

    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 })
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Voice agent feedback endpoint is active',
    timestamp: new Date().toISOString()
  })
}
