"use client"
import React, { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { supabase } from '@/services/supabaseClient'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs'
import { ArrowLeft, Loader2, RefreshCw } from 'lucide-react'
import { toast } from 'sonner'
import FeedbackDisplay from '../../../guest/_components/FeedbackDisplay'
import DetailedFeedback from '../../../guest/_components/DetailedFeedback'
import { downloadPDF } from '../../../guest/_components/PDFGenerator'
import Link from 'next/link'

function InterviewFeedbackPage() {
    const { interview_id } = useParams()
    const router = useRouter()
    const [feedback, setFeedback] = useState(null)
    const [interview, setInterview] = useState(null)
    const [loading, setLoading] = useState(true)
    const [error, setError] = useState(null)

    useEffect(() => {
        if (interview_id) {
            fetchFeedback()
        }
    }, [interview_id])

    const fetchFeedback = async () => {
        try {
            setLoading(true)
            setError(null)

            // First, get the interview details
            const { data: interviewData, error: interviewError } = await supabase
                .from('Interviews')
                .select('*')
                .eq('interview_id', interview_id)
                .single()

            if (interviewError) {
                throw new Error('Interview not found')
            }

            setInterview(interviewData)

            // Then, get the feedback
            const { data: feedbackData, error: feedbackError } = await supabase
                .from('interview-feedback')
                .select('*')
                .eq('interview_id', interview_id)
                .order('created_at', { ascending: false })
                .limit(1)
                .single()

            if (feedbackError) {
                throw new Error('Feedback not found. Please complete the interview first.')
            }

            setFeedback(feedbackData.feedback)
        } catch (err) {
            console.error('Error fetching feedback:', err)
            setError(err.message)
            toast.error(err.message)
        } finally {
            setLoading(false)
        }
    }

    const handleDownloadPDF = () => {
        if (feedback && interview) {
            downloadPDF(feedback, {
                jobPosition: interview.jobPosition,
                jobDescription: interview.jobDescription,
                duration: interview.duration,
                type: interview.type
            }, interview.userName || 'Candidate')
            toast.success('PDF download initiated!')
        }
    }

    if (loading) {
        return (
            <div className="min-h-screen flex items-center justify-center bg-gray-50">
                <Card className="w-full max-w-md">
                    <CardContent className="p-8 text-center">
                        <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4" />
                        <h3 className="text-lg font-semibold mb-2">Loading Your Feedback</h3>
                        <p className="text-gray-600">Please wait while we retrieve your interview results...</p>
                    </CardContent>
                </Card>
            </div>
        )
    }

    if (error) {
        return (
            <div className="min-h-screen flex items-center justify-center bg-gray-50">
                <Card className="w-full max-w-md">
                    <CardContent className="p-8 text-center">
                        <div className="text-red-500 mb-4">
                            <svg className="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                            </svg>
                        </div>
                        <h3 className="text-lg font-semibold mb-2 text-red-600">Error Loading Feedback</h3>
                        <p className="text-gray-600 mb-4">{error}</p>
                        <div className="space-y-2">
                            <Button onClick={fetchFeedback} className="w-full">
                                <RefreshCw className="w-4 h-4 mr-2" />
                                Try Again
                            </Button>
                            <Link href="/dashboard">
                                <Button variant="outline" className="w-full">
                                    <ArrowLeft className="w-4 h-4 mr-2" />
                                    Back to Dashboard
                                </Button>
                            </Link>
                        </div>
                    </CardContent>
                </Card>
            </div>
        )
    }

    return (
        <div className="min-h-screen bg-gray-50 py-8">
            <div className="container mx-auto px-4 max-w-6xl">
                {/* Header */}
                <div className="mb-8">
                    <div className="flex items-center gap-4 mb-4">
                        <Link href="/dashboard">
                            <Button variant="outline" size="sm">
                                <ArrowLeft className="w-4 h-4 mr-2" />
                                Back to Dashboard
                            </Button>
                        </Link>
                        <div>
                            <h1 className="text-3xl font-bold text-gray-900">Interview Feedback</h1>
                            <p className="text-gray-600">
                                {interview?.jobPosition} • Completed on {new Date().toLocaleDateString()}
                            </p>
                        </div>
                    </div>
                </div>

                {/* Feedback Content */}
                <Tabs defaultValue="overview" className="space-y-6">
                    <TabsList className="grid w-full grid-cols-2">
                        <TabsTrigger value="overview">Overview</TabsTrigger>
                        <TabsTrigger value="detailed">Detailed Analysis</TabsTrigger>
                    </TabsList>

                    <TabsContent value="overview" className="space-y-6">
                        <FeedbackDisplay
                            feedback={feedback}
                            jobDetails={interview}
                            onDownloadPDF={handleDownloadPDF}
                        />
                    </TabsContent>

                    <TabsContent value="detailed" className="space-y-6">
                        <DetailedFeedback
                            feedback={feedback}
                            jobDetails={interview}
                        />

                        {/* Download button for detailed view */}
                        <div className="flex justify-center">
                            <Button
                                onClick={handleDownloadPDF}
                                size="lg"
                                className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700"
                            >
                                Download Complete Report
                            </Button>
                        </div>
                    </TabsContent>
                </Tabs>

                {/* Action Buttons */}
                <div className="mt-12 flex flex-col sm:flex-row gap-4 justify-center">
                    <Link href="/dashboard/create-interview">
                        <Button variant="outline" size="lg" className="w-full sm:w-auto">
                            Create Another Interview
                        </Button>
                    </Link>
                    <Link href="/jobs">
                        <Button size="lg" className="w-full sm:w-auto">
                            Browse Job Opportunities
                        </Button>
                    </Link>
                </div>
            </div>
        </div>
    )
}

export default InterviewFeedbackPage
