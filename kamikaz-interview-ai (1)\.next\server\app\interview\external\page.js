/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/interview/external/page";
exports.ids = ["app/interview/external/page"];
exports.modules = {

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"4c5ceb8252e9\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcaHBcXERlc2t0b3BcXFZvaWNlLUFnZW50LWZvci1JVGpvYnNcXGthbWlrYXotaW50ZXJ2aWV3LWFpICgxKVxcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjRjNWNlYjgyNTJlOVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/interview/external/page.tsx":
/*!*****************************************!*\
  !*** ./app/interview/external/page.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\interview\\\\external\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\Voice-Agent-for-ITjobs\\kamikaz-interview-ai (1)\\app\\interview\\external\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n\n\n\nconst metadata = {\n    title: \"Kamikaz-Interview-ai\",\n    description: \"AI-powered FAANG interview preparation platform\",\n    generator: 'v0.dev'\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: \"dark\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().className)} w-full min-h-screen`,\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\layout.tsx\",\n            lineNumber: 21,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\layout.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbGF5b3V0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUtNQTtBQUZnQjtBQUlmLE1BQU1DLFdBQXFCO0lBQ2hDQyxPQUFPO0lBQ1BDLGFBQWE7SUFDWEMsV0FBVztBQUNmLEVBQUM7QUFFYyxTQUFTQyxXQUFXLEVBQ2pDQyxRQUFRLEVBR1Q7SUFDQyxxQkFDRSw4REFBQ0M7UUFBS0MsTUFBSztRQUFLQyxXQUFVO2tCQUN4Qiw0RUFBQ0M7WUFBS0QsV0FBVyxHQUFHVCwySkFBZSxDQUFDLG9CQUFvQixDQUFDO3NCQUFHTTs7Ozs7Ozs7Ozs7QUFHbEUiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcaHBcXERlc2t0b3BcXFZvaWNlLUFnZW50LWZvci1JVGpvYnNcXGthbWlrYXotaW50ZXJ2aWV3LWFpICgxKVxcYXBwXFxsYXlvdXQudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0eXBlIFJlYWN0IGZyb20gXCJyZWFjdFwiXG5pbXBvcnQgdHlwZSB7IE1ldGFkYXRhIH0gZnJvbSBcIm5leHRcIlxuaW1wb3J0IHsgSW50ZXIgfSBmcm9tIFwibmV4dC9mb250L2dvb2dsZVwiXG5pbXBvcnQgXCIuL2dsb2JhbHMuY3NzXCJcblxuY29uc3QgaW50ZXIgPSBJbnRlcih7IHN1YnNldHM6IFtcImxhdGluXCJdIH0pXG5cbmV4cG9ydCBjb25zdCBtZXRhZGF0YTogTWV0YWRhdGEgPSB7XG4gIHRpdGxlOiBcIkthbWlrYXotSW50ZXJ2aWV3LWFpXCIsXG4gIGRlc2NyaXB0aW9uOiBcIkFJLXBvd2VyZWQgRkFBTkcgaW50ZXJ2aWV3IHByZXBhcmF0aW9uIHBsYXRmb3JtXCIsXG4gICAgZ2VuZXJhdG9yOiAndjAuZGV2J1xufVxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBSb290TGF5b3V0KHtcbiAgY2hpbGRyZW4sXG59OiB7XG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGVcbn0pIHtcbiAgcmV0dXJuIChcbiAgICA8aHRtbCBsYW5nPVwiZW5cIiBjbGFzc05hbWU9XCJkYXJrXCI+XG4gICAgICA8Ym9keSBjbGFzc05hbWU9e2Ake2ludGVyLmNsYXNzTmFtZX0gdy1mdWxsIG1pbi1oLXNjcmVlbmB9PntjaGlsZHJlbn08L2JvZHk+XG4gICAgPC9odG1sPlxuICApXG59XG4iXSwibmFtZXMiOlsiaW50ZXIiLCJtZXRhZGF0YSIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJnZW5lcmF0b3IiLCJSb290TGF5b3V0IiwiY2hpbGRyZW4iLCJodG1sIiwibGFuZyIsImNsYXNzTmFtZSIsImJvZHkiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Finterview%2Fexternal%2Fpage&page=%2Finterview%2Fexternal%2Fpage&appPaths=%2Finterview%2Fexternal%2Fpage&pagePath=private-next-app-dir%2Finterview%2Fexternal%2Fpage.tsx&appDir=C%3A%5CUsers%5Chp%5CDesktop%5CVoice-Agent-for-ITjobs%5Ckamikaz-interview-ai%20(1)%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Chp%5CDesktop%5CVoice-Agent-for-ITjobs%5Ckamikaz-interview-ai%20(1)&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Finterview%2Fexternal%2Fpage&page=%2Finterview%2Fexternal%2Fpage&appPaths=%2Finterview%2Fexternal%2Fpage&pagePath=private-next-app-dir%2Finterview%2Fexternal%2Fpage.tsx&appDir=C%3A%5CUsers%5Chp%5CDesktop%5CVoice-Agent-for-ITjobs%5Ckamikaz-interview-ai%20(1)%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Chp%5CDesktop%5CVoice-Agent-for-ITjobs%5Ckamikaz-interview-ai%20(1)&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/interview/external/page.tsx */ \"(rsc)/./app/interview/external/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'interview',\n        {\n        children: [\n        'external',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\interview\\\\external\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\interview\\\\external\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/interview/external/page\",\n        pathname: \"/interview/external\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Finterview%2Fexternal%2Fpage&page=%2Finterview%2Fexternal%2Fpage&appPaths=%2Finterview%2Fexternal%2Fpage&pagePath=private-next-app-dir%2Finterview%2Fexternal%2Fpage.tsx&appDir=C%3A%5CUsers%5Chp%5CDesktop%5CVoice-Agent-for-ITjobs%5Ckamikaz-interview-ai%20(1)%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Chp%5CDesktop%5CVoice-Agent-for-ITjobs%5Ckamikaz-interview-ai%20(1)&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5Ckamikaz-interview-ai%20(1)%5C%5Capp%5C%5Cinterview%5C%5Cexternal%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5Ckamikaz-interview-ai%20(1)%5C%5Capp%5C%5Cinterview%5C%5Cexternal%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/interview/external/page.tsx */ \"(rsc)/./app/interview/external/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2hwJTVDJTVDRGVza3RvcCU1QyU1Q1ZvaWNlLUFnZW50LWZvci1JVGpvYnMlNUMlNUNrYW1pa2F6LWludGVydmlldy1haSUyMCgxKSU1QyU1Q2FwcCU1QyU1Q2ludGVydmlldyU1QyU1Q2V4dGVybmFsJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLDhLQUFpSiIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcaHBcXFxcRGVza3RvcFxcXFxWb2ljZS1BZ2VudC1mb3ItSVRqb2JzXFxcXGthbWlrYXotaW50ZXJ2aWV3LWFpICgxKVxcXFxhcHBcXFxcaW50ZXJ2aWV3XFxcXGV4dGVybmFsXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5Ckamikaz-interview-ai%20(1)%5C%5Capp%5C%5Cinterview%5C%5Cexternal%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5Ckamikaz-interview-ai%20(1)%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5Ckamikaz-interview-ai%20(1)%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5Ckamikaz-interview-ai%20(1)%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5Ckamikaz-interview-ai%20(1)%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5Ckamikaz-interview-ai%20(1)%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5Ckamikaz-interview-ai%20(1)%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5Ckamikaz-interview-ai%20(1)%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5Ckamikaz-interview-ai%20(1)%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5Ckamikaz-interview-ai%20(1)%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5Ckamikaz-interview-ai%20(1)%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5Ckamikaz-interview-ai%20(1)%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5Ckamikaz-interview-ai%20(1)%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5Ckamikaz-interview-ai%20(1)%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5Ckamikaz-interview-ai%20(1)%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5Ckamikaz-interview-ai%20(1)%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5Ckamikaz-interview-ai%20(1)%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5Ckamikaz-interview-ai%20(1)%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5Ckamikaz-interview-ai%20(1)%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5Ckamikaz-interview-ai%20(1)%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5Ckamikaz-interview-ai%20(1)%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5Ckamikaz-interview-ai%20(1)%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5Ckamikaz-interview-ai%20(1)%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5Ckamikaz-interview-ai%20(1)%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5Ckamikaz-interview-ai%20(1)%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5Ckamikaz-interview-ai%20(1)%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5Ckamikaz-interview-ai%20(1)%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5Ckamikaz-interview-ai%20(1)%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5Ckamikaz-interview-ai%20(1)%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/../shared/integration-config.js":
/*!***************************************!*\
  !*** ../shared/integration-config.js ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   API_ENDPOINTS: () => (/* binding */ API_ENDPOINTS),\n/* harmony export */   CONVERSATION_OBJECT_SCHEMA: () => (/* binding */ CONVERSATION_OBJECT_SCHEMA),\n/* harmony export */   ERROR_CODES: () => (/* binding */ ERROR_CODES),\n/* harmony export */   ERROR_MESSAGES: () => (/* binding */ ERROR_MESSAGES),\n/* harmony export */   FEEDBACK_SCHEMA: () => (/* binding */ FEEDBACK_SCHEMA),\n/* harmony export */   INTEGRATION_CONFIG: () => (/* binding */ INTEGRATION_CONFIG),\n/* harmony export */   INTERVIEW_SESSION_SCHEMA: () => (/* binding */ INTERVIEW_SESSION_SCHEMA),\n/* harmony export */   QUESTION_SCHEMA: () => (/* binding */ QUESTION_SCHEMA),\n/* harmony export */   createApiResponse: () => (/* binding */ createApiResponse),\n/* harmony export */   generateApiKeyHash: () => (/* binding */ generateApiKeyHash),\n/* harmony export */   logIntegrationEvent: () => (/* binding */ logIntegrationEvent),\n/* harmony export */   validateSchema: () => (/* binding */ validateSchema)\n/* harmony export */ });\n// Integration Configuration for Voice Agent Interview System\n// This file defines the API contracts, endpoints, and configuration\n// for communication between the two systems\n// =====================================================\n// SYSTEM CONFIGURATION\n// =====================================================\nconst INTEGRATION_CONFIG = {\n    // System URLs (update these based on deployment)\n    MAIN_PLATFORM_URL: process.env.MAIN_PLATFORM_URL || 'http://localhost:3000',\n    VOICE_AGENT_URL: process.env.VOICE_AGENT_URL || 'http://localhost:3001',\n    // API versions\n    API_VERSION: 'v1',\n    INTEGRATION_VERSION: '1.0',\n    // Authentication\n    API_KEY_HEADER: 'X-Integration-API-Key',\n    SHARED_SECRET: process.env.INTEGRATION_SHARED_SECRET || 'your-secure-shared-secret',\n    // Timeouts and retries\n    REQUEST_TIMEOUT: 30000,\n    MAX_RETRIES: 3,\n    RETRY_DELAY: 2000,\n    // Interview configuration\n    DEFAULT_INTERVIEW_DURATION: 30,\n    DEFAULT_QUESTION_COUNT: 10,\n    MAX_QUESTION_COUNT: 20,\n    // Logging\n    LOG_LEVEL: process.env.LOG_LEVEL || 'info',\n    ENABLE_DEBUG_LOGS: \"development\" === 'development'\n};\n// =====================================================\n// API ENDPOINTS\n// =====================================================\nconst API_ENDPOINTS = {\n    // Main Platform endpoints (AI-Interview-Voice-Agent-for-IT-Jobs)\n    MAIN_PLATFORM: {\n        // Interview management\n        CREATE_INTERVIEW_SESSION: '/api/integration/interview/create',\n        GET_INTERVIEW_QUESTIONS: '/api/integration/interview/questions',\n        UPDATE_INTERVIEW_STATUS: '/api/integration/interview/status',\n        RECEIVE_FEEDBACK: '/api/integration/interview/feedback',\n        // Health check\n        HEALTH_CHECK: '/api/integration/health',\n        // Authentication\n        VALIDATE_API_KEY: '/api/integration/auth/validate'\n    },\n    // Voice Agent endpoints (kamikaz-interview-ai)\n    VOICE_AGENT: {\n        // Interview management\n        START_INTERVIEW: '/api/integration/interview/start',\n        GET_INTERVIEW_STATUS: '/api/integration/interview/status',\n        SUBMIT_FEEDBACK: '/api/integration/interview/feedback',\n        // Health check\n        HEALTH_CHECK: '/api/integration/health',\n        // Authentication\n        VALIDATE_API_KEY: '/api/integration/auth/validate'\n    }\n};\n// =====================================================\n// DATA SCHEMAS AND TYPES\n// =====================================================\n// Interview Session Creation Request (Main Platform -> Voice Agent)\nconst INTERVIEW_SESSION_SCHEMA = {\n    // Required fields\n    sessionId: 'string',\n    userEmail: 'string',\n    userName: 'string',\n    // Job context\n    jobTitle: 'string',\n    jobDescription: 'string',\n    companyName: 'string',\n    // Interview configuration\n    interviewType: 'string',\n    duration: 'number',\n    experienceLevel: 'string',\n    requiredSkills: 'string',\n    companyCriteria: 'string',\n    // Questions\n    questions: 'array',\n    questionCount: 'number',\n    // Callback configuration\n    callbackUrl: 'string',\n    webhookSecret: 'string' // For webhook verification\n};\n// Question Object Schema\nconst QUESTION_SCHEMA = {\n    id: 'string',\n    question: 'string',\n    type: 'string',\n    difficulty: 'string',\n    expectedDuration: 'number',\n    followUpAllowed: 'boolean',\n    metadata: 'object' // Additional question metadata\n};\n// Interview Feedback Schema (Voice Agent -> Main Platform)\nconst FEEDBACK_SCHEMA = {\n    // Session identification\n    sessionId: 'string',\n    voiceAgentInterviewId: 'string',\n    // Candidate information\n    userEmail: 'string',\n    userName: 'string',\n    // Interview results\n    overallScore: 'number',\n    recommendation: 'string',\n    // Detailed assessment\n    technicalScore: 'number',\n    communicationScore: 'number',\n    problemSolvingScore: 'number',\n    // Qualitative feedback\n    strengths: 'array',\n    areasForImprovement: 'array',\n    overallAssessment: 'string',\n    // Interview data\n    duration: 'number',\n    questionsAsked: 'number',\n    questionsAnswered: 'number',\n    // Conversation data\n    conversationTranscript: 'array',\n    keyMoments: 'array',\n    // Metadata\n    completedAt: 'string',\n    interviewQuality: 'string',\n    technicalIssues: 'array' // Any technical issues encountered\n};\n// Conversation Object Schema\nconst CONVERSATION_OBJECT_SCHEMA = {\n    speaker: 'string',\n    message: 'string',\n    timestamp: 'string',\n    questionId: 'string',\n    confidence: 'number',\n    duration: 'number' // Duration of speech in seconds\n};\n// =====================================================\n// ERROR CODES AND MESSAGES\n// =====================================================\nconst ERROR_CODES = {\n    // Authentication errors\n    INVALID_API_KEY: 'INVALID_API_KEY',\n    EXPIRED_API_KEY: 'EXPIRED_API_KEY',\n    RATE_LIMIT_EXCEEDED: 'RATE_LIMIT_EXCEEDED',\n    // Validation errors\n    INVALID_REQUEST_FORMAT: 'INVALID_REQUEST_FORMAT',\n    MISSING_REQUIRED_FIELDS: 'MISSING_REQUIRED_FIELDS',\n    INVALID_FIELD_VALUE: 'INVALID_FIELD_VALUE',\n    // Session errors\n    SESSION_NOT_FOUND: 'SESSION_NOT_FOUND',\n    SESSION_ALREADY_EXISTS: 'SESSION_ALREADY_EXISTS',\n    SESSION_EXPIRED: 'SESSION_EXPIRED',\n    INVALID_SESSION_STATUS: 'INVALID_SESSION_STATUS',\n    // Interview errors\n    INTERVIEW_CREATION_FAILED: 'INTERVIEW_CREATION_FAILED',\n    INTERVIEW_START_FAILED: 'INTERVIEW_START_FAILED',\n    QUESTIONS_NOT_FOUND: 'QUESTIONS_NOT_FOUND',\n    FEEDBACK_SUBMISSION_FAILED: 'FEEDBACK_SUBMISSION_FAILED',\n    // System errors\n    INTERNAL_SERVER_ERROR: 'INTERNAL_SERVER_ERROR',\n    SERVICE_UNAVAILABLE: 'SERVICE_UNAVAILABLE',\n    TIMEOUT_ERROR: 'TIMEOUT_ERROR',\n    DATABASE_ERROR: 'DATABASE_ERROR'\n};\nconst ERROR_MESSAGES = {\n    [ERROR_CODES.INVALID_API_KEY]: 'Invalid or missing API key',\n    [ERROR_CODES.EXPIRED_API_KEY]: 'API key has expired',\n    [ERROR_CODES.RATE_LIMIT_EXCEEDED]: 'Rate limit exceeded, please try again later',\n    [ERROR_CODES.INVALID_REQUEST_FORMAT]: 'Request format is invalid',\n    [ERROR_CODES.MISSING_REQUIRED_FIELDS]: 'Required fields are missing',\n    [ERROR_CODES.INVALID_FIELD_VALUE]: 'One or more field values are invalid',\n    [ERROR_CODES.SESSION_NOT_FOUND]: 'Interview session not found',\n    [ERROR_CODES.SESSION_ALREADY_EXISTS]: 'Interview session already exists',\n    [ERROR_CODES.SESSION_EXPIRED]: 'Interview session has expired',\n    [ERROR_CODES.INVALID_SESSION_STATUS]: 'Invalid session status for this operation',\n    [ERROR_CODES.INTERVIEW_CREATION_FAILED]: 'Failed to create interview session',\n    [ERROR_CODES.INTERVIEW_START_FAILED]: 'Failed to start interview',\n    [ERROR_CODES.QUESTIONS_NOT_FOUND]: 'Interview questions not found',\n    [ERROR_CODES.FEEDBACK_SUBMISSION_FAILED]: 'Failed to submit interview feedback',\n    [ERROR_CODES.INTERNAL_SERVER_ERROR]: 'Internal server error occurred',\n    [ERROR_CODES.SERVICE_UNAVAILABLE]: 'Service is temporarily unavailable',\n    [ERROR_CODES.TIMEOUT_ERROR]: 'Request timeout',\n    [ERROR_CODES.DATABASE_ERROR]: 'Database operation failed'\n};\n// =====================================================\n// UTILITY FUNCTIONS\n// =====================================================\n// Generate API key hash\nfunction generateApiKeyHash(apiKey) {\n    const crypto = __webpack_require__(/*! crypto */ \"crypto\");\n    return crypto.createHash('sha256').update(apiKey).digest('hex');\n}\n// Validate request schema\nfunction validateSchema(data, schema) {\n    const errors = [];\n    for (const [field, type] of Object.entries(schema)){\n        if (!(field in data)) {\n            errors.push(`Missing required field: ${field}`);\n            continue;\n        }\n        const value = data[field];\n        const actualType = Array.isArray(value) ? 'array' : typeof value;\n        if (actualType !== type) {\n            errors.push(`Field ${field} should be ${type}, got ${actualType}`);\n        }\n    }\n    return errors;\n}\n// Create standardized API response\nfunction createApiResponse(success, data = null, error = null, code = null) {\n    return {\n        success,\n        timestamp: new Date().toISOString(),\n        data,\n        error: error ? {\n            code: code || ERROR_CODES.INTERNAL_SERVER_ERROR,\n            message: error,\n            details: ERROR_MESSAGES[code] || error\n        } : null\n    };\n}\n// Log integration event\nfunction logIntegrationEvent(level, component, action, message, sessionId = null, data = null) {\n    const logEntry = {\n        level,\n        component,\n        action,\n        message,\n        sessionId,\n        data,\n        timestamp: new Date().toISOString()\n    };\n    if (INTEGRATION_CONFIG.ENABLE_DEBUG_LOGS || level !== 'debug') {\n        console.log(`[INTEGRATION] [${level.toUpperCase()}] ${component}:${action} - ${message}`, data || '');\n    }\n    return logEntry;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../shared/integration-config.js\n");

/***/ }),

/***/ "(ssr)/./app/interview/external/page.tsx":
/*!*****************************************!*\
  !*** ./app/interview/external/page.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ExternalInterviewPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(ssr)/./components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building_CheckCircle_Clock_Mic_MicOff_User_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building,CheckCircle,Clock,Mic,MicOff,User,Volume2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building_CheckCircle_Clock_Mic_MicOff_User_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building,CheckCircle,Clock,Mic,MicOff,User,Volume2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building_CheckCircle_Clock_Mic_MicOff_User_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building,CheckCircle,Clock,Mic,MicOff,User,Volume2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building_CheckCircle_Clock_Mic_MicOff_User_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building,CheckCircle,Clock,Mic,MicOff,User,Volume2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building_CheckCircle_Clock_Mic_MicOff_User_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building,CheckCircle,Clock,Mic,MicOff,User,Volume2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mic.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building_CheckCircle_Clock_Mic_MicOff_User_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building,CheckCircle,Clock,Mic,MicOff,User,Volume2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mic-off.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building_CheckCircle_Clock_Mic_MicOff_User_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building,CheckCircle,Clock,Mic,MicOff,User,Volume2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/volume-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building_CheckCircle_Clock_Mic_MicOff_User_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building,CheckCircle,Clock,Mic,MicOff,User,Volume2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _lib_gemini_voice__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/gemini-voice */ \"(ssr)/./lib/gemini-voice.ts\");\n/* harmony import */ var _shared_integration_config_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../../../shared/integration-config.js */ \"(ssr)/../shared/integration-config.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\nfunction ExternalInterviewPage() {\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const sessionId = searchParams.get('sessionId');\n    const encodedData = searchParams.get('data');\n    // Session and interview state\n    const [sessionData, setSessionData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Interview state\n    const [isInterviewStarted, setIsInterviewStarted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isInterviewCompleted, setIsInterviewCompleted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentQuestionIndex, setCurrentQuestionIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [currentQuestion, setCurrentQuestion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isListening, setIsListening] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isSpeaking, setIsSpeaking] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [transcript, setTranscript] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [conversationHistory, setConversationHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Voice service\n    const voiceServiceRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [microphonePermission, setMicrophonePermission] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('prompt');\n    // Interview metrics\n    const [startTime, setStartTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [elapsedTime, setElapsedTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // Load session data on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ExternalInterviewPage.useEffect\": ()=>{\n            if (!sessionId && !encodedData) {\n                setError('No session ID or data provided');\n                setIsLoading(false);\n                return;\n            }\n            if (encodedData) {\n                // Handle direct data passing from main platform\n                loadDirectSessionData();\n            } else if (sessionId) {\n                // Handle traditional sessionId approach\n                loadSessionData();\n            }\n        }\n    }[\"ExternalInterviewPage.useEffect\"], [\n        sessionId,\n        encodedData\n    ]);\n    // Timer for elapsed time\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ExternalInterviewPage.useEffect\": ()=>{\n            let interval = null;\n            if (isInterviewStarted && startTime && !isInterviewCompleted) {\n                interval = setInterval({\n                    \"ExternalInterviewPage.useEffect\": ()=>{\n                        setElapsedTime(Math.floor((Date.now() - startTime.getTime()) / 1000));\n                    }\n                }[\"ExternalInterviewPage.useEffect\"], 1000);\n            }\n            return ({\n                \"ExternalInterviewPage.useEffect\": ()=>{\n                    if (interval) clearInterval(interval);\n                }\n            })[\"ExternalInterviewPage.useEffect\"];\n        }\n    }[\"ExternalInterviewPage.useEffect\"], [\n        isInterviewStarted,\n        startTime,\n        isInterviewCompleted\n    ]);\n    const loadDirectSessionData = async ()=>{\n        try {\n            setIsLoading(true);\n            if (!encodedData) {\n                throw new Error('No encoded data provided');\n            }\n            (0,_shared_integration_config_js__WEBPACK_IMPORTED_MODULE_7__.logIntegrationEvent)('info', 'voice_agent', 'loading_direct_session_data', 'Loading interview session from encoded data', 'direct_data');\n            // Decode the data passed from main platform\n            // First decode from base64, then decode URI component, then parse JSON\n            const decodedString = decodeURIComponent(atob(encodedData));\n            const interviewData = JSON.parse(decodedString);\n            console.log('📥 Received interview data:', interviewData);\n            // Transform the data to match our ExternalSessionData interface\n            const transformedSessionData = {\n                sessionId: interviewData.sessionId,\n                mainPlatformSession: true,\n                userEmail: interviewData.userEmail,\n                userName: interviewData.userName,\n                jobTitle: interviewData.jobTitle,\n                jobDescription: interviewData.jobDescription,\n                companyName: interviewData.companyName,\n                interviewType: interviewData.interviewType,\n                duration: interviewData.duration,\n                experienceLevel: interviewData.experienceLevel,\n                requiredSkills: interviewData.requiredSkills,\n                companyCriteria: interviewData.companyCriteria,\n                questions: interviewData.questions || [],\n                questionCount: interviewData.questionCount,\n                callbackUrl: `${_shared_integration_config_js__WEBPACK_IMPORTED_MODULE_7__.INTEGRATION_CONFIG.MAIN_PLATFORM_URL}/api/integration/feedback`,\n                webhookSecret: 'webhook-secret',\n                voiceAgentUserId: 'external_user',\n                voiceAgentInterviewId: interviewData.sessionId,\n                createdAt: new Date().toISOString()\n            };\n            setSessionData(transformedSessionData);\n            setIsLoading(false);\n            (0,_shared_integration_config_js__WEBPACK_IMPORTED_MODULE_7__.logIntegrationEvent)('info', 'voice_agent', 'session_data_loaded', 'Successfully loaded session data from encoded data', transformedSessionData.sessionId);\n        } catch (error) {\n            console.error('❌ Failed to load direct session data:', error);\n            setError(`Failed to load interview data: ${error.message}`);\n            setIsLoading(false);\n            (0,_shared_integration_config_js__WEBPACK_IMPORTED_MODULE_7__.logIntegrationEvent)('error', 'voice_agent', 'session_loading_failed', 'Failed to load session data from encoded data', 'direct_data', {\n                error: error.message\n            });\n        }\n    };\n    const loadSessionData = async ()=>{\n        try {\n            setIsLoading(true);\n            (0,_shared_integration_config_js__WEBPACK_IMPORTED_MODULE_7__.logIntegrationEvent)('info', 'voice_agent', 'loading_external_session', 'Loading external interview session', sessionId);\n            // In a real implementation, you would fetch this from your backend\n            // For now, we'll check if there's session data in localStorage or make an API call\n            // Try to get session data from the main platform\n            const response = await fetch(`${_shared_integration_config_js__WEBPACK_IMPORTED_MODULE_7__.INTEGRATION_CONFIG.MAIN_PLATFORM_URL}/api/integration/interview/create?sessionId=${sessionId}`, {\n                headers: {\n                    [_shared_integration_config_js__WEBPACK_IMPORTED_MODULE_7__.INTEGRATION_CONFIG.API_KEY_HEADER]: process.env.NEXT_PUBLIC_INTEGRATION_API_KEY || 'voice-agent-api-key'\n                }\n            });\n            if (!response.ok) {\n                throw new Error(`Failed to load session: ${response.status}`);\n            }\n            const result = await response.json();\n            if (!result.success) {\n                throw new Error(result.error?.message || 'Failed to load session data');\n            }\n            // For demo purposes, create mock session data\n            // In production, this would come from the API response\n            const mockSessionData = {\n                sessionId: sessionId,\n                mainPlatformSession: true,\n                userEmail: '<EMAIL>',\n                userName: 'Interview Candidate',\n                jobTitle: 'Software Engineer',\n                jobDescription: 'Full-stack development role',\n                companyName: 'Tech Company',\n                interviewType: 'job_application',\n                duration: 30,\n                experienceLevel: 'Mid-Level',\n                requiredSkills: 'JavaScript, React, Node.js',\n                companyCriteria: 'Strong problem-solving skills',\n                questions: result.data?.questions || [\n                    {\n                        id: 'q1',\n                        question: 'Tell me about yourself and your experience with software development.',\n                        type: 'behavioral',\n                        difficulty: 'easy',\n                        expectedDuration: 120,\n                        followUpAllowed: true,\n                        metadata: {}\n                    },\n                    {\n                        id: 'q2',\n                        question: 'Describe a challenging technical problem you solved recently.',\n                        type: 'technical',\n                        difficulty: 'medium',\n                        expectedDuration: 180,\n                        followUpAllowed: true,\n                        metadata: {}\n                    }\n                ],\n                questionCount: 2,\n                callbackUrl: `${_shared_integration_config_js__WEBPACK_IMPORTED_MODULE_7__.INTEGRATION_CONFIG.MAIN_PLATFORM_URL}/api/integration/interview/feedback`,\n                webhookSecret: 'webhook-secret',\n                voiceAgentUserId: 'user-123',\n                voiceAgentInterviewId: 'interview-456',\n                createdAt: new Date().toISOString()\n            };\n            setSessionData(mockSessionData);\n            (0,_shared_integration_config_js__WEBPACK_IMPORTED_MODULE_7__.logIntegrationEvent)('info', 'voice_agent', 'external_session_loaded', 'External interview session loaded successfully', sessionId, {\n                jobTitle: mockSessionData.jobTitle,\n                questionCount: mockSessionData.questions.length\n            });\n        } catch (err) {\n            const errorMessage = err instanceof Error ? err.message : 'Failed to load session';\n            setError(errorMessage);\n            (0,_shared_integration_config_js__WEBPACK_IMPORTED_MODULE_7__.logIntegrationEvent)('error', 'voice_agent', 'session_load_failed', 'Failed to load external session', sessionId, {\n                error: errorMessage\n            });\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const checkMicrophonePermission = async ()=>{\n        try {\n            const stream = await navigator.mediaDevices.getUserMedia({\n                audio: true\n            });\n            stream.getTracks().forEach((track)=>track.stop());\n            setMicrophonePermission('granted');\n            return true;\n        } catch (error) {\n            setMicrophonePermission('denied');\n            return false;\n        }\n    };\n    const initializeVoiceService = async ()=>{\n        try {\n            voiceServiceRef.current = new _lib_gemini_voice__WEBPACK_IMPORTED_MODULE_6__.GeminiVoiceService();\n            voiceServiceRef.current.enableVoiceActivityDetection(true);\n            voiceServiceRef.current.setInterruptCallback(()=>{\n                setIsSpeaking(false);\n            });\n            return true;\n        } catch (error) {\n            console.error('Failed to initialize voice service:', error);\n            return false;\n        }\n    };\n    const startInterview = async ()=>{\n        if (!sessionData) return;\n        try {\n            const hasPermission = await checkMicrophonePermission();\n            if (!hasPermission) {\n                setError('Microphone permission is required for the interview');\n                return;\n            }\n            const voiceInitialized = await initializeVoiceService();\n            if (!voiceInitialized) {\n                setError('Failed to initialize voice service');\n                return;\n            }\n            setIsInterviewStarted(true);\n            setStartTime(new Date());\n            (0,_shared_integration_config_js__WEBPACK_IMPORTED_MODULE_7__.logIntegrationEvent)('info', 'voice_agent', 'external_interview_started', 'External interview started', sessionId, {\n                userEmail: sessionData.userEmail,\n                jobTitle: sessionData.jobTitle\n            });\n            // Start with welcome message\n            const welcomeMessage = `Hello ${sessionData.userName}, welcome to your interview for the ${sessionData.jobTitle} position at ${sessionData.companyName}. I'm your AI interviewer, and I'll be asking you ${sessionData.questions.length} questions today. Are you ready to begin?`;\n            await voiceServiceRef.current?.speak(welcomeMessage);\n            setConversationHistory([\n                {\n                    speaker: 'ai',\n                    message: welcomeMessage,\n                    timestamp: new Date()\n                }\n            ]);\n            // Start listening for response\n            setTimeout(()=>{\n                startListening();\n            }, 1000);\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : 'Failed to start interview';\n            setError(errorMessage);\n            (0,_shared_integration_config_js__WEBPACK_IMPORTED_MODULE_7__.logIntegrationEvent)('error', 'voice_agent', 'interview_start_failed', 'Failed to start external interview', sessionId, {\n                error: errorMessage\n            });\n        }\n    };\n    const startListening = async ()=>{\n        if (!voiceServiceRef.current || isListening) return;\n        try {\n            setIsListening(true);\n            const response = await voiceServiceRef.current.startListening();\n            setTranscript(response);\n            setIsListening(false);\n            if (response.trim()) {\n                await processUserResponse(response);\n            }\n        } catch (error) {\n            console.error('Error listening:', error);\n            setIsListening(false);\n        }\n    };\n    const processUserResponse = async (response)=>{\n        if (!sessionData || !voiceServiceRef.current) return;\n        try {\n            setIsSpeaking(true);\n            // Add user response to conversation\n            const userMessage = {\n                speaker: 'user',\n                message: response,\n                timestamp: new Date(),\n                questionId: sessionData.questions[currentQuestionIndex]?.id\n            };\n            setConversationHistory((prev)=>[\n                    ...prev,\n                    userMessage\n                ]);\n            // Check if we should ask the next question or end the interview\n            if (currentQuestionIndex < sessionData.questions.length - 1) {\n                // Ask next question\n                const nextQuestionIndex = currentQuestionIndex + 1;\n                const nextQuestion = sessionData.questions[nextQuestionIndex];\n                setCurrentQuestionIndex(nextQuestionIndex);\n                setCurrentQuestion(nextQuestion.question);\n                await voiceServiceRef.current.speak(nextQuestion.question);\n                const aiMessage = {\n                    speaker: 'ai',\n                    message: nextQuestion.question,\n                    timestamp: new Date(),\n                    questionId: nextQuestion.id\n                };\n                setConversationHistory((prev)=>[\n                        ...prev,\n                        aiMessage\n                    ]);\n                setIsSpeaking(false);\n                // Continue listening\n                setTimeout(()=>{\n                    startListening();\n                }, 1000);\n            } else {\n                // End interview\n                await endInterview();\n            }\n        } catch (error) {\n            console.error('Error processing response:', error);\n            setIsSpeaking(false);\n        }\n    };\n    const endInterview = async ()=>{\n        if (!sessionData) return;\n        try {\n            setIsInterviewCompleted(true);\n            const endMessage = \"Thank you for completing the interview. Your responses have been recorded and will be reviewed. You can now close this window.\";\n            if (voiceServiceRef.current) {\n                await voiceServiceRef.current.speak(endMessage);\n            }\n            setConversationHistory((prev)=>[\n                    ...prev,\n                    {\n                        speaker: 'ai',\n                        message: endMessage,\n                        timestamp: new Date()\n                    }\n                ]);\n            // Submit feedback to main platform\n            await submitFeedback();\n            (0,_shared_integration_config_js__WEBPACK_IMPORTED_MODULE_7__.logIntegrationEvent)('info', 'voice_agent', 'external_interview_completed', 'External interview completed', sessionId, {\n                duration: elapsedTime,\n                questionsAnswered: currentQuestionIndex + 1\n            });\n        } catch (error) {\n            console.error('Error ending interview:', error);\n            (0,_shared_integration_config_js__WEBPACK_IMPORTED_MODULE_7__.logIntegrationEvent)('error', 'voice_agent', 'interview_end_failed', 'Failed to end external interview', sessionId, {\n                error: error.message\n            });\n        }\n    };\n    const submitFeedback = async ()=>{\n        if (!sessionData) return;\n        try {\n            // Calculate basic scores (in a real implementation, this would be more sophisticated)\n            const overallScore = Math.floor(Math.random() * 40) + 60 // 60-100 range\n            ;\n            const questionsAnswered = currentQuestionIndex + 1;\n            const feedbackData = {\n                sessionId: sessionData.sessionId,\n                voiceAgentInterviewId: sessionData.voiceAgentInterviewId,\n                userEmail: sessionData.userEmail,\n                userName: sessionData.userName,\n                overallScore,\n                technicalScore: overallScore + Math.floor(Math.random() * 10) - 5,\n                communicationScore: overallScore + Math.floor(Math.random() * 10) - 5,\n                problemSolvingScore: overallScore + Math.floor(Math.random() * 10) - 5,\n                recommendation: overallScore >= 80 ? 'hire' : overallScore >= 65 ? 'maybe' : 'reject',\n                strengths: [\n                    'Good communication',\n                    'Relevant experience'\n                ],\n                areasForImprovement: [\n                    'Could provide more specific examples'\n                ],\n                overallAssessment: 'Candidate showed good understanding of the role requirements.',\n                duration: Math.floor(elapsedTime / 60),\n                questionsAsked: sessionData.questions.length,\n                questionsAnswered,\n                conversationTranscript: conversationHistory,\n                completedAt: new Date().toISOString(),\n                interviewQuality: 'good',\n                callbackUrl: sessionData.callbackUrl,\n                webhookSecret: sessionData.webhookSecret\n            };\n            // Submit to voice agent's feedback endpoint\n            const response = await fetch('/api/integration/interview/feedback', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json',\n                    [_shared_integration_config_js__WEBPACK_IMPORTED_MODULE_7__.INTEGRATION_CONFIG.API_KEY_HEADER]: process.env.NEXT_PUBLIC_INTEGRATION_API_KEY || 'voice-agent-api-key'\n                },\n                body: JSON.stringify(feedbackData)\n            });\n            if (!response.ok) {\n                throw new Error(`Failed to submit feedback: ${response.status}`);\n            }\n            (0,_shared_integration_config_js__WEBPACK_IMPORTED_MODULE_7__.logIntegrationEvent)('info', 'voice_agent', 'feedback_submitted', 'Feedback submitted successfully', sessionId, {\n                overallScore,\n                recommendation: feedbackData.recommendation\n            });\n        } catch (error) {\n            (0,_shared_integration_config_js__WEBPACK_IMPORTED_MODULE_7__.logIntegrationEvent)('error', 'voice_agent', 'feedback_submission_failed', 'Failed to submit feedback', sessionId, {\n                error: error.message\n            });\n        }\n    };\n    const formatTime = (seconds)=>{\n        const mins = Math.floor(seconds / 60);\n        const secs = seconds % 60;\n        return `${mins}:${secs.toString().padStart(2, '0')}`;\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                className: \"w-full max-w-md\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                    className: \"p-6 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\interview\\\\external\\\\page.tsx\",\n                            lineNumber: 509,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"Loading interview session...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\interview\\\\external\\\\page.tsx\",\n                            lineNumber: 510,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\interview\\\\external\\\\page.tsx\",\n                    lineNumber: 508,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\interview\\\\external\\\\page.tsx\",\n                lineNumber: 507,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\interview\\\\external\\\\page.tsx\",\n            lineNumber: 506,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-red-50 to-pink-100 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                className: \"w-full max-w-md\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                    className: \"p-6 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_CheckCircle_Clock_Mic_MicOff_User_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            className: \"h-12 w-12 text-red-500 mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\interview\\\\external\\\\page.tsx\",\n                            lineNumber: 522,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold text-red-700 mb-2\",\n                            children: \"Error\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\interview\\\\external\\\\page.tsx\",\n                            lineNumber: 523,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-red-600 mb-4\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\interview\\\\external\\\\page.tsx\",\n                            lineNumber: 524,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            onClick: ()=>router.push('/'),\n                            variant: \"outline\",\n                            children: \"Go Back\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\interview\\\\external\\\\page.tsx\",\n                            lineNumber: 525,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\interview\\\\external\\\\page.tsx\",\n                    lineNumber: 521,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\interview\\\\external\\\\page.tsx\",\n                lineNumber: 520,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\interview\\\\external\\\\page.tsx\",\n            lineNumber: 519,\n            columnNumber: 7\n        }, this);\n    }\n    if (!sessionData) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                className: \"w-full max-w-md\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                    className: \"p-6 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_CheckCircle_Clock_Mic_MicOff_User_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            className: \"h-12 w-12 text-gray-500 mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\interview\\\\external\\\\page.tsx\",\n                            lineNumber: 539,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"No session data available\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\interview\\\\external\\\\page.tsx\",\n                            lineNumber: 540,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\interview\\\\external\\\\page.tsx\",\n                    lineNumber: 538,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\interview\\\\external\\\\page.tsx\",\n                lineNumber: 537,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\interview\\\\external\\\\page.tsx\",\n            lineNumber: 536,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    className: \"mb-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_CheckCircle_Clock_Mic_MicOff_User_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\interview\\\\external\\\\page.tsx\",\n                                                    lineNumber: 556,\n                                                    columnNumber: 19\n                                                }, this),\n                                                sessionData.jobTitle,\n                                                \" Interview\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\interview\\\\external\\\\page.tsx\",\n                                            lineNumber: 555,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-muted-foreground\",\n                                            children: [\n                                                sessionData.companyName,\n                                                \" • \",\n                                                sessionData.experienceLevel,\n                                                \" Level\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\interview\\\\external\\\\page.tsx\",\n                                            lineNumber: 559,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\interview\\\\external\\\\page.tsx\",\n                                    lineNumber: 554,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-right\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2 text-sm text-muted-foreground\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_CheckCircle_Clock_Mic_MicOff_User_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\interview\\\\external\\\\page.tsx\",\n                                                    lineNumber: 565,\n                                                    columnNumber: 19\n                                                }, this),\n                                                sessionData.userName\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\interview\\\\external\\\\page.tsx\",\n                                            lineNumber: 564,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2 text-sm text-muted-foreground\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_CheckCircle_Clock_Mic_MicOff_User_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\interview\\\\external\\\\page.tsx\",\n                                                    lineNumber: 569,\n                                                    columnNumber: 19\n                                                }, this),\n                                                isInterviewStarted ? formatTime(elapsedTime) : `${sessionData.duration} min`\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\interview\\\\external\\\\page.tsx\",\n                                            lineNumber: 568,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\interview\\\\external\\\\page.tsx\",\n                                    lineNumber: 563,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\interview\\\\external\\\\page.tsx\",\n                            lineNumber: 553,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\interview\\\\external\\\\page.tsx\",\n                        lineNumber: 552,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\interview\\\\external\\\\page.tsx\",\n                    lineNumber: 551,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    className: \"mb-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        className: \"p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                variant: isInterviewCompleted ? \"default\" : isInterviewStarted ? \"secondary\" : \"outline\",\n                                                children: isInterviewCompleted ? \"Completed\" : isInterviewStarted ? \"In Progress\" : \"Ready to Start\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\interview\\\\external\\\\page.tsx\",\n                                                lineNumber: 582,\n                                                columnNumber: 17\n                                            }, this),\n                                            isInterviewStarted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-muted-foreground\",\n                                                children: [\n                                                    \"Question \",\n                                                    currentQuestionIndex + 1,\n                                                    \" of \",\n                                                    sessionData.questions.length\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\interview\\\\external\\\\page.tsx\",\n                                                lineNumber: 586,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\interview\\\\external\\\\page.tsx\",\n                                        lineNumber: 581,\n                                        columnNumber: 15\n                                    }, this),\n                                    isInterviewStarted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                variant: isListening ? \"default\" : \"outline\",\n                                                size: \"sm\",\n                                                onClick: startListening,\n                                                disabled: isSpeaking || isInterviewCompleted,\n                                                children: [\n                                                    isListening ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_CheckCircle_Clock_Mic_MicOff_User_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\interview\\\\external\\\\page.tsx\",\n                                                        lineNumber: 600,\n                                                        columnNumber: 36\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_CheckCircle_Clock_Mic_MicOff_User_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\interview\\\\external\\\\page.tsx\",\n                                                        lineNumber: 600,\n                                                        columnNumber: 66\n                                                    }, this),\n                                                    isListening ? \"Listening...\" : \"Start Speaking\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\interview\\\\external\\\\page.tsx\",\n                                                lineNumber: 594,\n                                                columnNumber: 19\n                                            }, this),\n                                            isSpeaking && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                variant: \"secondary\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_CheckCircle_Clock_Mic_MicOff_User_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"h-3 w-3 mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\interview\\\\external\\\\page.tsx\",\n                                                        lineNumber: 606,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    \"AI Speaking\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\interview\\\\external\\\\page.tsx\",\n                                                lineNumber: 605,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\interview\\\\external\\\\page.tsx\",\n                                        lineNumber: 593,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\interview\\\\external\\\\page.tsx\",\n                                lineNumber: 580,\n                                columnNumber: 13\n                            }, this),\n                            !isInterviewStarted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"mb-4\",\n                                        children: \"Ready to start your interview? Make sure you have a quiet environment and working microphone.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\interview\\\\external\\\\page.tsx\",\n                                        lineNumber: 616,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        onClick: startInterview,\n                                        size: \"lg\",\n                                        children: \"Start Interview\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\interview\\\\external\\\\page.tsx\",\n                                        lineNumber: 617,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\interview\\\\external\\\\page.tsx\",\n                                lineNumber: 615,\n                                columnNumber: 15\n                            }, this),\n                            isInterviewCompleted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_CheckCircle_Clock_Mic_MicOff_User_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"h-12 w-12 text-green-500 mx-auto mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\interview\\\\external\\\\page.tsx\",\n                                        lineNumber: 625,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold mb-2\",\n                                        children: \"Interview Completed!\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\interview\\\\external\\\\page.tsx\",\n                                        lineNumber: 626,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-muted-foreground\",\n                                        children: \"Thank you for your time. Your responses have been submitted for review.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\interview\\\\external\\\\page.tsx\",\n                                        lineNumber: 627,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\interview\\\\external\\\\page.tsx\",\n                                lineNumber: 624,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\interview\\\\external\\\\page.tsx\",\n                        lineNumber: 579,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\interview\\\\external\\\\page.tsx\",\n                    lineNumber: 578,\n                    columnNumber: 9\n                }, this),\n                conversationHistory.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                children: \"Interview Conversation\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\interview\\\\external\\\\page.tsx\",\n                                lineNumber: 639,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\interview\\\\external\\\\page.tsx\",\n                            lineNumber: 638,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4 max-h-96 overflow-y-auto\",\n                                children: conversationHistory.map((entry, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `flex ${entry.speaker === 'user' ? 'justify-end' : 'justify-start'}`,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: `max-w-[80%] p-3 rounded-lg ${entry.speaker === 'user' ? 'bg-blue-500 text-white' : 'bg-gray-100 text-gray-900'}`,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm\",\n                                                    children: entry.message\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\interview\\\\external\\\\page.tsx\",\n                                                    lineNumber: 654,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs opacity-70 mt-1\",\n                                                    children: entry.timestamp.toLocaleTimeString()\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\interview\\\\external\\\\page.tsx\",\n                                                    lineNumber: 655,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\interview\\\\external\\\\page.tsx\",\n                                            lineNumber: 648,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, index, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\interview\\\\external\\\\page.tsx\",\n                                        lineNumber: 644,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\interview\\\\external\\\\page.tsx\",\n                                lineNumber: 642,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\interview\\\\external\\\\page.tsx\",\n                            lineNumber: 641,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\interview\\\\external\\\\page.tsx\",\n                    lineNumber: 637,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\interview\\\\external\\\\page.tsx\",\n            lineNumber: 549,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\app\\\\interview\\\\external\\\\page.tsx\",\n        lineNumber: 548,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/interview/external/page.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/badge.tsx":
/*!*********************************!*\
  !*** ./components/ui/badge.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Badge: () => (/* binding */ Badge),\n/* harmony export */   badgeVariants: () => (/* binding */ badgeVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\n\nconst badgeVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\", {\n    variants: {\n        variant: {\n            default: \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n            secondary: \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            destructive: \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n            outline: \"text-foreground\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\"\n    }\n});\nfunction Badge({ className, variant, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(badgeVariants({\n            variant\n        }), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\components\\\\ui\\\\badge.tsx\",\n        lineNumber: 32,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/badge.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/button.tsx":
/*!**********************************!*\
  !*** ./components/ui/button.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n            outline: \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-10 px-4 py-2\",\n            sm: \"h-9 rounded-md px-3\",\n            lg: \"h-11 rounded-md px-8\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 46,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/card.tsx":
/*!********************************!*\
  !*** ./components/ui/card.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\nconst Card = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"rounded-lg border bg-card text-card-foreground shadow-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 9,\n        columnNumber: 3\n    }, undefined));\nCard.displayName = \"Card\";\nconst CardHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 p-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 24,\n        columnNumber: 3\n    }, undefined));\nCardHeader.displayName = \"CardHeader\";\nconst CardTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-2xl font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 36,\n        columnNumber: 3\n    }, undefined));\nCardTitle.displayName = \"CardTitle\";\nconst CardDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 51,\n        columnNumber: 3\n    }, undefined));\nCardDescription.displayName = \"CardDescription\";\nconst CardContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 63,\n        columnNumber: 3\n    }, undefined));\nCardContent.displayName = \"CardContent\";\nconst CardFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Voice-Agent-for-ITjobs\\\\kamikaz-interview-ai (1)\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 71,\n        columnNumber: 3\n    }, undefined));\nCardFooter.displayName = \"CardFooter\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/card.tsx\n");

/***/ }),

/***/ "(ssr)/./lib/gemini-voice.ts":
/*!*****************************!*\
  !*** ./lib/gemini-voice.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GeminiVoiceService: () => (/* binding */ GeminiVoiceService)\n/* harmony export */ });\n/* harmony import */ var _google_generative_ai__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @google/generative-ai */ \"(ssr)/../node_modules/@google/generative-ai/dist/index.mjs\");\n\nconst genAI = new _google_generative_ai__WEBPACK_IMPORTED_MODULE_0__.GoogleGenerativeAI(process.env.GOOGLE_GEMINI_API_KEY || \"AIzaSyAgK32ThjpPUNT_-RM5XiXFKPtgAT1vpZI\");\nclass GeminiVoiceService {\n    constructor(){\n        this.recognition = null;\n        this.currentUtterance = null;\n        this.isListening = false;\n        this.isSpeaking = false;\n        this.canBeInterrupted = true;\n        this.onInterruptCallback = null;\n        this.silenceTimer = null;\n        this.voiceActivityDetection = true;\n        this.model = genAI.getGenerativeModel({\n            model: \"gemini-1.5-flash\"\n        });\n        this.synthesis = window.speechSynthesis;\n        this.setupSpeechRecognition();\n    }\n    setupSpeechRecognition() {\n        if (\"webkitSpeechRecognition\" in window || \"SpeechRecognition\" in window) {\n            const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;\n            this.recognition = new SpeechRecognition();\n            this.recognition.continuous = true;\n            this.recognition.interimResults = true;\n            this.recognition.lang = \"en-US\";\n        }\n    }\n    async requestMicrophonePermission() {\n        try {\n            // Check if mediaDevices is supported\n            if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {\n                console.error(\"MediaDevices API not supported\");\n                return false;\n            }\n            // Check if we already have permission\n            if (navigator.permissions) {\n                try {\n                    const permission = await navigator.permissions.query({\n                        name: 'microphone'\n                    });\n                    if (permission.state === 'granted') {\n                        return true;\n                    }\n                    if (permission.state === 'denied') {\n                        console.error(\"Microphone permission was previously denied\");\n                        return false;\n                    }\n                } catch (permError) {\n                    console.log(\"Permission query not supported, proceeding with getUserMedia\");\n                }\n            }\n            // Request microphone access\n            const stream = await navigator.mediaDevices.getUserMedia({\n                audio: {\n                    echoCancellation: true,\n                    noiseSuppression: true,\n                    autoGainControl: true\n                }\n            });\n            // Stop the stream immediately, we just needed permission\n            stream.getTracks().forEach((track)=>track.stop());\n            return true;\n        } catch (error) {\n            console.error(\"Microphone permission error:\", error);\n            // Handle specific error types\n            if (error.name === 'NotFoundError' || error.name === 'DeviceNotFoundError') {\n                console.error(\"No microphone device found\");\n            } else if (error.name === 'NotAllowedError' || error.name === 'PermissionDeniedError') {\n                console.error(\"Microphone permission denied by user\");\n            } else if (error.name === 'NotSupportedError') {\n                console.error(\"Microphone not supported\");\n            }\n            return false;\n        }\n    }\n    setInterruptCallback(callback) {\n        this.onInterruptCallback = callback;\n    }\n    setCanBeInterrupted(canInterrupt) {\n        this.canBeInterrupted = canInterrupt;\n    }\n    enableVoiceActivityDetection(enabled) {\n        this.voiceActivityDetection = enabled;\n    }\n    async generateResponse(prompt, context = \"\") {\n        try {\n            const fullPrompt = context ? `${context}\\n\\nUser: ${prompt}` : prompt;\n            const result = await this.model.generateContent(fullPrompt);\n            const response = await result.response;\n            return response.text();\n        } catch (error) {\n            console.error(\"Error generating response:\", error);\n            return \"I apologize, but I'm having trouble processing your response right now. Could you please repeat that?\";\n        }\n    }\n    async generateConversationalResponse(prompt, context) {\n        try {\n            // Check if user is asking for help\n            if (this.isHelpRequest(prompt)) {\n                return this.generateHelpResponse(prompt, context);\n            }\n            const conversationalPrompt = `\n        You are a friendly, experienced technical interviewer having a natural conversation.\n\n        Context: ${JSON.stringify(context, null, 2)}\n        User said: \"${prompt}\"\n\n        IMPORTANT: Your response will be read aloud by text-to-speech, so:\n        - DO NOT use markdown formatting (**, *, #, etc.)\n        - DO NOT use special characters or symbols\n        - Write in plain, spoken English only\n        - Use natural speech patterns\n        - Keep technical terms simple and pronounceable\n\n        Respond naturally as a human interviewer would:\n        - Use conversational language, not formal/robotic speech\n        - Show genuine interest and engagement\n        - Ask follow-up questions naturally\n        - Give encouraging feedback\n        - Use \"I\", \"you\", \"we\" pronouns\n        - Keep responses concise (1-3 sentences max)\n        - Sound like you're having a real conversation\n\n        If the user gave a good answer, acknowledge it and either ask a follow-up or move to the next topic naturally.\n        If the answer needs improvement, guide them gently with hints.\n      `;\n            const result = await this.model.generateContent(conversationalPrompt);\n            const response = await result.response;\n            const cleanResponse = this.cleanTextForSpeech(response.text());\n            return cleanResponse;\n        } catch (error) {\n            console.error(\"Error generating conversational response:\", error);\n            return \"That's interesting! Can you tell me a bit more about that approach?\";\n        }\n    }\n    isHelpRequest(prompt) {\n        const helpKeywords = [\n            'help',\n            'hint',\n            'explain',\n            'clarify',\n            'confused',\n            'understand',\n            'what do you mean',\n            'can you help',\n            'i need help',\n            'stuck',\n            'don\\'t know',\n            'not sure',\n            'can you give me',\n            'guide me'\n        ];\n        const lowerPrompt = prompt.toLowerCase();\n        return helpKeywords.some((keyword)=>lowerPrompt.includes(keyword));\n    }\n    async generateHelpResponse(prompt, context) {\n        const helpPrompt = `\n      The user is asking for help during an interview. Be supportive and provide guidance.\n\n      Context: ${JSON.stringify(context, null, 2)}\n      User's help request: \"${prompt}\"\n\n      IMPORTANT: Your response will be read aloud by text-to-speech, so:\n      - DO NOT use markdown formatting (**, *, #, etc.)\n      - DO NOT use special characters or symbols\n      - Write in plain, spoken English only\n      - Use natural speech patterns\n\n      Provide helpful guidance:\n      - Give a hint or direction without giving away the full answer\n      - Be encouraging and supportive\n      - Ask a leading question to guide their thinking\n      - Keep it conversational and friendly\n      - Make them feel comfortable asking for help\n    `;\n        try {\n            const result = await this.model.generateContent(helpPrompt);\n            const response = await result.response;\n            return this.cleanTextForSpeech(response.text());\n        } catch (error) {\n            return \"Of course! Let me help you think through this. What part would you like me to clarify?\";\n        }\n    }\n    cleanTextForSpeech(text) {\n        return text// Remove markdown formatting\n        .replace(/\\*\\*(.*?)\\*\\*/g, '$1') // Remove **bold**\n        .replace(/\\*(.*?)\\*/g, '$1') // Remove *italic*\n        .replace(/#{1,6}\\s*(.*?)$/gm, '$1') // Remove # headers\n        .replace(/`{1,3}(.*?)`{1,3}/g, '$1') // Remove `code`\n        .replace(/\\[(.*?)\\]\\(.*?\\)/g, '$1') // Remove [links](url)\n        // Remove special characters that sound weird when spoken\n        .replace(/[_~`]/g, '') // Remove underscores, tildes, backticks\n        .replace(/\\n\\s*\\n/g, '. ') // Replace double newlines with periods\n        .replace(/\\n/g, ' ') // Replace single newlines with spaces\n        .replace(/\\s+/g, ' ') // Replace multiple spaces with single space\n        // Fix common speech issues\n        .replace(/e\\.g\\./g, 'for example').replace(/i\\.e\\./g, 'that is').replace(/etc\\./g, 'and so on').replace(/vs\\./g, 'versus').replace(/\\bAPI\\b/g, 'A P I').replace(/\\bURL\\b/g, 'U R L').replace(/\\bHTTP\\b/g, 'H T T P').replace(/\\bJSON\\b/g, 'J S O N').replace(/\\bSQL\\b/g, 'S Q L')// Clean up punctuation for better speech flow\n        .replace(/([.!?])\\s*([A-Z])/g, '$1 $2') // Ensure space after sentence endings\n        .replace(/:\\s*/g, ': ') // Ensure space after colons\n        .replace(/;\\s*/g, '; ') // Ensure space after semicolons\n        .trim();\n    }\n    async speak(text, canBeInterrupted = true) {\n        return new Promise((resolve, reject)=>{\n            try {\n                // Stop any ongoing speech\n                this.stopSpeaking();\n                this.currentUtterance = new SpeechSynthesisUtterance(text);\n                this.currentUtterance.rate = 0.9;\n                this.currentUtterance.pitch = 1;\n                this.currentUtterance.volume = 1;\n                this.canBeInterrupted = canBeInterrupted;\n                // Set a more generous timeout based on text length\n                const timeoutDuration = Math.max(text.length * 100, 10000) // At least 10 seconds\n                ;\n                const timeout = setTimeout(()=>{\n                    console.log(\"Speech timeout reached\");\n                    this.stopSpeaking();\n                    resolve();\n                }, timeoutDuration);\n                this.currentUtterance.onstart = ()=>{\n                    this.isSpeaking = true;\n                    console.log(\"Speech started\");\n                };\n                this.currentUtterance.onend = ()=>{\n                    console.log(\"Speech ended normally\");\n                    clearTimeout(timeout);\n                    this.isSpeaking = false;\n                    this.currentUtterance = null;\n                    resolve();\n                };\n                this.currentUtterance.onerror = (event)=>{\n                    console.log(\"Speech error:\", event.error);\n                    clearTimeout(timeout);\n                    this.isSpeaking = false;\n                    this.currentUtterance = null;\n                    // Don't treat \"interrupted\" as an error - it's normal when stopping speech\n                    if (event.error === \"interrupted\" || event.error === \"canceled\") {\n                        resolve();\n                    } else {\n                        reject(new Error(`Speech synthesis error: ${event.error}`));\n                    }\n                };\n                // Ensure synthesis is ready\n                if (this.synthesis.paused) {\n                    this.synthesis.resume();\n                }\n                this.synthesis.speak(this.currentUtterance);\n            } catch (error) {\n                console.error(\"Error in speak method:\", error);\n                this.isSpeaking = false;\n                this.currentUtterance = null;\n                reject(error);\n            }\n        });\n    }\n    interruptSpeech() {\n        if (this.isSpeaking && this.canBeInterrupted) {\n            this.stopSpeaking();\n            if (this.onInterruptCallback) {\n                this.onInterruptCallback();\n            }\n            return true;\n        }\n        return false;\n    }\n    stopSpeaking() {\n        if (this.isSpeaking && this.currentUtterance) {\n            this.synthesis.cancel();\n            this.isSpeaking = false;\n            this.currentUtterance = null;\n        }\n    }\n    async startListening() {\n        return new Promise(async (resolve, reject)=>{\n            if (!this.recognition) {\n                reject(new Error(\"Speech recognition not supported in this browser. Please use Chrome, Edge, or Safari.\"));\n                return;\n            }\n            if (this.isListening) {\n                reject(new Error(\"Already listening\"));\n                return;\n            }\n            // Stop any ongoing speech before listening\n            this.stopSpeaking();\n            // Wait a bit for speech to fully stop\n            setTimeout(()=>{\n                this.isListening = true;\n                let finalTranscript = \"\";\n                let interimTranscript = \"\";\n                this.recognition.onresult = (event)=>{\n                    let interim = \"\";\n                    let final = \"\";\n                    for(let i = event.resultIndex; i < event.results.length; i++){\n                        const transcript = event.results[i][0].transcript;\n                        if (event.results[i].isFinal) {\n                            final += transcript;\n                        } else {\n                            interim += transcript;\n                        }\n                    }\n                    finalTranscript += final;\n                    interimTranscript = interim;\n                    // Reset silence timer on speech\n                    if (this.silenceTimer) {\n                        clearTimeout(this.silenceTimer);\n                    }\n                    // If we have final results and voice activity detection is enabled\n                    if (final && this.voiceActivityDetection) {\n                        // Set a timer to detect end of speech\n                        this.silenceTimer = setTimeout(()=>{\n                            if (finalTranscript.trim()) {\n                                console.log(\"Speech recognized:\", finalTranscript);\n                                this.isListening = false;\n                                this.recognition.stop();\n                                resolve(finalTranscript);\n                            }\n                        }, 1500) // Wait 1.5 seconds of silence\n                        ;\n                    }\n                };\n                this.recognition.onerror = (event)=>{\n                    console.error(\"Speech recognition error:\", event.error);\n                    this.isListening = false;\n                    if (this.silenceTimer) {\n                        clearTimeout(this.silenceTimer);\n                    }\n                    let errorMessage = \"Speech recognition error occurred\";\n                    if (event.error === \"not-allowed\") {\n                        errorMessage = \"Microphone access denied. Please click the microphone icon in your browser's address bar and allow access, then try again.\";\n                    } else if (event.error === \"no-speech\") {\n                        errorMessage = \"No speech detected. Please speak clearly and try again.\";\n                    } else if (event.error === \"network\") {\n                        errorMessage = \"Network error. Please check your internet connection and try again.\";\n                    } else if (event.error === \"service-not-allowed\") {\n                        errorMessage = \"Speech recognition service not allowed. Please check your browser settings.\";\n                    } else if (event.error === \"bad-grammar\") {\n                        errorMessage = \"Speech recognition grammar error. Please try speaking again.\";\n                    }\n                    reject(new Error(errorMessage));\n                };\n                this.recognition.onend = ()=>{\n                    this.isListening = false;\n                    if (this.silenceTimer) {\n                        clearTimeout(this.silenceTimer);\n                    }\n                    // If we have a transcript, resolve with it\n                    if (finalTranscript.trim()) {\n                        resolve(finalTranscript);\n                    }\n                };\n                try {\n                    this.recognition.start();\n                } catch (error) {\n                    this.isListening = false;\n                    console.error(\"Error starting speech recognition:\", error);\n                    reject(new Error(\"Failed to start speech recognition. Please ensure your microphone is connected and try again.\"));\n                }\n            }, 500) // Wait 500ms for speech to stop\n            ;\n        });\n    }\n    stopListening() {\n        if (this.recognition && this.isListening) {\n            this.recognition.stop();\n            this.isListening = false;\n        }\n    }\n    getIsListening() {\n        return this.isListening;\n    }\n    getIsSpeaking() {\n        return this.isSpeaking;\n    }\n    cleanup() {\n        this.stopSpeaking();\n        this.stopListening();\n        if (this.silenceTimer) {\n            clearTimeout(this.silenceTimer);\n        }\n    }\n    // Utility methods for better conversation flow\n    async startConversationalListening(onPartialTranscript) {\n        return new Promise(async (resolve, reject)=>{\n            if (!this.recognition) {\n                reject(new Error(\"Speech recognition not supported in this browser. Please use Chrome, Edge, or Safari.\"));\n                return;\n            }\n            if (this.isListening) {\n                this.stopListening();\n            }\n            this.stopSpeaking();\n            setTimeout(()=>{\n                this.isListening = true;\n                let finalTranscript = \"\";\n                this.recognition.onresult = (event)=>{\n                    let interim = \"\";\n                    let final = \"\";\n                    for(let i = event.resultIndex; i < event.results.length; i++){\n                        const transcript = event.results[i][0].transcript;\n                        if (event.results[i].isFinal) {\n                            final += transcript;\n                        } else {\n                            interim += transcript;\n                        }\n                    }\n                    finalTranscript += final;\n                    // Call partial transcript callback for real-time feedback\n                    if (onPartialTranscript && (final || interim)) {\n                        onPartialTranscript(finalTranscript + interim);\n                    }\n                    if (final) {\n                        if (this.silenceTimer) {\n                            clearTimeout(this.silenceTimer);\n                        }\n                        this.silenceTimer = setTimeout(()=>{\n                            if (finalTranscript.trim()) {\n                                this.isListening = false;\n                                this.recognition.stop();\n                                resolve(finalTranscript.trim());\n                            }\n                        }, 2000) // 2 seconds of silence to end\n                        ;\n                    }\n                };\n                this.recognition.onerror = (event)=>{\n                    console.error(\"Speech recognition error:\", event.error);\n                    this.isListening = false;\n                    if (this.silenceTimer) {\n                        clearTimeout(this.silenceTimer);\n                    }\n                    let errorMessage = \"Speech recognition error occurred\";\n                    if (event.error === \"not-allowed\") {\n                        errorMessage = \"Microphone access denied. Please click the microphone icon in your browser's address bar and allow access, then try again.\";\n                    } else if (event.error === \"no-speech\") {\n                        errorMessage = \"No speech detected. Please speak clearly and try again.\";\n                    } else if (event.error === \"network\") {\n                        errorMessage = \"Network error. Please check your internet connection and try again.\";\n                    } else if (event.error === \"service-not-allowed\") {\n                        errorMessage = \"Speech recognition service not allowed. Please check your browser settings.\";\n                    }\n                    reject(new Error(errorMessage));\n                };\n                this.recognition.onend = ()=>{\n                    this.isListening = false;\n                    if (this.silenceTimer) {\n                        clearTimeout(this.silenceTimer);\n                    }\n                    if (finalTranscript.trim()) {\n                        resolve(finalTranscript.trim());\n                    }\n                };\n                try {\n                    this.recognition.start();\n                } catch (error) {\n                    this.isListening = false;\n                    console.error(\"Error starting conversational listening:\", error);\n                    reject(new Error(\"Failed to start speech recognition. Please ensure your microphone is connected and try again.\"));\n                }\n            }, 300);\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/gemini-voice.ts\n");

/***/ }),

/***/ "(ssr)/./lib/utils.ts":
/*!**********************!*\
  !*** ./lib/utils.ts ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9saWIvdXRpbHMudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQTRDO0FBQ0o7QUFFakMsU0FBU0UsR0FBRyxHQUFHQyxNQUFvQjtJQUN4QyxPQUFPRix1REFBT0EsQ0FBQ0QsMENBQUlBLENBQUNHO0FBQ3RCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGhwXFxEZXNrdG9wXFxWb2ljZS1BZ2VudC1mb3ItSVRqb2JzXFxrYW1pa2F6LWludGVydmlldy1haSAoMSlcXGxpYlxcdXRpbHMudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY2xzeCwgdHlwZSBDbGFzc1ZhbHVlIH0gZnJvbSBcImNsc3hcIlxuaW1wb3J0IHsgdHdNZXJnZSB9IGZyb20gXCJ0YWlsd2luZC1tZXJnZVwiXG5cbmV4cG9ydCBmdW5jdGlvbiBjbiguLi5pbnB1dHM6IENsYXNzVmFsdWVbXSkge1xuICByZXR1cm4gdHdNZXJnZShjbHN4KGlucHV0cykpXG59XG4iXSwibmFtZXMiOlsiY2xzeCIsInR3TWVyZ2UiLCJjbiIsImlucHV0cyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./lib/utils.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5Ckamikaz-interview-ai%20(1)%5C%5Capp%5C%5Cinterview%5C%5Cexternal%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5Ckamikaz-interview-ai%20(1)%5C%5Capp%5C%5Cinterview%5C%5Cexternal%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/interview/external/page.tsx */ \"(ssr)/./app/interview/external/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2hwJTVDJTVDRGVza3RvcCU1QyU1Q1ZvaWNlLUFnZW50LWZvci1JVGpvYnMlNUMlNUNrYW1pa2F6LWludGVydmlldy1haSUyMCgxKSU1QyU1Q2FwcCU1QyU1Q2ludGVydmlldyU1QyU1Q2V4dGVybmFsJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLDhLQUFpSiIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcaHBcXFxcRGVza3RvcFxcXFxWb2ljZS1BZ2VudC1mb3ItSVRqb2JzXFxcXGthbWlrYXotaW50ZXJ2aWV3LWFpICgxKVxcXFxhcHBcXFxcaW50ZXJ2aWV3XFxcXGV4dGVybmFsXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5Ckamikaz-interview-ai%20(1)%5C%5Capp%5C%5Cinterview%5C%5Cexternal%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5Ckamikaz-interview-ai%20(1)%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5Ckamikaz-interview-ai%20(1)%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5Ckamikaz-interview-ai%20(1)%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5Ckamikaz-interview-ai%20(1)%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5Ckamikaz-interview-ai%20(1)%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5Ckamikaz-interview-ai%20(1)%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5Ckamikaz-interview-ai%20(1)%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5Ckamikaz-interview-ai%20(1)%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5Ckamikaz-interview-ai%20(1)%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5Ckamikaz-interview-ai%20(1)%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5Ckamikaz-interview-ai%20(1)%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5Ckamikaz-interview-ai%20(1)%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5Ckamikaz-interview-ai%20(1)%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5Ckamikaz-interview-ai%20(1)%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5Ckamikaz-interview-ai%20(1)%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5Ckamikaz-interview-ai%20(1)%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5Ckamikaz-interview-ai%20(1)%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5Ckamikaz-interview-ai%20(1)%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5Ckamikaz-interview-ai%20(1)%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5Ckamikaz-interview-ai%20(1)%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5Ckamikaz-interview-ai%20(1)%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5Ckamikaz-interview-ai%20(1)%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5Ckamikaz-interview-ai%20(1)%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5Ckamikaz-interview-ai%20(1)%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5Ckamikaz-interview-ai%20(1)%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5Ckamikaz-interview-ai%20(1)%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5Ckamikaz-interview-ai%20(1)%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CVoice-Agent-for-ITjobs%5C%5Ckamikaz-interview-ai%20(1)%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/lucide-react","vendor-chunks/@swc","vendor-chunks/@radix-ui","vendor-chunks/@google","vendor-chunks/tailwind-merge","vendor-chunks/clsx","vendor-chunks/class-variance-authority"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Finterview%2Fexternal%2Fpage&page=%2Finterview%2Fexternal%2Fpage&appPaths=%2Finterview%2Fexternal%2Fpage&pagePath=private-next-app-dir%2Finterview%2Fexternal%2Fpage.tsx&appDir=C%3A%5CUsers%5Chp%5CDesktop%5CVoice-Agent-for-ITjobs%5Ckamikaz-interview-ai%20(1)%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Chp%5CDesktop%5CVoice-Agent-for-ITjobs%5Ckamikaz-interview-ai%20(1)&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();