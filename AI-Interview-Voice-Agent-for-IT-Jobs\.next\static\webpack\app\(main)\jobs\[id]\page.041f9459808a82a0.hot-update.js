"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(main)/jobs/[id]/page",{

/***/ "(app-pages-browser)/./lib/integration-client.js":
/*!***********************************!*\
  !*** ./lib/integration-client.js ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   IntegrationClient: () => (/* binding */ IntegrationClient),\n/* harmony export */   integrationClient: () => (/* binding */ integrationClient)\n/* harmony export */ });\n/* harmony import */ var _shared_integration_config_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../shared/integration-config.js */ \"(app-pages-browser)/../shared/integration-config.js\");\n/* harmony import */ var _services_supabaseClient_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../services/supabaseClient.js */ \"(app-pages-browser)/./services/supabaseClient.js\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n// Integration Client for Voice Agent Communication\n// This utility handles communication between the main platform and voice agent\n\n\nclass IntegrationClient {\n    // Create headers for API requests\n    createHeaders() {\n        return {\n            'Content-Type': 'application/json',\n            [_shared_integration_config_js__WEBPACK_IMPORTED_MODULE_0__.INTEGRATION_CONFIG.API_KEY_HEADER]: this.apiKey\n        };\n    }\n    // Make API request with retry logic\n    async makeRequest(url) {\n        let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n        const requestOptions = {\n            ...options,\n            headers: {\n                ...this.createHeaders(),\n                ...options.headers\n            },\n            signal: AbortSignal.timeout(_shared_integration_config_js__WEBPACK_IMPORTED_MODULE_0__.INTEGRATION_CONFIG.REQUEST_TIMEOUT)\n        };\n        for(let attempt = 0; attempt <= this.maxRetries; attempt++){\n            try {\n                const response = await fetch(url, requestOptions);\n                if (response.ok) {\n                    return await response.json();\n                }\n                // If it's a client error (4xx), don't retry\n                if (response.status >= 400 && response.status < 500) {\n                    var _errorData_error;\n                    const errorData = await response.json().catch(()=>({}));\n                    throw new Error(\"Client error \".concat(response.status, \": \").concat(((_errorData_error = errorData.error) === null || _errorData_error === void 0 ? void 0 : _errorData_error.message) || 'Unknown error'));\n                }\n                // For server errors (5xx), retry\n                if (attempt < this.maxRetries) {\n                    await this.delay(_shared_integration_config_js__WEBPACK_IMPORTED_MODULE_0__.INTEGRATION_CONFIG.RETRY_DELAY * (attempt + 1));\n                    continue;\n                }\n                throw new Error(\"Server error \".concat(response.status, \" after \").concat(this.maxRetries, \" retries\"));\n            } catch (error) {\n                var _error_name;\n                if (attempt < this.maxRetries && !((_error_name = error.name) === null || _error_name === void 0 ? void 0 : _error_name.includes('AbortError'))) {\n                    await this.delay(_shared_integration_config_js__WEBPACK_IMPORTED_MODULE_0__.INTEGRATION_CONFIG.RETRY_DELAY * (attempt + 1));\n                    continue;\n                }\n                throw error;\n            }\n        }\n    }\n    // Delay utility for retries\n    delay(ms) {\n        return new Promise((resolve)=>setTimeout(resolve, ms));\n    }\n    // Create interview session in voice agent\n    async createInterviewSession(sessionData) {\n        try {\n            (0,_shared_integration_config_js__WEBPACK_IMPORTED_MODULE_0__.logIntegrationEvent)('info', 'main_platform', 'creating_voice_agent_session', 'Creating interview session in voice agent', sessionData.sessionId);\n            const url = \"\".concat(_shared_integration_config_js__WEBPACK_IMPORTED_MODULE_0__.INTEGRATION_CONFIG.VOICE_AGENT_URL, \"/api/integration/interview/start\");\n            const response = await this.makeRequest(url, {\n                method: 'POST',\n                body: JSON.stringify(sessionData)\n            });\n            if (!response.success) {\n                var _response_error;\n                throw new Error(((_response_error = response.error) === null || _response_error === void 0 ? void 0 : _response_error.message) || 'Failed to create session in voice agent');\n            }\n            (0,_shared_integration_config_js__WEBPACK_IMPORTED_MODULE_0__.logIntegrationEvent)('info', 'main_platform', 'voice_agent_session_created', 'Interview session created in voice agent', sessionData.sessionId, {\n                voiceAgentInterviewId: response.data.voiceAgentInterviewId\n            });\n            return response.data;\n        } catch (error) {\n            (0,_shared_integration_config_js__WEBPACK_IMPORTED_MODULE_0__.logIntegrationEvent)('error', 'main_platform', 'voice_agent_session_failed', 'Failed to create session in voice agent', sessionData.sessionId, {\n                error: error.message\n            });\n            throw error;\n        }\n    }\n    // Get interview status from voice agent\n    async getInterviewStatus(sessionId) {\n        let voiceAgentInterviewId = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : null;\n        try {\n            const params = new URLSearchParams();\n            if (sessionId) params.append('sessionId', sessionId);\n            if (voiceAgentInterviewId) params.append('voiceAgentInterviewId', voiceAgentInterviewId);\n            const url = \"\".concat(_shared_integration_config_js__WEBPACK_IMPORTED_MODULE_0__.INTEGRATION_CONFIG.VOICE_AGENT_URL, \"/api/integration/interview/start?\").concat(params);\n            const response = await this.makeRequest(url, {\n                method: 'GET'\n            });\n            if (!response.success) {\n                var _response_error;\n                throw new Error(((_response_error = response.error) === null || _response_error === void 0 ? void 0 : _response_error.message) || 'Failed to get interview status');\n            }\n            return response.data;\n        } catch (error) {\n            (0,_shared_integration_config_js__WEBPACK_IMPORTED_MODULE_0__.logIntegrationEvent)('error', 'main_platform', 'get_status_failed', 'Failed to get interview status from voice agent', sessionId, {\n                error: error.message\n            });\n            throw error;\n        }\n    }\n    // Check voice agent health\n    async checkVoiceAgentHealth() {\n        try {\n            const url = \"\".concat(_shared_integration_config_js__WEBPACK_IMPORTED_MODULE_0__.INTEGRATION_CONFIG.VOICE_AGENT_URL, \"/api/integration/health\");\n            const response = await this.makeRequest(url, {\n                method: 'GET'\n            });\n            return response.data;\n        } catch (error) {\n            (0,_shared_integration_config_js__WEBPACK_IMPORTED_MODULE_0__.logIntegrationEvent)('warning', 'main_platform', 'voice_agent_health_check_failed', 'Voice agent health check failed', null, {\n                error: error.message\n            });\n            return {\n                status: 'unhealthy',\n                error: error.message,\n                timestamp: new Date().toISOString()\n            };\n        }\n    }\n    // Generate interview questions\n    async generateQuestions(jobData) {\n        try {\n            (0,_shared_integration_config_js__WEBPACK_IMPORTED_MODULE_0__.logIntegrationEvent)('info', 'main_platform', 'generating_questions', 'Generating interview questions', null, {\n                jobTitle: jobData.jobTitle\n            });\n            const response = await fetch('/api/generate-questions', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    jobTitle: jobData.jobTitle,\n                    jobDescription: jobData.jobDescription,\n                    requiredSkills: jobData.requiredSkills,\n                    experienceLevel: jobData.experienceLevel,\n                    questionCount: jobData.questionCount || _shared_integration_config_js__WEBPACK_IMPORTED_MODULE_0__.INTEGRATION_CONFIG.DEFAULT_QUESTION_COUNT\n                })\n            });\n            const questionsData = await response.json();\n            if (!response.ok) {\n                throw new Error(questionsData.error || 'Failed to generate questions');\n            }\n            // Format questions for voice agent\n            const formattedQuestions = (questionsData.questions || []).map((q, index)=>({\n                    id: \"q_\".concat(index + 1),\n                    question: q,\n                    type: 'technical',\n                    difficulty: 'medium',\n                    expectedDuration: 120,\n                    followUpAllowed: true,\n                    metadata: {\n                        generated: true,\n                        jobTitle: jobData.jobTitle\n                    }\n                }));\n            (0,_shared_integration_config_js__WEBPACK_IMPORTED_MODULE_0__.logIntegrationEvent)('info', 'main_platform', 'questions_generated', 'Interview questions generated successfully', null, {\n                questionCount: formattedQuestions.length\n            });\n            return formattedQuestions;\n        } catch (error) {\n            (0,_shared_integration_config_js__WEBPACK_IMPORTED_MODULE_0__.logIntegrationEvent)('error', 'main_platform', 'question_generation_failed', 'Failed to generate interview questions', null, {\n                error: error.message\n            });\n            // Return default questions as fallback\n            return [\n                {\n                    id: 'q_default_1',\n                    question: 'Tell me about yourself and your experience.',\n                    type: 'behavioral',\n                    difficulty: 'easy',\n                    expectedDuration: 120,\n                    followUpAllowed: true,\n                    metadata: {\n                        fallback: true\n                    }\n                },\n                {\n                    id: 'q_default_2',\n                    question: \"What interests you about the \".concat(jobData.jobTitle, \" position?\"),\n                    type: 'behavioral',\n                    difficulty: 'easy',\n                    expectedDuration: 120,\n                    followUpAllowed: true,\n                    metadata: {\n                        fallback: true\n                    }\n                }\n            ];\n        }\n    }\n    // Start interview process (creates session and redirects to voice agent)\n    async startInterview(interviewData) {\n        try {\n            const sessionId = \"session_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substr(2, 9));\n            (0,_shared_integration_config_js__WEBPACK_IMPORTED_MODULE_0__.logIntegrationEvent)('info', 'main_platform', 'starting_interview_process', 'Starting integrated interview process', sessionId, {\n                userEmail: interviewData.userEmail,\n                jobTitle: interviewData.jobTitle\n            });\n            // Generate questions if not provided\n            let questions = interviewData.questions;\n            if (!questions || questions.length === 0) {\n                questions = await this.generateQuestions({\n                    jobTitle: interviewData.jobTitle,\n                    jobDescription: interviewData.jobDescription,\n                    requiredSkills: interviewData.requiredSkills,\n                    experienceLevel: interviewData.experienceLevel,\n                    questionCount: interviewData.questionCount\n                });\n            }\n            // Prepare session data for voice agent\n            const sessionData = {\n                sessionId,\n                userEmail: interviewData.userEmail,\n                userName: interviewData.userName,\n                jobTitle: interviewData.jobTitle,\n                jobDescription: interviewData.jobDescription,\n                companyName: interviewData.companyName,\n                interviewType: interviewData.interviewType || 'job_application',\n                duration: interviewData.duration || _shared_integration_config_js__WEBPACK_IMPORTED_MODULE_0__.INTEGRATION_CONFIG.DEFAULT_INTERVIEW_DURATION,\n                experienceLevel: interviewData.experienceLevel,\n                requiredSkills: interviewData.requiredSkills,\n                companyCriteria: interviewData.companyCriteria,\n                questions,\n                questionCount: questions.length,\n                callbackUrl: \"\".concat(_shared_integration_config_js__WEBPACK_IMPORTED_MODULE_0__.INTEGRATION_CONFIG.MAIN_PLATFORM_URL, \"/api/integration/interview/feedback\"),\n                webhookSecret: process.env.INTEGRATION_SHARED_SECRET || 'webhook-secret'\n            };\n            // Create session in main platform database\n            // Using the existing supabase client from services\n            const { data: session, error: sessionError } = await _services_supabaseClient_js__WEBPACK_IMPORTED_MODULE_1__.supabase.from('Interview_Sessions').insert([\n                {\n                    main_interview_id: sessionId,\n                    job_id: interviewData.jobId || null,\n                    company_id: interviewData.companyId || null,\n                    user_email: interviewData.userEmail,\n                    user_name: interviewData.userName,\n                    job_title: interviewData.jobTitle,\n                    company_name: interviewData.companyName,\n                    interview_type: sessionData.interviewType,\n                    status: 'pending',\n                    session_data: {\n                        jobDescription: interviewData.jobDescription,\n                        requiredSkills: interviewData.requiredSkills,\n                        experienceLevel: interviewData.experienceLevel,\n                        companyCriteria: interviewData.companyCriteria,\n                        duration: sessionData.duration,\n                        questionCount: questions.length,\n                        questions: questions,\n                        voiceAgentUrl: \"\".concat(_shared_integration_config_js__WEBPACK_IMPORTED_MODULE_0__.INTEGRATION_CONFIG.VOICE_AGENT_URL, \"/interview/external?sessionId=\").concat(sessionId),\n                        integrationVersion: _shared_integration_config_js__WEBPACK_IMPORTED_MODULE_0__.INTEGRATION_CONFIG.INTEGRATION_VERSION\n                    }\n                }\n            ]).select().single();\n            if (sessionError) {\n                console.error('Session creation error:', sessionError);\n                const errorMessage = sessionError.message || sessionError.details || JSON.stringify(sessionError);\n                throw new Error(\"Failed to create session: \".concat(errorMessage));\n            }\n            // Create session in voice agent\n            const voiceAgentResponse = await this.createInterviewSession(sessionData);\n            // Update session with voice agent details\n            await _services_supabaseClient_js__WEBPACK_IMPORTED_MODULE_1__.supabase.from('Interview_Sessions').update({\n                voice_agent_interview_id: voiceAgentResponse.voiceAgentInterviewId,\n                status: 'ready'\n            }).eq('id', session.id);\n            (0,_shared_integration_config_js__WEBPACK_IMPORTED_MODULE_0__.logIntegrationEvent)('info', 'main_platform', 'interview_process_ready', 'Interview process ready, redirecting to voice agent', sessionId, {\n                voiceAgentUrl: session.voice_agent_url\n            });\n            return {\n                sessionId,\n                voiceAgentUrl: session.voice_agent_url,\n                voiceAgentInterviewId: voiceAgentResponse.voiceAgentInterviewId,\n                estimatedDuration: sessionData.duration,\n                questionCount: questions.length\n            };\n        } catch (error) {\n            (0,_shared_integration_config_js__WEBPACK_IMPORTED_MODULE_0__.logIntegrationEvent)('error', 'main_platform', 'interview_start_failed', 'Failed to start interview process', null, {\n                error: error.message\n            });\n            throw error;\n        }\n    }\n    // Get feedback status\n    async getFeedbackStatus(sessionId) {\n        try {\n            // Using the existing supabase client from services\n            const { data: feedback, error } = await _services_supabaseClient_js__WEBPACK_IMPORTED_MODULE_1__.supabase.from('Interview_Feedback_Bridge').select('*').eq('main_interview_id', sessionId).single();\n            if (error && error.code !== 'PGRST116') {\n                throw new Error(\"Database error: \".concat(error.message));\n            }\n            return feedback;\n        } catch (error) {\n            (0,_shared_integration_config_js__WEBPACK_IMPORTED_MODULE_0__.logIntegrationEvent)('error', 'main_platform', 'get_feedback_status_failed', 'Failed to get feedback status', sessionId, {\n                error: error.message\n            });\n            throw error;\n        }\n    }\n    constructor(){\n        this.apiKey = process.env.INTEGRATION_API_KEY || 'main-platform-api-key';\n        this.retryCount = 0;\n        this.maxRetries = _shared_integration_config_js__WEBPACK_IMPORTED_MODULE_0__.INTEGRATION_CONFIG.MAX_RETRIES;\n    }\n}\n// Export singleton instance\nconst integrationClient = new IntegrationClient();\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/integration-client.js\n"));

/***/ })

});